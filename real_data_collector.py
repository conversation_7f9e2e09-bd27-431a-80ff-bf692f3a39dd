#!/usr/bin/env python3
"""
📊 REAL CRYPTO & FINANCE DATA COLLECTOR
Collects actual market data from free APIs for AI training
"""

import asyncio
import aiohttp
import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DataCollector")

class RealDataCollector:
    """Collects real crypto and finance data from free APIs"""
    
    def __init__(self):
        self.db_path = "real_market_data.db"
        self.session = None
        self._initialize_database()
        
        # Free API endpoints (no keys required)
        self.apis = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'binance': 'https://api.binance.com/api/v3',
            'coinbase': 'https://api.exchange.coinbase.com',
            'kraken': 'https://api.kraken.com/0/public'
        }
        
        # Major crypto pairs to track
        self.crypto_pairs = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
            'LINKUSDT', 'AVAXUSDT', 'MATICUSDT', 'UNIUSDT', 'LTCUSDT'
        ]

    def _initialize_database(self):
        """Initialize SQLite database for real market data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Price data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS price_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                price REAL,
                volume REAL,
                high_24h REAL,
                low_24h REAL,
                change_24h REAL,
                market_cap REAL,
                source TEXT
            )
        ''')
        
        # Order book data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orderbook_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                bids TEXT,
                asks TEXT,
                spread REAL,
                source TEXT
            )
        ''')
        
        # News/sentiment data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS news_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                title TEXT,
                content TEXT,
                sentiment_score REAL,
                source TEXT,
                symbols TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"📊 Database initialized: {self.db_path}")

    async def collect_coingecko_data(self):
        """Collect data from CoinGecko API (free, no key required)"""
        try:
            # Get top 100 coins with market data
            url = f"{self.apis['coingecko']}/coins/markets"
            params = {
                'vs_currency': 'usd',
                'order': 'market_cap_desc',
                'per_page': 100,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '24h'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    for coin in data:
                        cursor.execute('''
                            INSERT INTO price_data 
                            (timestamp, symbol, price, volume, high_24h, low_24h, change_24h, market_cap, source)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            datetime.now().isoformat(),
                            coin['symbol'].upper() + 'USD',
                            coin['current_price'],
                            coin['total_volume'],
                            coin['high_24h'],
                            coin['low_24h'],
                            coin['price_change_percentage_24h'],
                            coin['market_cap'],
                            'coingecko'
                        ))
                    
                    conn.commit()
                    conn.close()
                    logger.info(f"✅ Collected {len(data)} coins from CoinGecko")
                    return len(data)
                    
        except Exception as e:
            logger.error(f"❌ CoinGecko error: {e}")
            return 0

    async def collect_binance_data(self):
        """Collect data from Binance API (free, no key required)"""
        try:
            # Get 24hr ticker statistics
            url = f"{self.apis['binance']}/ticker/24hr"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    count = 0
                    for ticker in data:
                        if ticker['symbol'] in self.crypto_pairs:
                            cursor.execute('''
                                INSERT INTO price_data 
                                (timestamp, symbol, price, volume, high_24h, low_24h, change_24h, market_cap, source)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                datetime.now().isoformat(),
                                ticker['symbol'],
                                float(ticker['lastPrice']),
                                float(ticker['volume']),
                                float(ticker['highPrice']),
                                float(ticker['lowPrice']),
                                float(ticker['priceChangePercent']),
                                0,  # Market cap not available
                                'binance'
                            ))
                            count += 1
                    
                    conn.commit()
                    conn.close()
                    logger.info(f"✅ Collected {count} pairs from Binance")
                    return count
                    
        except Exception as e:
            logger.error(f"❌ Binance error: {e}")
            return 0

    async def collect_orderbook_data(self, symbol='BTCUSDT'):
        """Collect real order book data"""
        try:
            url = f"{self.apis['binance']}/depth"
            params = {'symbol': symbol, 'limit': 100}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Calculate spread
                    best_bid = float(data['bids'][0][0]) if data['bids'] else 0
                    best_ask = float(data['asks'][0][0]) if data['asks'] else 0
                    spread = best_ask - best_bid if best_bid and best_ask else 0
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO orderbook_data 
                        (timestamp, symbol, bids, asks, spread, source)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        datetime.now().isoformat(),
                        symbol,
                        json.dumps(data['bids'][:10]),  # Top 10 bids
                        json.dumps(data['asks'][:10]),  # Top 10 asks
                        spread,
                        'binance'
                    ))
                    
                    conn.commit()
                    conn.close()
                    logger.info(f"✅ Collected order book for {symbol}")
                    return True
                    
        except Exception as e:
            logger.error(f"❌ Order book error: {e}")
            return False

    async def collect_historical_data(self, symbol='BTCUSDT', days=30):
        """Collect historical price data"""
        try:
            url = f"{self.apis['binance']}/klines"
            params = {
                'symbol': symbol,
                'interval': '1h',  # 1 hour intervals
                'limit': days * 24  # days * 24 hours
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    for kline in data:
                        timestamp = datetime.fromtimestamp(kline[0] / 1000).isoformat()
                        open_price = float(kline[1])
                        high_price = float(kline[2])
                        low_price = float(kline[3])
                        close_price = float(kline[4])
                        volume = float(kline[5])
                        
                        cursor.execute('''
                            INSERT INTO price_data 
                            (timestamp, symbol, price, volume, high_24h, low_24h, change_24h, market_cap, source)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            timestamp,
                            symbol,
                            close_price,
                            volume,
                            high_price,
                            low_price,
                            ((close_price - open_price) / open_price) * 100,
                            0,
                            'binance_historical'
                        ))
                    
                    conn.commit()
                    conn.close()
                    logger.info(f"✅ Collected {len(data)} historical records for {symbol}")
                    return len(data)
                    
        except Exception as e:
            logger.error(f"❌ Historical data error: {e}")
            return 0

    def get_data_summary(self):
        """Get summary of collected data"""
        conn = sqlite3.connect(self.db_path)
        
        # Price data summary
        price_summary = pd.read_sql_query('''
            SELECT source, COUNT(*) as records, COUNT(DISTINCT symbol) as symbols,
                   MIN(timestamp) as earliest, MAX(timestamp) as latest
            FROM price_data 
            GROUP BY source
        ''', conn)
        
        # Order book summary
        orderbook_summary = pd.read_sql_query('''
            SELECT source, COUNT(*) as records, COUNT(DISTINCT symbol) as symbols
            FROM orderbook_data 
            GROUP BY source
        ''', conn)
        
        conn.close()
        
        return {
            'price_data': price_summary.to_dict('records'),
            'orderbook_data': orderbook_summary.to_dict('records')
        }

    async def run_data_collection(self, duration_minutes=60):
        """Run continuous data collection"""
        logger.info(f"🚀 Starting real data collection for {duration_minutes} minutes")
        
        self.session = aiohttp.ClientSession()
        
        try:
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            
            # Collect historical data first
            logger.info("📊 Collecting historical data...")
            for symbol in self.crypto_pairs[:3]:  # Top 3 pairs
                await self.collect_historical_data(symbol, days=7)
                await asyncio.sleep(1)  # Rate limiting
            
            # Continuous real-time collection
            iteration = 0
            while datetime.now() < end_time:
                iteration += 1
                logger.info(f"🔄 Collection iteration {iteration}")
                
                # Collect current market data
                coingecko_count = await self.collect_coingecko_data()
                await asyncio.sleep(2)  # Rate limiting
                
                binance_count = await self.collect_binance_data()
                await asyncio.sleep(2)
                
                # Collect order book for top pairs
                for symbol in self.crypto_pairs[:2]:
                    await self.collect_orderbook_data(symbol)
                    await asyncio.sleep(1)
                
                logger.info(f"✅ Iteration {iteration}: {coingecko_count + binance_count} records collected")
                
                # Wait before next collection
                await asyncio.sleep(60)  # Collect every minute
                
        finally:
            await self.session.close()
        
        # Final summary
        summary = self.get_data_summary()
        logger.info("📊 Data collection completed!")
        logger.info(f"Price data sources: {summary['price_data']}")
        logger.info(f"Order book sources: {summary['orderbook_data']}")
        
        return summary

async def main():
    """Main data collection function"""
    collector = RealDataCollector()
    
    print("📊 REAL CRYPTO & FINANCE DATA COLLECTOR")
    print("=" * 50)
    print("Collecting actual market data from free APIs...")
    
    # Run data collection for 5 minutes
    summary = await collector.run_data_collection(duration_minutes=5)
    
    print("\n✅ Data collection completed!")
    print(f"Database: {collector.db_path}")
    print("Summary:", json.dumps(summary, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
