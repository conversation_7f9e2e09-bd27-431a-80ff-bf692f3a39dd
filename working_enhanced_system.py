#!/usr/bin/env python3
"""
🚀 WORKING ENHANCED SYSTEM
Complete enhanced trading system without external dependencies
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import RobustScaler
from sklearn.feature_selection import RFE
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import train_test_split

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WorkingEnhancedSystem")

@dataclass
class EnhancedPrediction:
    symbol: str
    prediction: float
    confidence: float
    signal: str
    strength: str
    reasoning: str
    timestamp: str

class WorkingEnhancedSystem:
    """Complete working enhanced trading system"""
    
    def __init__(self):
        self.db_path = "working_enhanced_system.db"
        self.session = None
        self.models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self._initialize_database()
        
        # Crypto pairs to track
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']

    def _initialize_database(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Market data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume REAL,
                source TEXT
            )
        ''')
        
        # Enhanced features table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                features_json TEXT,
                target_return REAL
            )
        ''')
        
        # Predictions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                prediction REAL,
                confidence REAL,
                signal TEXT,
                model_used TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Database initialized: {self.db_path}")

    async def collect_real_data(self):
        """Collect real market data from Binance API"""
        try:
            url = "https://api.binance.com/api/v3/ticker/24hr"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    records_added = 0
                    for ticker in data:
                        if ticker['symbol'] in self.crypto_pairs:
                            cursor.execute('''
                                INSERT INTO market_data 
                                (timestamp, symbol, open_price, high_price, low_price, close_price, volume, source)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                datetime.now().isoformat(),
                                ticker['symbol'],
                                float(ticker['openPrice']),
                                float(ticker['highPrice']),
                                float(ticker['lowPrice']),
                                float(ticker['lastPrice']),
                                float(ticker['volume']),
                                'binance_24hr'
                            ))
                            records_added += 1
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Collected {records_added} real market records")
                    return records_added
                    
        except Exception as e:
            logger.error(f"❌ Data collection error: {e}")
            return 0

    async def collect_historical_klines(self, symbol: str, limit: int = 100):
        """Collect historical kline data"""
        try:
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol,
                'interval': '1h',
                'limit': limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    for kline in data:
                        timestamp = datetime.fromtimestamp(kline[0] / 1000).isoformat()
                        
                        cursor.execute('''
                            INSERT OR REPLACE INTO market_data 
                            (timestamp, symbol, open_price, high_price, low_price, close_price, volume, source)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            timestamp, symbol,
                            float(kline[1]),  # open
                            float(kline[2]),  # high
                            float(kline[3]),  # low
                            float(kline[4]),  # close
                            float(kline[5]),  # volume
                            'binance_klines'
                        ))
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Collected {len(data)} historical records for {symbol}")
                    return len(data)
                    
        except Exception as e:
            logger.error(f"❌ Historical data error: {e}")
            return 0

    def create_enhanced_features(self):
        """Create enhanced features from market data"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Load recent market data
            df = pd.read_sql_query('''
                SELECT timestamp, symbol, open_price, high_price, low_price, close_price, volume
                FROM market_data 
                ORDER BY symbol, timestamp
            ''', conn)
            
            if len(df) == 0:
                logger.warning("No market data available")
                return 0
            
            # Create enhanced features for each symbol
            enhanced_records = []
            
            for symbol in df['symbol'].unique():
                symbol_data = df[df['symbol'] == symbol].copy()
                
                if len(symbol_data) < 20:
                    continue
                
                symbol_data = symbol_data.sort_values('timestamp')
                
                # Technical indicators
                symbol_data['sma_5'] = symbol_data['close_price'].rolling(5).mean()
                symbol_data['sma_10'] = symbol_data['close_price'].rolling(10).mean()
                symbol_data['sma_20'] = symbol_data['close_price'].rolling(20).mean()
                
                # Exponential moving averages
                symbol_data['ema_12'] = symbol_data['close_price'].ewm(span=12).mean()
                symbol_data['ema_26'] = symbol_data['close_price'].ewm(span=26).mean()
                
                # MACD
                symbol_data['macd'] = symbol_data['ema_12'] - symbol_data['ema_26']
                symbol_data['macd_signal'] = symbol_data['macd'].ewm(span=9).mean()
                symbol_data['macd_histogram'] = symbol_data['macd'] - symbol_data['macd_signal']
                
                # RSI
                delta = symbol_data['close_price'].diff()
                gain = delta.where(delta > 0, 0)
                loss = -delta.where(delta < 0, 0)
                avg_gain = gain.rolling(14).mean()
                avg_loss = loss.rolling(14).mean()
                rs = avg_gain / avg_loss
                symbol_data['rsi'] = 100 - (100 / (1 + rs))
                
                # Bollinger Bands
                symbol_data['bb_middle'] = symbol_data['sma_20']
                bb_std = symbol_data['close_price'].rolling(20).std()
                symbol_data['bb_upper'] = symbol_data['bb_middle'] + (bb_std * 2)
                symbol_data['bb_lower'] = symbol_data['bb_middle'] - (bb_std * 2)
                symbol_data['bb_width'] = (symbol_data['bb_upper'] - symbol_data['bb_lower']) / symbol_data['bb_middle']
                symbol_data['bb_position'] = (symbol_data['close_price'] - symbol_data['bb_lower']) / (symbol_data['bb_upper'] - symbol_data['bb_lower'])
                
                # Volatility
                symbol_data['volatility'] = symbol_data['close_price'].rolling(10).std() / symbol_data['close_price'].rolling(10).mean()
                
                # Volume indicators
                symbol_data['volume_sma'] = symbol_data['volume'].rolling(10).mean()
                symbol_data['volume_ratio'] = symbol_data['volume'] / symbol_data['volume_sma']
                
                # Price momentum
                for period in [5, 10, 20]:
                    symbol_data[f'momentum_{period}'] = symbol_data['close_price'].pct_change(period)
                
                # Statistical features
                for window in [5, 10, 20]:
                    symbol_data[f'price_std_{window}'] = symbol_data['close_price'].rolling(window).std()
                    symbol_data[f'price_skew_{window}'] = symbol_data['close_price'].rolling(window).skew()
                
                # Target: future return
                symbol_data['target_return'] = symbol_data['close_price'].shift(-1).pct_change()
                
                # Create feature records
                feature_cols = [col for col in symbol_data.columns 
                              if col not in ['timestamp', 'symbol', 'open_price', 'high_price', 'low_price', 'close_price', 'volume', 'target_return']]
                
                for _, row in symbol_data.iterrows():
                    if pd.notna(row['target_return']):
                        features = {col: float(row[col]) if pd.notna(row[col]) else 0.0 for col in feature_cols}
                        
                        enhanced_records.append({
                            'timestamp': row['timestamp'],
                            'symbol': row['symbol'],
                            'features_json': json.dumps(features),
                            'target_return': float(row['target_return'])
                        })
            
            # Save enhanced features
            if enhanced_records:
                cursor = conn.cursor()
                for record in enhanced_records:
                    cursor.execute('''
                        INSERT INTO enhanced_features 
                        (timestamp, symbol, features_json, target_return)
                        VALUES (?, ?, ?, ?)
                    ''', (record['timestamp'], record['symbol'], record['features_json'], record['target_return']))
                
                conn.commit()
            
            conn.close()
            
            logger.info(f"✅ Created {len(enhanced_records)} enhanced feature records")
            return len(enhanced_records)
            
        except Exception as e:
            logger.error(f"❌ Feature engineering error: {e}")
            return 0

    def train_enhanced_models(self):
        """Train enhanced models"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Load enhanced features
            df = pd.read_sql_query('''
                SELECT features_json, target_return 
                FROM enhanced_features 
                WHERE target_return IS NOT NULL
            ''', conn)
            
            conn.close()
            
            if len(df) < 50:
                logger.warning("Insufficient data for training")
                return False
            
            # Parse features
            features_list = []
            targets = []
            
            for _, row in df.iterrows():
                try:
                    features = json.loads(row['features_json'])
                    feature_values = list(features.values())
                    
                    # Check for valid features
                    if len(feature_values) > 10 and not any(np.isnan(feature_values)):
                        features_list.append(feature_values)
                        targets.append(row['target_return'])
                except:
                    continue
            
            if len(features_list) < 20:
                logger.warning("Insufficient valid features for training")
                return False
            
            X = np.array(features_list)
            y = np.array(targets)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
            
            # Preprocessing
            scaler = RobustScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Feature selection
            selector = RFE(RandomForestRegressor(n_estimators=50, random_state=42), n_features_to_select=min(15, X.shape[1]))
            X_train_selected = selector.fit_transform(X_train_scaled, y_train)
            X_test_selected = selector.transform(X_test_scaled)
            
            # Train model
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                random_state=42,
                n_jobs=-1
            )
            
            model.fit(X_train_selected, y_train)
            
            # Evaluate
            y_pred_train = model.predict(X_train_selected)
            y_pred_test = model.predict(X_test_selected)
            
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            test_r2 = r2_score(y_test, y_pred_test)
            
            # Save models
            self.models['enhanced_rf'] = model
            self.scalers['main'] = scaler
            self.feature_selectors['main'] = selector
            
            logger.info(f"✅ Enhanced model trained successfully")
            logger.info(f"   Training MSE: {train_mse:.6f}")
            logger.info(f"   Test MSE: {test_mse:.6f}")
            logger.info(f"   Test R²: {test_r2:.6f}")
            logger.info(f"   Features selected: {X_train_selected.shape[1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Model training error: {e}")
            return False

    def generate_enhanced_predictions(self) -> List[EnhancedPrediction]:
        """Generate enhanced predictions"""
        try:
            if 'enhanced_rf' not in self.models:
                logger.warning("No trained model available")
                return []
            
            conn = sqlite3.connect(self.db_path)
            
            # Get latest features for each symbol
            predictions = []
            
            for symbol in self.crypto_pairs:
                # Get latest feature record
                query = '''
                    SELECT features_json, timestamp 
                    FROM enhanced_features 
                    WHERE symbol = ? 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                '''
                
                result = pd.read_sql_query(query, conn, params=(symbol,))
                
                if len(result) == 0:
                    continue
                
                try:
                    features = json.loads(result.iloc[0]['features_json'])
                    feature_values = np.array(list(features.values())).reshape(1, -1)
                    
                    # Preprocess
                    features_scaled = self.scalers['main'].transform(feature_values)
                    features_selected = self.feature_selectors['main'].transform(features_scaled)
                    
                    # Predict
                    prediction = self.models['enhanced_rf'].predict(features_selected)[0]
                    
                    # Calculate confidence (simplified)
                    confidence = min(1.0, abs(prediction) * 10 + 0.5)
                    
                    # Generate signal
                    if prediction > 0.01:  # > 1%
                        signal = "BUY"
                        strength = "STRONG" if prediction > 0.02 else "WEAK"
                    elif prediction < -0.01:  # < -1%
                        signal = "SELL"
                        strength = "STRONG" if prediction < -0.02 else "WEAK"
                    else:
                        signal = "HOLD"
                        strength = "NEUTRAL"
                    
                    reasoning = f"Enhanced model prediction: {prediction:+.3f} with confidence {confidence:.3f}"
                    
                    pred = EnhancedPrediction(
                        symbol=symbol,
                        prediction=prediction,
                        confidence=confidence,
                        signal=signal,
                        strength=strength,
                        reasoning=reasoning,
                        timestamp=datetime.now().isoformat()
                    )
                    
                    predictions.append(pred)
                    
                    # Save prediction to database
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO predictions 
                        (timestamp, symbol, prediction, confidence, signal, model_used)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (pred.timestamp, pred.symbol, pred.prediction, pred.confidence, pred.signal, 'enhanced_rf'))
                    
                except Exception as e:
                    logger.warning(f"Prediction failed for {symbol}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Generated {len(predictions)} enhanced predictions")
            return predictions
            
        except Exception as e:
            logger.error(f"❌ Prediction generation error: {e}")
            return []

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Count records
            market_data_count = pd.read_sql_query("SELECT COUNT(*) as count FROM market_data", conn).iloc[0]['count']
            features_count = pd.read_sql_query("SELECT COUNT(*) as count FROM enhanced_features", conn).iloc[0]['count']
            predictions_count = pd.read_sql_query("SELECT COUNT(*) as count FROM predictions", conn).iloc[0]['count']
            
            # Get latest data timestamp
            latest_data = pd.read_sql_query("SELECT MAX(timestamp) as latest FROM market_data", conn).iloc[0]['latest']
            
            conn.close()
            
            status = {
                'timestamp': datetime.now().isoformat(),
                'market_data_records': market_data_count,
                'enhanced_features': features_count,
                'predictions_generated': predictions_count,
                'latest_data': latest_data,
                'models_trained': len(self.models),
                'system_health': 'OPERATIONAL' if len(self.models) > 0 else 'TRAINING_REQUIRED'
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Status check error: {e}")
            return {}

    async def run_complete_enhanced_system(self, duration_minutes: int = 10):
        """Run complete enhanced system demonstration"""
        logger.info(f"🚀 Starting complete enhanced system for {duration_minutes} minutes")
        
        self.session = aiohttp.ClientSession()
        
        try:
            # Phase 1: Data Collection
            logger.info("📊 Phase 1: Collecting real market data...")
            
            # Collect current data
            await self.collect_real_data()
            
            # Collect historical data for each symbol
            for symbol in self.crypto_pairs:
                await self.collect_historical_klines(symbol, 50)
                await asyncio.sleep(0.5)  # Rate limiting
            
            # Phase 2: Feature Engineering
            logger.info("🔧 Phase 2: Creating enhanced features...")
            features_created = self.create_enhanced_features()
            
            if features_created == 0:
                logger.error("❌ No features created, cannot proceed")
                return {}
            
            # Phase 3: Model Training
            logger.info("🤖 Phase 3: Training enhanced models...")
            training_success = self.train_enhanced_models()
            
            if not training_success:
                logger.error("❌ Model training failed")
                return {}
            
            # Phase 4: Real-time Prediction Loop
            logger.info("🔮 Phase 4: Generating real-time predictions...")
            
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            prediction_cycles = 0
            
            while datetime.now() < end_time:
                prediction_cycles += 1
                logger.info(f"🔄 Prediction cycle {prediction_cycles}")
                
                # Collect fresh data
                await self.collect_real_data()
                
                # Update features
                self.create_enhanced_features()
                
                # Generate predictions
                predictions = self.generate_enhanced_predictions()
                
                if predictions:
                    logger.info("📈 Current enhanced predictions:")
                    for pred in predictions:
                        logger.info(f"   {pred.symbol}: {pred.signal} ({pred.strength}) - "
                                  f"Prediction: {pred.prediction:+.3f}, Confidence: {pred.confidence:.3f}")
                
                await asyncio.sleep(60)  # 1 minute intervals
            
            # Final status
            final_status = self.get_system_status()
            
            logger.info("✅ Complete enhanced system demonstration completed!")
            logger.info(f"📊 Final status: {final_status}")
            
            return final_status
            
        finally:
            await self.session.close()

async def main():
    """Main demonstration of complete enhanced system"""
    print("🚀 COMPLETE ENHANCED TRADING SYSTEM")
    print("=" * 60)
    print("Real data collection + Enhanced features + Advanced models + Live predictions")
    print("=" * 60)
    
    system = WorkingEnhancedSystem()
    
    # Run complete enhanced system
    results = await system.run_complete_enhanced_system(duration_minutes=5)
    
    print(f"\n🚀 COMPLETE ENHANCED SYSTEM RESULTS:")
    print(f"Market data records: {results.get('market_data_records', 0)}")
    print(f"Enhanced features: {results.get('enhanced_features', 0)}")
    print(f"Predictions generated: {results.get('predictions_generated', 0)}")
    print(f"Models trained: {results.get('models_trained', 0)}")
    print(f"System health: {results.get('system_health', 'UNKNOWN')}")
    
    print(f"\n✅ Complete enhanced system demonstration completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
