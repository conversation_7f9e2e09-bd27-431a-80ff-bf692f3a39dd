#!/usr/bin/env python3
"""
Performance Improvement Testing Script
Tests and validates the specific performance fixes implemented for NORYON V2
"""

import asyncio
import time
import logging
import statistics
from datetime import datetime, timezone
from typing import Dict, List, Any
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_ai_orchestration import AdvancedAIOrchestrator, AIRequest, AgentRole
from advanced_risk_management import AdvancedRiskManager
from advanced_performance_optimizer import AdvancedPerformanceOptimizer, PerformanceMetricType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)

logger = logging.getLogger("PerformanceTest")


class PerformanceTestSuite:
    """Comprehensive performance testing suite."""
    
    def __init__(self):
        self.ai_orchestrator = AdvancedAIOrchestrator()
        self.risk_manager = AdvancedRiskManager()
        self.performance_optimizer = AdvancedPerformanceOptimizer()
        
        self.test_results = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        logger.info("🧪 Starting Performance Test Suite")
        logger.info("=" * 60)
        
        # Test 1: AI Timeout Optimization
        await self.test_ai_timeout_optimization()
        
        # Test 2: Risk Management Enforcement
        await self.test_risk_management_enforcement()
        
        # Test 3: Performance Monitoring
        await self.test_performance_monitoring()
        
        # Test 4: System Reliability
        await self.test_system_reliability()
        
        # Test 5: Concurrent Processing
        await self.test_concurrent_processing()
        
        # Generate final test report
        return self.generate_test_report()
    
    async def test_ai_timeout_optimization(self):
        """Test AI timeout optimization and fallback mechanisms."""
        logger.info("🎯 TEST 1: AI Timeout Optimization")
        
        test_start = time.time()
        response_times = []
        success_count = 0
        fallback_count = 0
        
        # Test with different models and timeout scenarios
        test_requests = [
            ("marco-o1:7b", 8.0, "fast_model"),
            ("magistral:24b", 12.0, "medium_model"),
            ("command-r:35b", 15.0, "slow_model"),
            ("gemma3:27b", 10.0, "variable_model"),
            ("mistral-small:24b", 8.0, "execution_model")
        ]
        
        for model, timeout, test_type in test_requests:
            request = AIRequest(
                request_id=f"timeout_test_{test_type}",
                agent_role=AgentRole.MARKET_ANALYST,
                prompt="Provide brief market analysis",
                context={"test_type": test_type},
                priority=1,
                timeout=timeout,
                timestamp=datetime.now(timezone.utc),
                metadata={"model": model, "test": "timeout_optimization"}
            )
            
            start_time = time.time()
            try:
                response = await self.ai_orchestrator.process_request(request)
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.success:
                    success_count += 1
                    if "Fallback Response" in response.response:
                        fallback_count += 1
                    
                logger.info(f"   {test_type}: {response_time:.2f}s ({'success' if response.success else 'failed'})")
                
            except Exception as e:
                logger.error(f"   {test_type}: ERROR - {e}")
        
        # Calculate results
        avg_response_time = statistics.mean(response_times) if response_times else 0
        success_rate = success_count / len(test_requests)
        fallback_rate = fallback_count / len(test_requests)
        
        self.test_results["ai_timeout_optimization"] = {
            "avg_response_time": avg_response_time,
            "success_rate": success_rate,
            "fallback_rate": fallback_rate,
            "target_response_time": 12.0,
            "target_success_rate": 0.90,
            "passed": avg_response_time <= 12.0 and success_rate >= 0.90
        }
        
        logger.info(f"   Results: {avg_response_time:.2f}s avg, {success_rate:.1%} success, {fallback_rate:.1%} fallback")
        logger.info(f"   Status: {'✅ PASSED' if self.test_results['ai_timeout_optimization']['passed'] else '❌ FAILED'}")
        logger.info("")
    
    async def test_risk_management_enforcement(self):
        """Test risk management limit enforcement."""
        logger.info("⚖️ TEST 2: Risk Management Enforcement")
        
        # Create portfolio with violations
        portfolio_data = {
            "positions": {
                "BTCUSDT": {"size": 0.27, "value": 13500.0},  # 27% - violates 15% limit
                "ETHUSDT": {"size": 0.18, "value": 9000.0},
                "ADAUSDT": {"size": 0.15, "value": 7500.0},
                "SOLUSDT": {"size": 0.12, "value": 6000.0},
                "DOTUSDT": {"size": 0.08, "value": 4000.0}
            },
            "total_value": 40000.0,
            "cash": 10000.0
        }
        
        # Test risk calculation and enforcement
        start_time = time.time()
        risk_metrics = self.risk_manager.calculate_comprehensive_risk_metrics(portfolio_data)
        risk_report = self.risk_manager.generate_risk_report()
        calculation_time = time.time() - start_time
        
        # Check for violations and enforcement
        var_95 = risk_metrics.get("ensemble_var", {}).get("var_95", 0)
        position_violations = 0
        enforcement_actions = 0
        
        # Check position size violations
        for symbol, position in portfolio_data["positions"].items():
            if position["size"] > 0.15:  # 15% limit
                position_violations += 1
        
        # Check for enforcement actions in risk history
        if hasattr(self.risk_manager, 'risk_history') and self.risk_manager.risk_history:
            for record in self.risk_manager.risk_history:
                if "risk_enforcement" in record:
                    enforcement_actions += 1
        
        self.test_results["risk_management_enforcement"] = {
            "var_95": var_95,
            "position_violations": position_violations,
            "enforcement_actions": enforcement_actions,
            "calculation_time": calculation_time,
            "target_var_95": 0.03,
            "target_calculation_time": 2.0,
            "passed": calculation_time <= 2.0 and enforcement_actions > 0
        }
        
        logger.info(f"   VaR95: {var_95:.2%} (limit: 3.0%)")
        logger.info(f"   Position violations: {position_violations}")
        logger.info(f"   Enforcement actions: {enforcement_actions}")
        logger.info(f"   Calculation time: {calculation_time:.2f}s")
        logger.info(f"   Status: {'✅ PASSED' if self.test_results['risk_management_enforcement']['passed'] else '❌ FAILED'}")
        logger.info("")
    
    async def test_performance_monitoring(self):
        """Test performance monitoring and optimization."""
        logger.info("📊 TEST 3: Performance Monitoring")
        
        # Record various performance metrics
        test_metrics = [
            (PerformanceMetricType.AI_RESPONSE_TIME, 12.5, "test_agent"),
            (PerformanceMetricType.AI_SUCCESS_RATE, 0.85, "test_agent"),
            (PerformanceMetricType.SYSTEM_THROUGHPUT, 3.2, "system"),
            (PerformanceMetricType.RISK_CALCULATION_TIME, 1.8, "risk_manager")
        ]
        
        for metric_type, value, component in test_metrics:
            self.performance_optimizer.record_metric(metric_type, value, component)
        
        # Test optimization trigger
        optimization_triggered = len(self.performance_optimizer.optimization_actions) > 0
        
        # Execute optimizations
        optimization_result = await self.performance_optimizer.execute_optimizations()
        
        # Get performance report
        performance_report = self.performance_optimizer.get_performance_report()
        
        self.test_results["performance_monitoring"] = {
            "metrics_recorded": len(test_metrics),
            "optimizations_triggered": optimization_triggered,
            "optimizations_executed": optimization_result.get("actions_executed", 0),
            "performance_score": performance_report.get("current_performance_score", 0),
            "target_score": performance_report.get("target_performance_score", 8.5),
            "passed": optimization_triggered and optimization_result.get("actions_executed", 0) > 0
        }
        
        logger.info(f"   Metrics recorded: {len(test_metrics)}")
        logger.info(f"   Optimizations triggered: {optimization_triggered}")
        logger.info(f"   Optimizations executed: {optimization_result.get('actions_executed', 0)}")
        logger.info(f"   Performance score: {performance_report.get('current_performance_score', 0):.1f}")
        logger.info(f"   Status: {'✅ PASSED' if self.test_results['performance_monitoring']['passed'] else '❌ FAILED'}")
        logger.info("")
    
    async def test_system_reliability(self):
        """Test system reliability and error handling."""
        logger.info("🛡️ TEST 4: System Reliability")
        
        error_count = 0
        recovery_count = 0
        total_tests = 5
        
        # Test error scenarios
        error_scenarios = [
            ("invalid_model", "nonexistent:model"),
            ("timeout_scenario", "magistral:24b"),
            ("empty_prompt", "marco-o1:7b"),
            ("invalid_context", "cogito:32b"),
            ("network_error", "command-r:35b")
        ]
        
        for scenario, model in error_scenarios:
            try:
                if scenario == "empty_prompt":
                    prompt = ""
                elif scenario == "invalid_context":
                    context = {"invalid": None}
                else:
                    prompt = "Test prompt"
                    context = {"test": True}
                
                request = AIRequest(
                    request_id=f"reliability_test_{scenario}",
                    agent_role=AgentRole.MARKET_ANALYST,
                    prompt=prompt,
                    context=context if scenario != "invalid_context" else {"invalid": None},
                    priority=1,
                    timeout=5.0 if scenario == "timeout_scenario" else 15.0,
                    timestamp=datetime.now(timezone.utc),
                    metadata={"test": "reliability", "scenario": scenario}
                )
                
                response = await self.ai_orchestrator.process_request(request)
                
                if not response.success:
                    error_count += 1
                    # Check if system recovered gracefully
                    if "Fallback Response" in response.response or "intelligent fallback" in response.response.lower():
                        recovery_count += 1
                
                logger.info(f"   {scenario}: {'recovered' if not response.success and 'fallback' in response.response.lower() else 'success' if response.success else 'failed'}")
                
            except Exception as e:
                error_count += 1
                logger.info(f"   {scenario}: exception handled")
        
        reliability_score = (total_tests - error_count + recovery_count) / total_tests
        
        self.test_results["system_reliability"] = {
            "total_tests": total_tests,
            "errors": error_count,
            "recoveries": recovery_count,
            "reliability_score": reliability_score,
            "target_reliability": 0.8,
            "passed": reliability_score >= 0.8
        }
        
        logger.info(f"   Errors: {error_count}/{total_tests}")
        logger.info(f"   Recoveries: {recovery_count}")
        logger.info(f"   Reliability score: {reliability_score:.1%}")
        logger.info(f"   Status: {'✅ PASSED' if self.test_results['system_reliability']['passed'] else '❌ FAILED'}")
        logger.info("")
    
    async def test_concurrent_processing(self):
        """Test concurrent processing performance."""
        logger.info("⚡ TEST 5: Concurrent Processing")
        
        # Create multiple concurrent requests
        concurrent_requests = []
        for i in range(8):  # Test with 8 concurrent requests
            request = AIRequest(
                request_id=f"concurrent_test_{i}",
                agent_role=AgentRole.MARKET_ANALYST,
                prompt=f"Analyze market condition {i}",
                context={"request_id": i},
                priority=1,
                timeout=10.0,
                timestamp=datetime.now(timezone.utc),
                metadata={"test": "concurrent", "batch": i}
            )
            concurrent_requests.append(request)
        
        # Execute concurrent requests
        start_time = time.time()
        tasks = [self.ai_orchestrator.process_request(req) for req in concurrent_requests]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Analyze results
        successful_responses = sum(1 for r in responses if not isinstance(r, Exception) and r.success)
        failed_responses = len(responses) - successful_responses
        throughput = len(responses) / (total_time / 60)  # requests per minute
        
        self.test_results["concurrent_processing"] = {
            "total_requests": len(concurrent_requests),
            "successful": successful_responses,
            "failed": failed_responses,
            "total_time": total_time,
            "throughput": throughput,
            "target_throughput": 5.0,
            "passed": throughput >= 5.0 and successful_responses >= 6
        }
        
        logger.info(f"   Concurrent requests: {len(concurrent_requests)}")
        logger.info(f"   Successful: {successful_responses}")
        logger.info(f"   Failed: {failed_responses}")
        logger.info(f"   Total time: {total_time:.2f}s")
        logger.info(f"   Throughput: {throughput:.1f} req/min")
        logger.info(f"   Status: {'✅ PASSED' if self.test_results['concurrent_processing']['passed'] else '❌ FAILED'}")
        logger.info("")
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        passed_tests = sum(1 for result in self.test_results.values() if result.get("passed", False))
        total_tests = len(self.test_results)
        overall_success = passed_tests / total_tests if total_tests > 0 else 0
        
        logger.info("=" * 60)
        logger.info("PERFORMANCE IMPROVEMENT TEST RESULTS")
        logger.info("=" * 60)
        
        for test_name, results in self.test_results.items():
            status = "✅ PASSED" if results.get("passed", False) else "❌ FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        logger.info("")
        logger.info(f"Overall Success Rate: {overall_success:.1%} ({passed_tests}/{total_tests})")
        logger.info("=" * 60)
        
        return {
            "test_results": self.test_results,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": overall_success,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }


async def main():
    """Main function to run performance tests."""
    test_suite = PerformanceTestSuite()
    
    try:
        results = await test_suite.run_all_tests()
        
        # Save results to file
        import json
        with open(f"performance_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Test suite error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
