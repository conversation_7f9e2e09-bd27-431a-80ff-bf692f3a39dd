#!/usr/bin/env python3
"""
Simple Test Script for Key Improvements
Tests the most important fixes directly
"""

import sys
import importlib.util

def test_risk_management_fix():
    """Test risk management bug fix"""
    print("🔍 Testing Risk Management Bug Fix...")
    try:
        # Import and test
        spec = importlib.util.spec_from_file_location("advanced_risk_management", "advanced_risk_management.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Test the fix
        risk_manager = module.AdvancedRiskManager()
        
        # Check if max_sector_exposure exists
        has_attribute = hasattr(risk_manager.risk_limits, 'max_sector_exposure')
        correct_value = risk_manager.risk_limits.max_sector_exposure == 0.35
        
        if has_attribute and correct_value:
            print("✅ Risk Management Bug: FIXED")
            print(f"   - max_sector_exposure attribute exists: {has_attribute}")
            print(f"   - Default value is 0.35: {correct_value}")
            return True
        else:
            print("❌ Risk Management Bug: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Risk Management Test Failed: {e}")
        return False

def test_unified_system():
    """Test unified trading system"""
    print("\n🔍 Testing Unified Trading System...")
    try:
        # Import and test
        spec = importlib.util.spec_from_file_location("unified_trading_system", "unified_trading_system.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Test the system
        config = module.SystemConfig(initial_capital=10000.0, paper_trading=True)
        system = module.UnifiedTradingSystem(config)
        
        # Test status
        system_ready = system.system_status == "READY"
        
        # Test API
        status = system.get_system_status()
        api_working = "system_status" in status and "portfolio" in status
        
        portfolio = system.get_portfolio_summary()
        portfolio_api_working = "portfolio" in portfolio and "positions" in portfolio
        
        if system_ready and api_working and portfolio_api_working:
            print("✅ Unified Trading System: WORKING")
            print(f"   - System initialization: {system_ready}")
            print(f"   - Status API: {api_working}")
            print(f"   - Portfolio API: {portfolio_api_working}")
            return True
        else:
            print("❌ Unified Trading System: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Unified System Test Failed: {e}")
        return False

def test_monitoring_system():
    """Test enhanced monitoring system"""
    print("\n🔍 Testing Enhanced Monitoring System...")
    try:
        # Import and test
        spec = importlib.util.spec_from_file_location("enhanced_monitoring_system", "enhanced_monitoring_system.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Test the system
        monitoring = module.EnhancedMonitoringSystem()
        
        # Test dashboard generation
        dashboard_gen = module.MonitoringDashboard(monitoring)
        html = dashboard_gen.generate_html_dashboard()
        
        html_valid = len(html) > 500 and "Trading System Monitoring Dashboard" in html
        
        if html_valid:
            print("✅ Enhanced Monitoring: WORKING")
            print(f"   - Dashboard generation: {html_valid}")
            print(f"   - HTML length: {len(html)} characters")
            return True
        else:
            print("❌ Enhanced Monitoring: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced Monitoring Test Failed: {e}")
        return False

def test_backtesting_system():
    """Test comprehensive backtesting"""
    print("\n🔍 Testing Comprehensive Backtesting...")
    try:
        # Import and test
        spec = importlib.util.spec_from_file_location("comprehensive_backtesting", "comprehensive_backtesting.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Test the system
        config = module.BacktestConfig(
            initial_capital=10000.0,
            start_date="2024-01-01",
            end_date="2024-03-31"
        )
        engine = module.AdvancedBacktestEngine(config)
        
        # Test data loading
        data_loaded = engine.load_historical_data("synthetic", ['BTCUSDT', 'ETHUSDT'])
        
        if data_loaded and len(engine.historical_data) > 0:
            print("✅ Comprehensive Backtesting: WORKING")
            print(f"   - Data loading: {data_loaded}")
            print(f"   - Symbols loaded: {len(engine.historical_data)}")
            return True
        else:
            print("❌ Comprehensive Backtesting: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Backtesting Test Failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 SYSTEM IMPROVEMENTS TEST SUITE")
    print("="*50)
    
    # Run tests
    results = []
    results.append(test_risk_management_fix())
    results.append(test_unified_system())
    results.append(test_monitoring_system())
    results.append(test_backtesting_system())
    
    # Calculate results
    passed = sum(results)
    total = len(results)
    success_rate = passed / total
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    print(f"Tests Passed: {passed}/{total} ({success_rate:.1%})")
    
    if success_rate == 1.0:
        print("🎉 ALL IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!")
        
        print("\n✅ COMPLETED IMPROVEMENTS:")
        print("   • Risk Management Bug: FIXED")
        print("   • Unified API Layer: IMPLEMENTED")
        print("   • Enhanced Monitoring: IMPLEMENTED")  
        print("   • Comprehensive Backtesting: IMPLEMENTED")
        
        print("\n🚀 NEW CAPABILITIES:")
        print("   • Consolidated trading system with clean API")
        print("   • Real-time monitoring with HTML dashboards")
        print("   • Advanced backtesting engine")
        print("   • Enhanced error tracking")
        
    else:
        print("⚠️ Some improvements need attention")
    
    print("="*50)
    
    return success_rate == 1.0

if __name__ == "__main__":
    main() 