#!/usr/bin/env python3
"""
Advanced AGI Memory System
Realistic AGI-like memory, learning, and cognitive capabilities for trading AI
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import pickle
import hashlib
from collections import defaultdict, deque
import sqlite3
import threading
import time

logger = logging.getLogger("AdvancedAGIMemorySystem")


class MemoryType(Enum):
    """Types of memory storage."""
    WORKING = "working"           # Short-term active memory
    EPISODIC = "episodic"        # Specific events and experiences
    SEMANTIC = "semantic"        # Facts and knowledge
    PROCEDURAL = "procedural"    # Skills and procedures
    EMOTIONAL = "emotional"      # Emotional associations
    STRATEGIC = "strategic"      # Long-term strategic insights


class MemoryImportance(Enum):
    """Memory importance levels."""
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    TRIVIAL = 1


class LearningType(Enum):
    """Types of learning."""
    SUPERVISED = "supervised"
    UNSUPERVISED = "unsupervised"
    REINFORCEMENT = "reinforcement"
    TRANSFER = "transfer"
    META = "meta"
    CONTINUAL = "continual"


@dataclass
class Memory:
    """Individual memory unit."""
    memory_id: str
    memory_type: MemoryType
    content: Dict[str, Any]
    importance: MemoryImportance
    confidence: float
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    emotional_valence: float = 0.0  # -1 (negative) to +1 (positive)
    associations: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    decay_rate: float = 0.01  # How fast memory fades


@dataclass
class LearningExperience:
    """Learning experience record."""
    experience_id: str
    learning_type: LearningType
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    reward: float
    success: bool
    lessons_learned: List[str]
    timestamp: datetime
    context: Dict[str, Any]


class AdvancedAGIMemorySystem:
    """Advanced AGI-like memory and learning system."""

    def __init__(self, memory_db_path: str = "agi_memory.db"):
        self.memory_db_path = memory_db_path
        self.working_memory = {}  # Active short-term memory
        self.memory_cache = {}    # Recently accessed memories
        self.learning_experiences = deque(maxlen=10000)  # Learning history
        self.knowledge_graph = defaultdict(list)  # Associative connections
        self.emotional_state = {"valence": 0.0, "arousal": 0.0, "confidence": 0.5}
        self.personality_traits = self._initialize_personality()
        self.cognitive_load = 0.0
        self.attention_focus = []
        self.goals = []
        self.beliefs = {}
        self.metacognitive_state = {}

        # Initialize database
        self._initialize_memory_database()

        # Memory management thread
        self.memory_thread = threading.Thread(target=self._memory_maintenance_loop, daemon=True)
        self.memory_thread.start()

        logger.info("🧠 Advanced AGI Memory System initialized")

    def _initialize_personality(self) -> Dict[str, float]:
        """Initialize AI personality traits."""
        return {
            "risk_tolerance": 0.6,      # Moderate risk tolerance
            "curiosity": 0.8,           # High curiosity for learning
            "confidence": 0.7,          # Moderate confidence
            "adaptability": 0.9,        # High adaptability
            "analytical": 0.95,         # Very analytical
            "intuitive": 0.6,           # Moderate intuition
            "conservative": 0.4,        # Moderately aggressive
            "social": 0.3,              # Low social needs (focused on trading)
            "perfectionism": 0.8,       # High perfectionism
            "patience": 0.7             # Good patience for long-term strategies
        }

    def _initialize_memory_database(self):
        """Initialize SQLite database for persistent memory."""
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            # Create memories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    memory_id TEXT PRIMARY KEY,
                    memory_type TEXT,
                    content TEXT,
                    importance INTEGER,
                    confidence REAL,
                    created_at TEXT,
                    last_accessed TEXT,
                    access_count INTEGER,
                    emotional_valence REAL,
                    associations TEXT,
                    tags TEXT,
                    context TEXT,
                    decay_rate REAL
                )
            ''')

            # Create learning experiences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_experiences (
                    experience_id TEXT PRIMARY KEY,
                    learning_type TEXT,
                    input_data TEXT,
                    output_data TEXT,
                    reward REAL,
                    success BOOLEAN,
                    lessons_learned TEXT,
                    timestamp TEXT,
                    context TEXT
                )
            ''')

            # Create knowledge graph table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_graph (
                    source_id TEXT,
                    target_id TEXT,
                    relationship_type TEXT,
                    strength REAL,
                    created_at TEXT
                )
            ''')

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Memory database initialization error: {e}")

    def store_memory(self, memory_type: MemoryType, content: Dict[str, Any],
                    importance: MemoryImportance = MemoryImportance.MEDIUM,
                    emotional_valence: float = 0.0, tags: List[str] = None,
                    context: Dict[str, Any] = None) -> str:
        """Store a new memory."""
        try:
            # Generate unique memory ID
            memory_id = self._generate_memory_id(content)

            # Create memory object
            memory = Memory(
                memory_id=memory_id,
                memory_type=memory_type,
                content=content,
                importance=importance,
                confidence=0.9,  # High initial confidence
                created_at=datetime.now(timezone.utc),
                last_accessed=datetime.now(timezone.utc),
                emotional_valence=emotional_valence,
                tags=tags or [],
                context=context or {}
            )

            # Store in working memory if important
            if importance.value >= MemoryImportance.HIGH.value:
                self.working_memory[memory_id] = memory

            # Store in cache
            self.memory_cache[memory_id] = memory

            # Persist to database
            self._persist_memory(memory)

            # Update emotional state
            self._update_emotional_state(emotional_valence)

            # Create associations
            self._create_memory_associations(memory)

            logger.info(f"🧠 Memory stored: {memory_type.value} - {memory_id[:8]}")

            return memory_id

        except Exception as e:
            logger.error(f"Memory storage error: {e}")
            return ""

    def retrieve_memory(self, memory_id: str = None, query: Dict[str, Any] = None,
                       memory_type: MemoryType = None, limit: int = 10) -> List[Memory]:
        """Retrieve memories by ID, query, or type."""
        try:
            memories = []

            # Retrieve specific memory by ID
            if memory_id:
                memory = self._get_memory_by_id(memory_id)
                if memory:
                    memories = [memory]

            # Retrieve by query or type
            else:
                memories = self._search_memories(query, memory_type, limit)

            # Update access information
            for memory in memories:
                memory.last_accessed = datetime.now(timezone.utc)
                memory.access_count += 1
                self.memory_cache[memory.memory_id] = memory

            return memories

        except Exception as e:
            logger.error(f"Memory retrieval error: {e}")
            return []

    def learn_from_experience(self, learning_type: LearningType,
                            input_data: Dict[str, Any], output_data: Dict[str, Any],
                            reward: float, success: bool, context: Dict[str, Any] = None) -> str:
        """Learn from a trading experience."""
        try:
            experience_id = f"exp_{int(time.time() * 1000)}"

            # Analyze the experience
            lessons_learned = self._extract_lessons(input_data, output_data, reward, success)

            # Create learning experience
            experience = LearningExperience(
                experience_id=experience_id,
                learning_type=learning_type,
                input_data=input_data,
                output_data=output_data,
                reward=reward,
                success=success,
                lessons_learned=lessons_learned,
                timestamp=datetime.now(timezone.utc),
                context=context or {}
            )

            # Store experience
            self.learning_experiences.append(experience)
            self._persist_learning_experience(experience)

            # Update beliefs and strategies
            self._update_beliefs(experience)

            # Store as episodic memory
            memory_content = {
                "experience_type": "trading_decision",
                "input": input_data,
                "output": output_data,
                "outcome": {"reward": reward, "success": success},
                "lessons": lessons_learned
            }

            importance = MemoryImportance.HIGH if abs(reward) > 0.05 else MemoryImportance.MEDIUM
            emotional_valence = min(max(reward * 2, -1), 1)  # Scale reward to emotion

            memory_id = self.store_memory(
                MemoryType.EPISODIC,
                memory_content,
                importance,
                emotional_valence,
                tags=["trading", "experience", learning_type.value],
                context=context
            )

            # Meta-learning: Learn how to learn better
            self._meta_learn(experience)

            logger.info(f"🎓 Learning experience recorded: {experience_id} (reward: {reward:.3f})")

            return experience_id

        except Exception as e:
            logger.error(f"Learning experience error: {e}")
            return ""

    def get_relevant_memories(self, current_context: Dict[str, Any],
                            limit: int = 5) -> List[Memory]:
        """Get memories relevant to current trading context."""
        try:
            # Extract context features
            symbol = current_context.get("symbol", "")
            market_condition = current_context.get("market_condition", "")
            strategy = current_context.get("strategy", "")

            # Search for relevant memories
            relevant_memories = []

            # 1. Similar market conditions
            market_memories = self._search_memories(
                {"market_condition": market_condition},
                MemoryType.EPISODIC,
                limit=3
            )
            relevant_memories.extend(market_memories)

            # 2. Same symbol experiences
            symbol_memories = self._search_memories(
                {"symbol": symbol},
                MemoryType.EPISODIC,
                limit=3
            )
            relevant_memories.extend(symbol_memories)

            # 3. Strategic knowledge
            strategy_memories = self._search_memories(
                {"strategy": strategy},
                MemoryType.SEMANTIC,
                limit=2
            )
            relevant_memories.extend(strategy_memories)

            # 4. Recent important memories
            recent_memories = self._get_recent_important_memories(limit=2)
            relevant_memories.extend(recent_memories)

            # Remove duplicates and sort by relevance
            unique_memories = {m.memory_id: m for m in relevant_memories}
            sorted_memories = sorted(
                unique_memories.values(),
                key=lambda m: (m.importance.value, m.confidence, -m.access_count),
                reverse=True
            )

            return sorted_memories[:limit]

        except Exception as e:
            logger.error(f"Relevant memories retrieval error: {e}")
            return []

    def update_cognitive_state(self, market_data: Dict[str, Any],
                             trading_performance: Dict[str, Any]):
        """Update cognitive and emotional state based on current conditions."""
        try:
            # Update cognitive load based on market complexity
            volatility = market_data.get("volatility", 0.2)
            volume_ratio = market_data.get("volume_ratio", 1.0)

            # Higher volatility and volume = higher cognitive load
            self.cognitive_load = min((volatility * 2 + volume_ratio - 1) * 0.5, 1.0)

            # Update emotional state based on performance
            recent_pnl = trading_performance.get("recent_pnl", 0.0)
            win_rate = trading_performance.get("win_rate", 0.5)

            # Emotional valence based on performance
            performance_emotion = (recent_pnl * 0.5 + (win_rate - 0.5) * 2)
            self.emotional_state["valence"] = min(max(performance_emotion, -1), 1)

            # Arousal based on volatility and cognitive load
            self.emotional_state["arousal"] = min(volatility * 2 + self.cognitive_load, 1.0)

            # Confidence based on recent performance and experience
            experience_count = len(self.learning_experiences)
            confidence_boost = min(experience_count / 1000, 0.3)  # Max 30% boost
            base_confidence = 0.5 + (win_rate - 0.5) * 0.4
            self.emotional_state["confidence"] = min(base_confidence + confidence_boost, 1.0)

            # Update attention focus
            self._update_attention_focus(market_data)

            # Update metacognitive state
            self._update_metacognitive_state(trading_performance)

        except Exception as e:
            logger.error(f"Cognitive state update error: {e}")

    def generate_insights(self, context: Dict[str, Any]) -> List[str]:
        """Generate insights based on accumulated knowledge and experience."""
        try:
            insights = []

            # Get relevant memories
            relevant_memories = self.get_relevant_memories(context, limit=10)

            # Analyze patterns in memories
            patterns = self._analyze_memory_patterns(relevant_memories)

            # Generate insights from patterns
            for pattern in patterns:
                if pattern["confidence"] > 0.7:
                    insight = self._pattern_to_insight(pattern, context)
                    if insight:
                        insights.append(insight)

            # Add strategic insights from beliefs
            strategic_insights = self._generate_strategic_insights(context)
            insights.extend(strategic_insights)

            # Add emotional/intuitive insights
            intuitive_insights = self._generate_intuitive_insights(context)
            insights.extend(intuitive_insights)

            # Rank insights by relevance and confidence
            ranked_insights = self._rank_insights(insights, context)

            return ranked_insights[:5]  # Top 5 insights

        except Exception as e:
            logger.error(f"Insight generation error: {e}")
            return ["Unable to generate insights due to system error"]

    def _generate_memory_id(self, content: Dict[str, Any]) -> str:
        """Generate unique memory ID."""
        content_str = json.dumps(content, sort_keys=True)
        timestamp = str(int(time.time() * 1000))
        hash_input = f"{content_str}_{timestamp}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    def _persist_memory(self, memory: Memory):
        """Persist memory to database."""
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO memories VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                memory.memory_id,
                memory.memory_type.value,
                json.dumps(memory.content),
                memory.importance.value,
                memory.confidence,
                memory.created_at.isoformat(),
                memory.last_accessed.isoformat(),
                memory.access_count,
                memory.emotional_valence,
                json.dumps(memory.associations),
                json.dumps(memory.tags),
                json.dumps(memory.context),
                memory.decay_rate
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Memory persistence error: {e}")

    def _get_memory_by_id(self, memory_id: str) -> Optional[Memory]:
        """Get memory by ID from cache or database."""
        # Check cache first
        if memory_id in self.memory_cache:
            return self.memory_cache[memory_id]

        # Check database
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM memories WHERE memory_id = ?', (memory_id,))
            row = cursor.fetchone()

            if row:
                memory = self._row_to_memory(row)
                self.memory_cache[memory_id] = memory
                return memory

            conn.close()

        except Exception as e:
            logger.error(f"Memory retrieval error: {e}")

        return None

    def _search_memories(self, query: Dict[str, Any], memory_type: MemoryType = None,
                        limit: int = 10) -> List[Memory]:
        """Search memories by query."""
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            # Build query
            sql = "SELECT * FROM memories WHERE 1=1"
            params = []

            if memory_type:
                sql += " AND memory_type = ?"
                params.append(memory_type.value)

            if query:
                # Simple text search in content
                for key, value in query.items():
                    sql += " AND content LIKE ?"
                    params.append(f"%{key}%{value}%")

            sql += " ORDER BY importance DESC, confidence DESC LIMIT ?"
            params.append(limit)

            cursor.execute(sql, params)
            rows = cursor.fetchall()

            memories = [self._row_to_memory(row) for row in rows]
            conn.close()

            return memories

        except Exception as e:
            logger.error(f"Memory search error: {e}")
            return []

    def _row_to_memory(self, row) -> Memory:
        """Convert database row to Memory object."""
        return Memory(
            memory_id=row[0],
            memory_type=MemoryType(row[1]),
            content=json.loads(row[2]),
            importance=MemoryImportance(row[3]),
            confidence=row[4],
            created_at=datetime.fromisoformat(row[5]),
            last_accessed=datetime.fromisoformat(row[6]),
            access_count=row[7],
            emotional_valence=row[8],
            associations=json.loads(row[9]),
            tags=json.loads(row[10]),
            context=json.loads(row[11]),
            decay_rate=row[12]
        )

    def _extract_lessons(self, input_data: Dict[str, Any], output_data: Dict[str, Any],
                        reward: float, success: bool) -> List[str]:
        """Extract lessons from trading experience."""
        lessons = []

        try:
            # Analyze what worked or didn't work
            if success and reward > 0.02:  # Good trade
                lessons.append(f"Strategy worked well in {input_data.get('market_condition', 'current')} conditions")
                lessons.append(f"Signal strength of {input_data.get('signal_strength', 'unknown')} was reliable")

                if input_data.get("volatility", 0) > 0.3:
                    lessons.append("High volatility trades can be profitable with proper timing")

            elif not success or reward < -0.02:  # Bad trade
                lessons.append(f"Strategy failed in {input_data.get('market_condition', 'current')} conditions")

                if input_data.get("volume_ratio", 1) < 0.8:
                    lessons.append("Low volume trades are risky - require higher confirmation")

                if abs(reward) > 0.05:
                    lessons.append("Large losses indicate need for better risk management")

            # Market-specific lessons
            symbol = input_data.get("symbol", "")
            if symbol:
                if reward > 0:
                    lessons.append(f"{symbol} responds well to {output_data.get('strategy', 'current')} strategy")
                else:
                    lessons.append(f"{symbol} requires different approach than {output_data.get('strategy', 'current')} strategy")

            return lessons

        except Exception as e:
            logger.error(f"Lesson extraction error: {e}")
            return ["Experience recorded but lessons unclear"]

    def _update_beliefs(self, experience: LearningExperience):
        """Update beliefs based on experience."""
        try:
            # Update strategy beliefs
            strategy = experience.context.get("strategy", "unknown")
            if strategy not in self.beliefs:
                self.beliefs[strategy] = {"effectiveness": 0.5, "confidence": 0.1, "sample_size": 0}

            # Update effectiveness based on reward
            current_eff = self.beliefs[strategy]["effectiveness"]
            sample_size = self.beliefs[strategy]["sample_size"]

            # Weighted average with new experience
            new_effectiveness = (current_eff * sample_size + experience.reward) / (sample_size + 1)
            self.beliefs[strategy]["effectiveness"] = new_effectiveness
            self.beliefs[strategy]["sample_size"] += 1
            self.beliefs[strategy]["confidence"] = min(sample_size / 100, 0.9)  # Max 90% confidence

            # Update market condition beliefs
            market_condition = experience.context.get("market_condition", "unknown")
            if market_condition not in self.beliefs:
                self.beliefs[market_condition] = {"favorability": 0.5, "confidence": 0.1, "sample_size": 0}

            current_fav = self.beliefs[market_condition]["favorability"]
            sample_size = self.beliefs[market_condition]["sample_size"]

            new_favorability = (current_fav * sample_size + (1 if experience.success else 0)) / (sample_size + 1)
            self.beliefs[market_condition]["favorability"] = new_favorability
            self.beliefs[market_condition]["sample_size"] += 1
            self.beliefs[market_condition]["confidence"] = min(sample_size / 50, 0.9)

        except Exception as e:
            logger.error(f"Belief update error: {e}")

    def _meta_learn(self, experience: LearningExperience):
        """Meta-learning: Learn how to learn better."""
        try:
            # Analyze learning effectiveness
            if "meta_learning" not in self.metacognitive_state:
                self.metacognitive_state["meta_learning"] = {
                    "learning_rate": 0.1,
                    "exploration_rate": 0.2,
                    "confidence_threshold": 0.7,
                    "adaptation_speed": 0.05
                }

            meta_state = self.metacognitive_state["meta_learning"]

            # Adjust learning rate based on recent performance
            recent_experiences = list(self.learning_experiences)[-10:]  # Last 10 experiences
            if len(recent_experiences) >= 5:
                avg_reward = np.mean([exp.reward for exp in recent_experiences])

                if avg_reward > 0.02:  # Good performance
                    meta_state["learning_rate"] *= 0.95  # Slow down learning (exploitation)
                    meta_state["exploration_rate"] *= 0.9  # Reduce exploration
                elif avg_reward < -0.02:  # Poor performance
                    meta_state["learning_rate"] *= 1.05  # Speed up learning
                    meta_state["exploration_rate"] *= 1.1  # Increase exploration

            # Adjust confidence threshold based on accuracy
            if experience.success:
                meta_state["confidence_threshold"] *= 0.99  # Slightly lower threshold
            else:
                meta_state["confidence_threshold"] *= 1.01  # Slightly higher threshold

            # Keep values in reasonable ranges
            meta_state["learning_rate"] = np.clip(meta_state["learning_rate"], 0.01, 0.5)
            meta_state["exploration_rate"] = np.clip(meta_state["exploration_rate"], 0.05, 0.5)
            meta_state["confidence_threshold"] = np.clip(meta_state["confidence_threshold"], 0.5, 0.95)

        except Exception as e:
            logger.error(f"Meta-learning error: {e}")

    def _update_emotional_state(self, valence: float):
        """Update emotional state based on new experience."""
        try:
            # Emotional momentum - emotions change gradually
            momentum = 0.1
            current_valence = self.emotional_state["valence"]

            # Update with momentum
            new_valence = current_valence * (1 - momentum) + valence * momentum
            self.emotional_state["valence"] = np.clip(new_valence, -1, 1)

            # Arousal increases with extreme emotions
            arousal_increase = abs(valence) * 0.1
            current_arousal = self.emotional_state["arousal"]
            new_arousal = min(current_arousal + arousal_increase, 1.0)
            self.emotional_state["arousal"] = new_arousal * 0.95  # Gradual decay

        except Exception as e:
            logger.error(f"Emotional state update error: {e}")

    def _create_memory_associations(self, memory: Memory):
        """Create associations between memories."""
        try:
            # Find similar memories to associate with
            similar_memories = self._find_similar_memories(memory, limit=5)

            for similar_memory in similar_memories:
                # Calculate association strength
                strength = self._calculate_association_strength(memory, similar_memory)

                if strength > 0.3:  # Minimum threshold
                    # Add bidirectional association
                    memory.associations.append(similar_memory.memory_id)
                    similar_memory.associations.append(memory.memory_id)

                    # Store in knowledge graph
                    self._store_association(memory.memory_id, similar_memory.memory_id,
                                          "similarity", strength)

        except Exception as e:
            logger.error(f"Memory association error: {e}")

    def _find_similar_memories(self, memory: Memory, limit: int = 5) -> List[Memory]:
        """Find memories similar to the given memory."""
        try:
            # Search for memories with similar tags or content
            similar_memories = []

            # Search by tags
            for tag in memory.tags:
                tag_memories = self._search_memories({"tag": tag}, memory.memory_type, limit=3)
                similar_memories.extend(tag_memories)

            # Search by content keywords
            content_str = json.dumps(memory.content)
            keywords = self._extract_keywords(content_str)

            for keyword in keywords[:3]:  # Top 3 keywords
                keyword_memories = self._search_memories({keyword: ""}, memory.memory_type, limit=2)
                similar_memories.extend(keyword_memories)

            # Remove duplicates and the memory itself
            unique_memories = {}
            for mem in similar_memories:
                if mem.memory_id != memory.memory_id:
                    unique_memories[mem.memory_id] = mem

            return list(unique_memories.values())[:limit]

        except Exception as e:
            logger.error(f"Similar memory search error: {e}")
            return []

    def _calculate_association_strength(self, memory1: Memory, memory2: Memory) -> float:
        """Calculate association strength between two memories."""
        try:
            strength = 0.0

            # Tag similarity
            common_tags = set(memory1.tags) & set(memory2.tags)
            tag_similarity = len(common_tags) / max(len(memory1.tags), len(memory2.tags), 1)
            strength += tag_similarity * 0.4

            # Content similarity (simplified)
            content1_str = json.dumps(memory1.content).lower()
            content2_str = json.dumps(memory2.content).lower()

            # Simple word overlap
            words1 = set(content1_str.split())
            words2 = set(content2_str.split())
            word_overlap = len(words1 & words2) / max(len(words1), len(words2), 1)
            strength += word_overlap * 0.3

            # Temporal proximity
            time_diff = abs((memory1.created_at - memory2.created_at).total_seconds())
            temporal_similarity = max(0, 1 - time_diff / (24 * 3600))  # Decay over 24 hours
            strength += temporal_similarity * 0.2

            # Emotional similarity
            emotion_diff = abs(memory1.emotional_valence - memory2.emotional_valence)
            emotion_similarity = max(0, 1 - emotion_diff)
            strength += emotion_similarity * 0.1

            return min(strength, 1.0)

        except Exception as e:
            logger.error(f"Association strength calculation error: {e}")
            return 0.0

    def _memory_maintenance_loop(self):
        """Background memory maintenance loop."""
        while True:
            try:
                # Memory decay and cleanup
                self._decay_memories()

                # Consolidate memories
                self._consolidate_memories()

                # Update memory associations
                self._update_memory_associations()

                # Sleep for maintenance interval
                time.sleep(300)  # 5 minutes

            except Exception as e:
                logger.error(f"Memory maintenance error: {e}")
                time.sleep(60)

    def _decay_memories(self):
        """Apply memory decay to reduce importance over time."""
        try:
            current_time = datetime.now(timezone.utc)

            for memory_id, memory in list(self.memory_cache.items()):
                # Calculate time since last access
                time_diff = (current_time - memory.last_accessed).total_seconds()

                # Apply decay based on time and decay rate
                decay_factor = 1 - (memory.decay_rate * time_diff / 86400)  # Per day
                memory.confidence *= max(decay_factor, 0.1)  # Minimum 10% confidence

                # Remove very low confidence memories
                if memory.confidence < 0.1 and memory.importance.value < 3:
                    del self.memory_cache[memory_id]

        except Exception as e:
            logger.error(f"Memory decay error: {e}")

    def _consolidate_memories(self):
        """Consolidate similar memories to reduce redundancy."""
        try:
            # Find similar memories and merge them
            memories_to_consolidate = []

            for memory_id, memory in self.memory_cache.items():
                similar_memories = self._find_similar_memories(memory, limit=3)
                if len(similar_memories) >= 2:
                    memories_to_consolidate.append((memory, similar_memories))

            # Perform consolidation
            for main_memory, similar_memories in memories_to_consolidate:
                if len(similar_memories) >= 2:
                    # Merge similar memories into main memory
                    self._merge_memories(main_memory, similar_memories)

        except Exception as e:
            logger.error(f"Memory consolidation error: {e}")

    def _update_memory_associations(self):
        """Update memory associations based on recent access patterns."""
        try:
            # Update associations for recently accessed memories
            recent_memories = [m for m in self.memory_cache.values()
                             if (datetime.now(timezone.utc) - m.last_accessed).total_seconds() < 3600]

            for memory in recent_memories:
                self._create_memory_associations(memory)

        except Exception as e:
            logger.error(f"Memory association update error: {e}")

    def _merge_memories(self, main_memory: Memory, similar_memories: List[Memory]):
        """Merge similar memories into main memory."""
        try:
            # Combine content
            combined_content = main_memory.content.copy()

            for memory in similar_memories:
                for key, value in memory.content.items():
                    if key not in combined_content:
                        combined_content[key] = value

            # Update main memory
            main_memory.content = combined_content
            main_memory.confidence = min(main_memory.confidence * 1.1, 1.0)  # Boost confidence
            main_memory.access_count += sum(m.access_count for m in similar_memories)

            # Combine tags
            all_tags = set(main_memory.tags)
            for memory in similar_memories:
                all_tags.update(memory.tags)
            main_memory.tags = list(all_tags)

            # Remove similar memories
            for memory in similar_memories:
                if memory.memory_id in self.memory_cache:
                    del self.memory_cache[memory.memory_id]

        except Exception as e:
            logger.error(f"Memory merge error: {e}")

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text."""
        try:
            # Simple keyword extraction
            words = text.lower().split()
            # Filter out common words and short words
            keywords = [word for word in words if len(word) > 3 and
                       word not in ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use']]
            return keywords[:10]  # Top 10 keywords
        except Exception as e:
            logger.error(f"Keyword extraction error: {e}")
            return []

    def _store_association(self, source_id: str, target_id: str, relationship_type: str, strength: float):
        """Store association in knowledge graph."""
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO knowledge_graph VALUES (?, ?, ?, ?, ?)
            ''', (source_id, target_id, relationship_type, strength, datetime.now(timezone.utc).isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Association storage error: {e}")

    def _persist_learning_experience(self, experience: LearningExperience):
        """Persist learning experience to database."""
        try:
            conn = sqlite3.connect(self.memory_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO learning_experiences VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                experience.experience_id,
                experience.learning_type.value,
                json.dumps(experience.input_data),
                json.dumps(experience.output_data),
                experience.reward,
                experience.success,
                json.dumps(experience.lessons_learned),
                experience.timestamp.isoformat(),
                json.dumps(experience.context)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Learning experience persistence error: {e}")

    def _get_recent_important_memories(self, limit: int = 5) -> List[Memory]:
        """Get recent important memories."""
        try:
            recent_memories = [m for m in self.memory_cache.values()
                             if m.importance.value >= MemoryImportance.HIGH.value]

            # Sort by recency and importance
            sorted_memories = sorted(recent_memories,
                                   key=lambda m: (m.last_accessed, m.importance.value),
                                   reverse=True)

            return sorted_memories[:limit]

        except Exception as e:
            logger.error(f"Recent important memories error: {e}")
            return []