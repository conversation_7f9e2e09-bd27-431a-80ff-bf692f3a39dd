#!/usr/bin/env python3
"""
📊 ENHANCED DATA PIPELINE
Real-time data collection with advanced feature engineering
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any
import json
from advanced_feature_engineering import AdvancedFeatureEngineering

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("EnhancedDataPipeline")

class EnhancedDataPipeline:
    """Enhanced data pipeline with real APIs and advanced features"""
    
    def __init__(self):
        self.db_path = "enhanced_trading_data.db"
        self.feature_engineer = AdvancedFeatureEngineering()
        self.session = None
        self._initialize_database()
        
        # Major crypto pairs to track
        self.crypto_pairs = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
            'LINKUSDT', 'AVAXUSDT', 'MATICUSDT', 'UNIUSDT', 'LTCUSDT'
        ]
        
        # API endpoints
        self.apis = {
            'binance_ticker': 'https://api.binance.com/api/v3/ticker/24hr',
            'binance_klines': 'https://api.binance.com/api/v3/klines',
            'binance_depth': 'https://api.binance.com/api/v3/depth',
            'coingecko_prices': 'https://api.coingecko.com/api/v3/simple/price'
        }

    def _initialize_database(self):
        """Initialize enhanced database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Raw market data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS raw_market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume REAL,
                quote_volume REAL,
                trades_count INTEGER,
                source TEXT
            )
        ''')
        
        # Enhanced features
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                features_json TEXT,
                target_return REAL,
                data_quality_score REAL
            )
        ''')
        
        # Order book data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orderbook_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                bid_price REAL,
                bid_quantity REAL,
                ask_price REAL,
                ask_quantity REAL,
                spread REAL,
                depth_score REAL
            )
        ''')
        
        # Data quality metrics
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_quality (
                timestamp TEXT,
                total_records INTEGER,
                missing_data_pct REAL,
                outlier_count INTEGER,
                feature_count INTEGER,
                data_freshness_minutes REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Enhanced database initialized: {self.db_path}")

    async def collect_binance_klines(self, symbol: str, interval: str = '1h', limit: int = 100):
        """Collect historical kline data from Binance"""
        try:
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            async with self.session.get(self.apis['binance_klines'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    records_added = 0
                    for kline in data:
                        timestamp = datetime.fromtimestamp(kline[0] / 1000).isoformat()
                        
                        cursor.execute('''
                            INSERT OR REPLACE INTO raw_market_data 
                            (timestamp, symbol, open_price, high_price, low_price, close_price, 
                             volume, quote_volume, trades_count, source)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            timestamp, symbol,
                            float(kline[1]),  # open
                            float(kline[2]),  # high
                            float(kline[3]),  # low
                            float(kline[4]),  # close
                            float(kline[5]),  # volume
                            float(kline[7]),  # quote volume
                            int(kline[8]),    # trades count
                            'binance_klines'
                        ))
                        records_added += 1
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Collected {records_added} klines for {symbol}")
                    return records_added
                else:
                    logger.error(f"❌ Binance klines error: {response.status}")
                    return 0
                    
        except Exception as e:
            logger.error(f"❌ Klines collection error: {e}")
            return 0

    async def collect_orderbook_data(self, symbol: str, limit: int = 20):
        """Collect order book data"""
        try:
            params = {
                'symbol': symbol,
                'limit': limit
            }
            
            async with self.session.get(self.apis['binance_depth'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Calculate order book metrics
                    bids = data['bids']
                    asks = data['asks']
                    
                    if bids and asks:
                        best_bid_price = float(bids[0][0])
                        best_bid_qty = float(bids[0][1])
                        best_ask_price = float(asks[0][0])
                        best_ask_qty = float(asks[0][1])
                        
                        spread = best_ask_price - best_bid_price
                        spread_pct = (spread / best_bid_price) * 100
                        
                        # Calculate depth score (total volume in top 10 levels)
                        bid_depth = sum(float(bid[1]) for bid in bids[:10])
                        ask_depth = sum(float(ask[1]) for ask in asks[:10])
                        depth_score = min(bid_depth, ask_depth)
                        
                        # Save to database
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        
                        cursor.execute('''
                            INSERT INTO orderbook_data 
                            (timestamp, symbol, bid_price, bid_quantity, ask_price, ask_quantity, spread, depth_score)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            datetime.now().isoformat(), symbol,
                            best_bid_price, best_bid_qty,
                            best_ask_price, best_ask_qty,
                            spread_pct, depth_score
                        ))
                        
                        conn.commit()
                        conn.close()
                        
                        logger.info(f"✅ Order book for {symbol}: Spread {spread_pct:.4f}%, Depth {depth_score:.2f}")
                        return True
                    
        except Exception as e:
            logger.error(f"❌ Order book error: {e}")
            return False

    async def collect_real_time_data(self):
        """Collect real-time market data"""
        try:
            # Get 24hr ticker statistics
            async with self.session.get(self.apis['binance_ticker']) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    records_added = 0
                    for ticker in data:
                        if ticker['symbol'] in self.crypto_pairs:
                            cursor.execute('''
                                INSERT INTO raw_market_data 
                                (timestamp, symbol, open_price, high_price, low_price, close_price, 
                                 volume, quote_volume, trades_count, source)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                datetime.now().isoformat(),
                                ticker['symbol'],
                                float(ticker['openPrice']),
                                float(ticker['highPrice']),
                                float(ticker['lowPrice']),
                                float(ticker['lastPrice']),
                                float(ticker['volume']),
                                float(ticker['quoteVolume']),
                                int(ticker['count']),
                                'binance_ticker'
                            ))
                            records_added += 1
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Real-time data: {records_added} symbols updated")
                    return records_added
                    
        except Exception as e:
            logger.error(f"❌ Real-time data error: {e}")
            return 0

    def create_enhanced_features(self, lookback_hours: int = 24):
        """Create enhanced features from raw data"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Load recent raw data
            query = '''
                SELECT timestamp, symbol, open_price, high_price, low_price, close_price, volume
                FROM raw_market_data 
                WHERE timestamp > datetime('now', '-{} hours')
                ORDER BY symbol, timestamp
            '''.format(lookback_hours)
            
            df = pd.read_sql_query(query, conn)
            
            if len(df) == 0:
                logger.warning("No raw data available for feature engineering")
                return 0
            
            # Rename columns for feature engineering
            df = df.rename(columns={
                'close_price': 'price',
                'high_price': 'high_24h',
                'low_price': 'low_24h'
            })
            
            # Calculate price change
            df['change_24h'] = df.groupby('symbol')['price'].pct_change() * 100
            
            # Apply advanced feature engineering
            logger.info("🔧 Applying advanced feature engineering...")
            enhanced_df = self.feature_engineer.create_all_features(df)
            
            if enhanced_df is None or len(enhanced_df) == 0:
                logger.warning("Feature engineering failed")
                return 0
            
            # Calculate target returns (future price change)
            enhanced_df['target_return'] = enhanced_df.groupby('symbol')['price'].shift(-1).pct_change()
            
            # Calculate data quality score
            enhanced_df['data_quality_score'] = self._calculate_data_quality(enhanced_df)
            
            # Save enhanced features
            cursor = conn.cursor()
            features_saved = 0
            
            for _, row in enhanced_df.iterrows():
                if pd.notna(row['target_return']):
                    # Extract feature columns (exclude metadata)
                    feature_cols = [col for col in enhanced_df.columns 
                                  if col not in ['timestamp', 'symbol', 'target_return', 'data_quality_score']]
                    
                    features_dict = {col: float(row[col]) if pd.notna(row[col]) else None 
                                   for col in feature_cols}
                    
                    cursor.execute('''
                        INSERT INTO enhanced_features 
                        (timestamp, symbol, features_json, target_return, data_quality_score)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        row['timestamp'],
                        row['symbol'],
                        json.dumps(features_dict),
                        float(row['target_return']),
                        float(row['data_quality_score'])
                    ))
                    features_saved += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Enhanced features: {features_saved} records saved")
            logger.info(f"   Feature count: {len(feature_cols)}")
            logger.info(f"   Data quality: {enhanced_df['data_quality_score'].mean():.3f}")
            
            return features_saved
            
        except Exception as e:
            logger.error(f"❌ Feature engineering error: {e}")
            return 0

    def _calculate_data_quality(self, df: pd.DataFrame) -> pd.Series:
        """Calculate data quality score for each record"""
        # Count non-null values per row
        non_null_count = df.count(axis=1)
        total_features = len(df.columns)
        
        # Calculate completeness score (0-1)
        completeness = non_null_count / total_features
        
        # Detect outliers using IQR method
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        outlier_scores = []
        
        for _, row in df.iterrows():
            outlier_count = 0
            for col in numeric_cols:
                if pd.notna(row[col]):
                    col_data = df[col].dropna()
                    if len(col_data) > 10:
                        Q1 = col_data.quantile(0.25)
                        Q3 = col_data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        
                        if row[col] < lower_bound or row[col] > upper_bound:
                            outlier_count += 1
            
            outlier_score = 1 - (outlier_count / len(numeric_cols))
            outlier_scores.append(outlier_score)
        
        # Combine completeness and outlier scores
        quality_scores = (completeness + pd.Series(outlier_scores)) / 2
        return quality_scores

    def get_data_quality_metrics(self):
        """Get current data quality metrics"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Count total records
            total_records = pd.read_sql_query(
                "SELECT COUNT(*) as count FROM raw_market_data WHERE timestamp > datetime('now', '-24 hours')",
                conn
            ).iloc[0]['count']
            
            # Calculate missing data percentage
            missing_data = pd.read_sql_query('''
                SELECT 
                    AVG(CASE WHEN close_price IS NULL THEN 1 ELSE 0 END) * 100 as missing_pct
                FROM raw_market_data 
                WHERE timestamp > datetime('now', '-24 hours')
            ''', conn).iloc[0]['missing_pct']
            
            # Count enhanced features
            feature_count = pd.read_sql_query(
                "SELECT COUNT(*) as count FROM enhanced_features WHERE timestamp > datetime('now', '-24 hours')",
                conn
            ).iloc[0]['count']
            
            # Calculate data freshness
            latest_data = pd.read_sql_query(
                "SELECT MAX(timestamp) as latest FROM raw_market_data",
                conn
            ).iloc[0]['latest']
            
            if latest_data:
                latest_time = pd.to_datetime(latest_data)
                freshness_minutes = (datetime.now() - latest_time.replace(tzinfo=None)).total_seconds() / 60
            else:
                freshness_minutes = float('inf')
            
            conn.close()
            
            metrics = {
                'total_records': total_records,
                'missing_data_pct': missing_data or 0,
                'feature_count': feature_count,
                'data_freshness_minutes': freshness_minutes,
                'data_quality_score': max(0, 1 - (missing_data or 0)/100 - min(freshness_minutes/60, 1))
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Data quality metrics error: {e}")
            return {}

    async def run_enhanced_data_collection(self, duration_minutes: int = 30):
        """Run enhanced data collection pipeline"""
        logger.info(f"🚀 Starting enhanced data collection for {duration_minutes} minutes")
        
        self.session = aiohttp.ClientSession()
        
        try:
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            
            # Initial historical data collection
            logger.info("📊 Collecting historical data...")
            for symbol in self.crypto_pairs[:5]:  # Top 5 pairs
                await self.collect_binance_klines(symbol, '1h', 100)
                await asyncio.sleep(0.5)  # Rate limiting
            
            # Real-time collection loop
            iteration = 0
            while datetime.now() < end_time:
                iteration += 1
                logger.info(f"🔄 Collection iteration {iteration}")
                
                # Collect real-time data
                await self.collect_real_time_data()
                await asyncio.sleep(1)
                
                # Collect order book data
                for symbol in self.crypto_pairs[:3]:
                    await self.collect_orderbook_data(symbol)
                    await asyncio.sleep(0.5)
                
                # Create enhanced features every 5 iterations
                if iteration % 5 == 0:
                    logger.info("🔧 Creating enhanced features...")
                    features_created = self.create_enhanced_features()
                    
                    # Get data quality metrics
                    quality_metrics = self.get_data_quality_metrics()
                    logger.info(f"📊 Data quality: {quality_metrics.get('data_quality_score', 0):.3f}")
                
                # Wait before next iteration
                await asyncio.sleep(60)  # 1 minute intervals
                
        finally:
            await self.session.close()
        
        # Final summary
        final_metrics = self.get_data_quality_metrics()
        logger.info("✅ Enhanced data collection completed!")
        logger.info(f"📊 Final metrics: {final_metrics}")
        
        return final_metrics

async def main():
    """Main enhanced data pipeline demonstration"""
    print("📊 ENHANCED DATA PIPELINE WITH REAL APIs")
    print("=" * 60)
    
    pipeline = EnhancedDataPipeline()
    
    # Run enhanced data collection
    metrics = await pipeline.run_enhanced_data_collection(duration_minutes=5)
    
    print(f"\n📊 ENHANCED DATA COLLECTION RESULTS:")
    print(f"Total records: {metrics.get('total_records', 0)}")
    print(f"Enhanced features: {metrics.get('feature_count', 0)}")
    print(f"Data quality score: {metrics.get('data_quality_score', 0):.3f}")
    print(f"Data freshness: {metrics.get('data_freshness_minutes', 0):.1f} minutes")
    
    print(f"\n✅ Enhanced data pipeline demonstration completed!")
    print(f"Database: {pipeline.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
