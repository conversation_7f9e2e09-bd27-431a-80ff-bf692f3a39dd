"""
Comprehensive Integration Test Runner for NORYON V2

Automated test execution with detailed reporting, performance metrics,
and system validation for the complete AI trading system.
"""

import pytest
import asyncio
import json
import time
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import subprocess
import psutil

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class IntegrationTestRunner:
    """Comprehensive test runner for NORYON V2 integration tests."""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.system_metrics = {}
        self.start_time = None
        self.end_time = None
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests and generate comprehensive report."""
        print("🚀 Starting NORYON V2 Comprehensive Integration Tests")
        print("=" * 60)
        
        self.start_time = datetime.utcnow()
        
        # Test suites to run
        test_suites = [
            "test_component_integration.py",
            "test_realtime_data_flow.py", 
            "test_ai_agent_coordination.py",
            "test_api_endpoints.py",
            "test_database_integration.py",
            "test_error_recovery.py",
            "test_performance_load.py"
        ]
        
        # Run each test suite
        for suite in test_suites:
            print(f"\n📋 Running {suite}...")
            result = self._run_test_suite(suite)
            self.test_results[suite] = result
            
        self.end_time = datetime.utcnow()
        
        # Generate comprehensive report
        report = self._generate_report()
        self._save_report(report)
        self._print_summary(report)
        
        return report
    
    def _run_test_suite(self, suite_name: str) -> Dict[str, Any]:
        """Run a specific test suite and collect metrics."""
        suite_start = time.perf_counter()
        
        # Get system metrics before test
        pre_metrics = self._get_system_metrics()
        
        # Run pytest for the specific suite
        test_path = Path(__file__).parent / suite_name
        cmd = [
            sys.executable, "-m", "pytest", 
            str(test_path),
            "-v",
            "--tb=short",
            "--json-report",
            f"--json-report-file=/tmp/noryon_test_{suite_name}.json"
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per suite
            )
            
            suite_end = time.perf_counter()
            
            # Get system metrics after test
            post_metrics = self._get_system_metrics()
            
            # Parse results
            return {
                "suite_name": suite_name,
                "duration": suite_end - suite_start,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "success": result.returncode == 0,
                "pre_metrics": pre_metrics,
                "post_metrics": post_metrics,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except subprocess.TimeoutExpired:
            return {
                "suite_name": suite_name,
                "duration": 300,
                "return_code": -1,
                "stdout": "",
                "stderr": "Test suite timed out",
                "success": False,
                "error": "timeout",
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "suite_name": suite_name,
                "duration": 0,
                "return_code": -1,
                "stdout": "",
                "stderr": str(e),
                "success": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics."""
        try:
            process = psutil.Process()
            return {
                "memory_mb": process.memory_info().rss / 1024 / 1024,
                "cpu_percent": process.cpu_percent(),
                "threads": process.num_threads(),
                "open_files": len(process.open_files()),
                "connections": len(process.connections()),
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {"error": str(e)}
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_duration = (self.end_time - self.start_time).total_seconds()
        
        # Calculate overall statistics
        total_suites = len(self.test_results)
        successful_suites = sum(1 for r in self.test_results.values() if r["success"])
        failed_suites = total_suites - successful_suites
        
        # Performance analysis
        suite_durations = [r["duration"] for r in self.test_results.values()]
        avg_suite_duration = sum(suite_durations) / len(suite_durations) if suite_durations else 0
        
        # System resource analysis
        memory_usage = []
        cpu_usage = []
        
        for result in self.test_results.values():
            if "pre_metrics" in result and "memory_mb" in result["pre_metrics"]:
                memory_usage.append(result["pre_metrics"]["memory_mb"])
            if "post_metrics" in result and "memory_mb" in result["post_metrics"]:
                memory_usage.append(result["post_metrics"]["memory_mb"])
        
        report = {
            "test_run_info": {
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat(),
                "total_duration": total_duration,
                "noryon_version": "2.0.0",
                "python_version": sys.version,
                "platform": sys.platform
            },
            "summary": {
                "total_suites": total_suites,
                "successful_suites": successful_suites,
                "failed_suites": failed_suites,
                "success_rate": successful_suites / total_suites if total_suites > 0 else 0,
                "avg_suite_duration": avg_suite_duration
            },
            "suite_results": self.test_results,
            "performance_analysis": {
                "total_test_time": total_duration,
                "avg_memory_usage": sum(memory_usage) / len(memory_usage) if memory_usage else 0,
                "max_memory_usage": max(memory_usage) if memory_usage else 0,
                "suite_durations": {
                    suite: result["duration"] 
                    for suite, result in self.test_results.items()
                }
            },
            "system_validation": self._validate_system_health(),
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _validate_system_health(self) -> Dict[str, Any]:
        """Validate overall system health based on test results."""
        validation = {
            "component_integration": "UNKNOWN",
            "realtime_performance": "UNKNOWN", 
            "ai_coordination": "UNKNOWN",
            "api_reliability": "UNKNOWN",
            "database_consistency": "UNKNOWN",
            "error_recovery": "UNKNOWN",
            "load_handling": "UNKNOWN",
            "overall_health": "UNKNOWN"
        }
        
        # Map test suites to health categories
        suite_mapping = {
            "test_component_integration.py": "component_integration",
            "test_realtime_data_flow.py": "realtime_performance",
            "test_ai_agent_coordination.py": "ai_coordination", 
            "test_api_endpoints.py": "api_reliability",
            "test_database_integration.py": "database_consistency",
            "test_error_recovery.py": "error_recovery",
            "test_performance_load.py": "load_handling"
        }
        
        # Evaluate each category
        for suite, category in suite_mapping.items():
            if suite in self.test_results:
                result = self.test_results[suite]
                if result["success"]:
                    validation[category] = "HEALTHY"
                else:
                    validation[category] = "ISSUES_DETECTED"
            else:
                validation[category] = "NOT_TESTED"
        
        # Determine overall health
        healthy_count = sum(1 for status in validation.values() if status == "HEALTHY")
        total_count = len([k for k in validation.keys() if k != "overall_health"])
        
        if healthy_count == total_count:
            validation["overall_health"] = "EXCELLENT"
        elif healthy_count >= total_count * 0.8:
            validation["overall_health"] = "GOOD"
        elif healthy_count >= total_count * 0.6:
            validation["overall_health"] = "FAIR"
        else:
            validation["overall_health"] = "POOR"
        
        return validation
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Check for failed test suites
        failed_suites = [
            suite for suite, result in self.test_results.items() 
            if not result["success"]
        ]
        
        if failed_suites:
            recommendations.append(
                f"❌ Failed test suites detected: {', '.join(failed_suites)}. "
                "Review error logs and fix underlying issues."
            )
        
        # Check performance
        slow_suites = [
            suite for suite, result in self.test_results.items()
            if result["duration"] > 60  # > 1 minute
        ]
        
        if slow_suites:
            recommendations.append(
                f"⚠️ Slow test suites detected: {', '.join(slow_suites)}. "
                "Consider optimizing performance or increasing timeouts."
            )
        
        # Check memory usage
        high_memory_suites = []
        for suite, result in self.test_results.items():
            if ("post_metrics" in result and "memory_mb" in result["post_metrics"] and
                result["post_metrics"]["memory_mb"] > 500):  # > 500MB
                high_memory_suites.append(suite)
        
        if high_memory_suites:
            recommendations.append(
                f"💾 High memory usage detected in: {', '.join(high_memory_suites)}. "
                "Review memory management and potential leaks."
            )
        
        # Success recommendations
        if not failed_suites:
            recommendations.append(
                "✅ All test suites passed! System is ready for production deployment."
            )
            recommendations.append(
                "🚀 Consider running additional stress tests before full production load."
            )
        
        return recommendations
    
    def _save_report(self, report: Dict[str, Any]):
        """Save test report to file."""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        report_file = f"noryon_integration_test_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"\n📄 Test report saved to: {report_file}")
        except Exception as e:
            print(f"\n❌ Failed to save report: {e}")
    
    def _print_summary(self, report: Dict[str, Any]):
        """Print test summary to console."""
        print("\n" + "=" * 60)
        print("🎯 NORYON V2 INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        summary = report["summary"]
        print(f"📊 Total Test Suites: {summary['total_suites']}")
        print(f"✅ Successful: {summary['successful_suites']}")
        print(f"❌ Failed: {summary['failed_suites']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1%}")
        print(f"⏱️ Total Duration: {report['test_run_info']['total_duration']:.1f}s")
        
        print("\n🏥 SYSTEM HEALTH VALIDATION:")
        validation = report["system_validation"]
        for category, status in validation.items():
            if category != "overall_health":
                emoji = "✅" if status == "HEALTHY" else "❌" if status == "ISSUES_DETECTED" else "❓"
                print(f"  {emoji} {category.replace('_', ' ').title()}: {status}")
        
        print(f"\n🎯 Overall Health: {validation['overall_health']}")
        
        print("\n💡 RECOMMENDATIONS:")
        for rec in report["recommendations"]:
            print(f"  {rec}")
        
        print("\n" + "=" * 60)


def main():
    """Main entry point for integration test runner."""
    runner = IntegrationTestRunner()
    report = runner.run_all_tests()
    
    # Exit with appropriate code
    if report["summary"]["failed_suites"] == 0:
        print("🎉 All integration tests passed!")
        sys.exit(0)
    else:
        print("💥 Some integration tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
