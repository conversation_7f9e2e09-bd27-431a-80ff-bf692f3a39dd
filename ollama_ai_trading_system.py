#!/usr/bin/env python3
"""
🤖 OLLAMA AI TRADING SYSTEM
Real integration with your 9 Ollama AI models for trading decisions
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import threading
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OllamaAITradingSystem")

@dataclass
class OllamaAIAgent:
    """Ollama AI Agent with specific model"""
    model_name: str
    agent_id: str
    initial_balance: float
    risk_tolerance: float
    confidence_threshold: float
    max_position_size: float
    personality: str
    active: bool = True

class OllamaAITradingSystem:
    """Trading system using your 9 Ollama AI models"""
    
    def __init__(self):
        self.db_path = "ollama_ai_trading_system.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # Your 9 Ollama AI models
        self.ollama_models = {
            'marco-o1:7b': 'Analytical reasoning specialist',
            'magistral:24b': 'Large context strategic planner', 
            'command-r:35b': 'Command and control decision maker',
            'cogito:32b': 'Deep thinking philosopher trader',
            'gemma3:27b': 'Google advanced reasoning model',
            'mistral-small:24b': 'Efficient European AI trader',
            'falcon3:10b': 'UAE falcon-fast decision maker',
            'granite3.3:8b': 'IBM enterprise-grade trader',
            'qwen3:32b': 'Chinese AI with global perspective',
            'deepseek-r1:latest': 'Deep reasoning and reflection'
        }
        
        # Initialize Ollama AI agents
        self.ollama_agents = {}
        self.agent_performance = {}
        self.agent_conversations = {}
        
        # Market data
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        self.market_data = {}
        
        self._initialize_database()
        self._initialize_ollama_agents()

    def _initialize_database(self):
        """Initialize database for Ollama AI trading"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Ollama AI agents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ollama_agents (
                agent_id TEXT PRIMARY KEY,
                model_name TEXT,
                initial_balance REAL,
                current_balance REAL,
                total_return REAL,
                risk_tolerance REAL,
                confidence_threshold REAL,
                max_position_size REAL,
                personality TEXT,
                active BOOLEAN,
                created_at TEXT,
                last_updated TEXT
            )
        ''')
        
        # AI conversations and decisions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                prompt TEXT,
                response TEXT,
                decision TEXT,
                confidence REAL,
                reasoning TEXT
            )
        ''')
        
        # Trading decisions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_trading_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                confidence REAL,
                ai_reasoning TEXT,
                executed BOOLEAN
            )
        ''')
        
        # Market analysis
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_market_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                market_sentiment TEXT,
                trend_analysis TEXT,
                risk_assessment TEXT,
                recommendations TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Ollama AI database initialized: {self.db_path}")

    def _initialize_ollama_agents(self):
        """Initialize Ollama AI agents with your models"""
        agent_configs = [
            # Analytical and reasoning models
            OllamaAIAgent("marco-o1:7b", "marco_analyst", 15000.0, 0.02, 0.7, 0.1, "Analytical reasoning specialist"),
            OllamaAIAgent("cogito:32b", "cogito_philosopher", 20000.0, 0.015, 0.75, 0.08, "Deep thinking philosopher"),
            OllamaAIAgent("deepseek-r1:latest", "deepseek_reflector", 18000.0, 0.025, 0.65, 0.12, "Deep reasoning and reflection"),
            
            # Large context strategic models
            OllamaAIAgent("magistral:24b", "magistral_strategist", 25000.0, 0.02, 0.6, 0.15, "Strategic planner"),
            OllamaAIAgent("command-r:35b", "command_controller", 30000.0, 0.03, 0.55, 0.18, "Command and control"),
            OllamaAIAgent("gemma3:27b", "gemma_advisor", 22000.0, 0.02, 0.65, 0.12, "Google advanced reasoning"),
            
            # Efficient and specialized models
            OllamaAIAgent("mistral-small:24b", "mistral_trader", 16000.0, 0.025, 0.6, 0.14, "European efficiency"),
            OllamaAIAgent("falcon3:10b", "falcon_speed", 14000.0, 0.04, 0.5, 0.2, "Fast decision maker"),
            OllamaAIAgent("granite3.3:8b", "granite_enterprise", 19000.0, 0.015, 0.7, 0.1, "Enterprise-grade stability"),
            OllamaAIAgent("qwen3:32b", "qwen_global", 21000.0, 0.02, 0.6, 0.13, "Global perspective")
        ]
        
        for agent in agent_configs:
            self.ollama_agents[agent.agent_id] = agent
            self.agent_performance[agent.agent_id] = {
                'portfolio_value': agent.initial_balance,
                'cash': agent.initial_balance,
                'positions': {},
                'trades': [],
                'ai_decisions': [],
                'conversations': []
            }
            self.agent_conversations[agent.agent_id] = []
        
        # Save to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for agent in agent_configs:
            cursor.execute('''
                INSERT OR REPLACE INTO ollama_agents 
                (agent_id, model_name, initial_balance, current_balance, total_return,
                 risk_tolerance, confidence_threshold, max_position_size, personality,
                 active, created_at, last_updated)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                agent.agent_id, agent.model_name, agent.initial_balance, agent.initial_balance, 0.0,
                agent.risk_tolerance, agent.confidence_threshold, agent.max_position_size,
                agent.personality, agent.active, datetime.now().isoformat(), datetime.now().isoformat()
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Initialized {len(self.ollama_agents)} Ollama AI agents")

    async def call_ollama_model(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Call specific Ollama model with prompt"""
        try:
            url = f"{self.ollama_url}/api/generate"
            
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 500
                }
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': model_name,
                        'prompt_tokens': result.get('prompt_eval_count', 0),
                        'completion_tokens': result.get('eval_count', 0)
                    }
                else:
                    logger.error(f"❌ Ollama API error {response.status} for {model_name}")
                    return {'success': False, 'error': f"API error {response.status}"}
                    
        except Exception as e:
            logger.error(f"❌ Error calling {model_name}: {e}")
            return {'success': False, 'error': str(e)}

    def create_trading_prompt(self, agent: OllamaAIAgent, market_data: Dict[str, Any]) -> str:
        """Create trading prompt for Ollama AI"""
        
        # Get recent performance
        performance = self.agent_performance[agent.agent_id]
        portfolio_value = performance['portfolio_value']
        total_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100
        
        prompt = f"""You are {agent.personality}, an AI trading agent using the {agent.model_name} model.

CURRENT PORTFOLIO:
- Portfolio Value: ${portfolio_value:,.2f}
- Total Return: {total_return:+.2f}%
- Cash Available: ${performance['cash']:,.2f}
- Active Positions: {len(performance['positions'])}

CURRENT MARKET DATA:
"""
        
        for symbol, data in market_data.items():
            prompt += f"- {symbol}: ${data['price']:.4f} ({data['change_24h']:+.2f}%)\n"
        
        prompt += f"""
TRADING PARAMETERS:
- Risk Tolerance: {agent.risk_tolerance*100:.1f}%
- Confidence Threshold: {agent.confidence_threshold*100:.0f}%
- Max Position Size: {agent.max_position_size*100:.0f}%

INSTRUCTIONS:
Analyze the market data and make a trading decision. Consider:
1. Current market trends and momentum
2. Risk/reward ratios
3. Portfolio diversification
4. Your personality as {agent.personality}

Respond in this EXACT format:
DECISION: [BUY/SELL/HOLD]
SYMBOL: [crypto symbol or NONE]
CONFIDENCE: [0-100]
REASONING: [your analysis in 2-3 sentences]

Be decisive and specific. Use your {agent.model_name} capabilities for analysis."""

        return prompt

    async def get_ai_trading_decisions(self, market_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Get trading decisions from all Ollama AI agents"""
        decisions = {}
        
        for agent_id, agent in self.ollama_agents.items():
            if not agent.active:
                continue
            
            try:
                # Create trading prompt
                prompt = self.create_trading_prompt(agent, market_data)
                
                # Call Ollama model
                logger.info(f"🤖 Consulting {agent.model_name} ({agent_id})...")
                result = await self.call_ollama_model(agent.model_name, prompt)
                
                if result['success']:
                    response = result['response']
                    
                    # Parse AI response
                    decision = self._parse_ai_response(response)
                    
                    if decision:
                        decisions[agent_id] = {
                            'agent': agent,
                            'decision': decision,
                            'raw_response': response,
                            'model_name': agent.model_name,
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        # Save conversation
                        self._save_ai_conversation(agent_id, agent.model_name, prompt, response, decision)
                        
                        logger.info(f"✅ {agent.model_name}: {decision['action']} {decision.get('symbol', '')} "
                                  f"(Confidence: {decision['confidence']}%)")
                    else:
                        logger.warning(f"⚠️ Could not parse response from {agent.model_name}")
                else:
                    logger.error(f"❌ Failed to get response from {agent.model_name}")
                
                # Small delay between calls
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Error getting decision from {agent_id}: {e}")
        
        return decisions

    def _parse_ai_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse AI response into structured decision"""
        try:
            lines = response.strip().split('\n')
            decision = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('DECISION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 50
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
            
            # Validate decision
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing AI response: {e}")
            return None

    def _save_ai_conversation(self, agent_id: str, model_name: str, prompt: str, response: str, decision: Dict[str, Any]):
        """Save AI conversation to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO ai_conversations 
                (timestamp, agent_id, model_name, prompt, response, decision, confidence, reasoning)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(), agent_id, model_name, prompt, response,
                decision.get('action', 'UNKNOWN'), decision.get('confidence', 0),
                decision.get('reasoning', 'No reasoning provided')
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error saving conversation: {e}")

    async def collect_market_data(self):
        """Collect market data for AI analysis"""
        try:
            # Use CoinGecko API for real data
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': 'bitcoin,ethereum,cardano,solana,polkadot',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Map to our symbols
                    symbol_map = {
                        'bitcoin': 'BTCUSDT',
                        'ethereum': 'ETHUSDT',
                        'cardano': 'ADAUSDT',
                        'solana': 'SOLUSDT',
                        'polkadot': 'DOTUSDT'
                    }
                    
                    market_data = {}
                    for coin_id, coin_data in data.items():
                        if coin_id in symbol_map:
                            symbol = symbol_map[coin_id]
                            market_data[symbol] = {
                                'price': coin_data['usd'],
                                'volume': coin_data.get('usd_24h_vol', 0),
                                'change_24h': coin_data.get('usd_24h_change', 0),
                                'timestamp': datetime.now().isoformat()
                            }
                    
                    self.market_data = market_data
                    logger.info(f"✅ Collected market data for {len(market_data)} symbols")
                    return market_data
                else:
                    logger.error(f"❌ Market data API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"❌ Error collecting market data: {e}")
            return {}

    def display_ollama_ai_status(self):
        """Display Ollama AI system status"""
        print(f"\n🤖 OLLAMA AI TRADING SYSTEM STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        total_portfolio = sum(perf['portfolio_value'] for perf in self.agent_performance.values())
        total_initial = sum(agent.initial_balance for agent in self.ollama_agents.values())
        total_return = (total_portfolio - total_initial) / total_initial * 100 if total_initial > 0 else 0
        
        print(f"📊 SYSTEM METRICS:")
        print(f"   Total Portfolio Value: ${total_portfolio:,.2f}")
        print(f"   Total Return: {total_return:+.2f}%")
        print(f"   Active AI Agents: {len([a for a in self.ollama_agents.values() if a.active])}")
        
        print(f"\n🤖 OLLAMA AI AGENTS:")
        for agent_id, agent in self.ollama_agents.items():
            performance = self.agent_performance[agent_id]
            agent_return = (performance['portfolio_value'] - agent.initial_balance) / agent.initial_balance * 100
            
            print(f"   {agent.model_name:20} | ${performance['portfolio_value']:8,.2f} | {agent_return:+6.2f}% | "
                  f"{len(performance['trades']):3d} trades | {agent.personality}")
        
        if self.market_data:
            print(f"\n📈 CURRENT MARKET DATA:")
            for symbol, data in self.market_data.items():
                print(f"   {symbol:10} | ${data['price']:8.4f} | {data['change_24h']:+6.2f}%")
        
        print("=" * 80)

    async def run_ollama_ai_trading_system(self, duration_minutes: int = 60):
        """Run Ollama AI trading system"""
        logger.info(f"🚀 Starting Ollama AI trading system for {duration_minutes} minutes")
        logger.info(f"   Using {len(self.ollama_agents)} Ollama AI models")
        
        self.session = aiohttp.ClientSession()
        
        try:
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0
            
            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Ollama AI cycle {cycle_count}")
                
                # Collect market data
                market_data = await self.collect_market_data()
                
                if market_data:
                    # Get AI decisions from all Ollama models
                    ai_decisions = await self.get_ai_trading_decisions(market_data)
                    
                    if ai_decisions:
                        logger.info(f"🧠 Received {len(ai_decisions)} AI decisions")
                        
                        # Display decisions
                        print(f"\n🤖 OLLAMA AI DECISIONS:")
                        for agent_id, decision_data in ai_decisions.items():
                            decision = decision_data['decision']
                            agent = decision_data['agent']
                            print(f"   {agent.model_name:20} | {decision['action']:4} | "
                                  f"{decision.get('symbol', 'N/A'):8} | {decision['confidence']:3.0f}% | "
                                  f"{decision.get('reasoning', 'No reasoning')[:50]}...")
                    
                    # Display status every 3 cycles
                    if cycle_count % 3 == 0:
                        self.display_ollama_ai_status()
                
                # Wait before next cycle
                await asyncio.sleep(120)  # 2 minutes between cycles
            
            # Final status
            print(f"\n🏁 OLLAMA AI TRADING SYSTEM COMPLETED")
            print(f"Total cycles: {cycle_count}")
            self.display_ollama_ai_status()
            
        finally:
            await self.session.close()

async def main():
    """Main Ollama AI trading system demonstration"""
    print("🤖 OLLAMA AI TRADING SYSTEM")
    print("=" * 60)
    print("Real integration with your 9 Ollama AI models:")
    print("• marco-o1:7b - Analytical reasoning specialist")
    print("• magistral:24b - Strategic planner")
    print("• command-r:35b - Command and control")
    print("• cogito:32b - Deep thinking philosopher")
    print("• gemma3:27b - Google advanced reasoning")
    print("• mistral-small:24b - European efficiency")
    print("• falcon3:10b - Fast decision maker")
    print("• granite3.3:8b - Enterprise stability")
    print("• qwen3:32b - Global perspective")
    print("• deepseek-r1:latest - Deep reasoning")
    print("=" * 60)
    
    # Create Ollama AI system
    system = OllamaAITradingSystem()
    
    # Run Ollama AI trading
    await system.run_ollama_ai_trading_system(duration_minutes=30)
    
    print(f"\n✅ Ollama AI trading system demonstration completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
