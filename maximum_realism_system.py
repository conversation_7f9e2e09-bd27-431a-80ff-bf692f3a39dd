#!/usr/bin/env python3
"""
🚀 MAXIMUM REALISM SYSTEM
Working system with maximum realism and all your Ollama models
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("MaximumRealismSystem")

@dataclass
class MaxRealismAgent:
    """Maximum realism AI agent"""
    model_name: str
    agent_id: str
    initial_balance: float
    current_balance: float
    risk_tolerance: float
    confidence_threshold: float
    max_position_size: float
    personality: str
    trading_style: str
    model_family: str
    trades_executed: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    active: bool = True

class MaximumRealismSystem:
    """Maximum realism trading system"""
    
    def __init__(self):
        self.db_path = "maximum_realism_system.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # ALL YOUR OLLAMA MODELS (including new ones you added)
        self.all_ollama_models = [
            # Original proven models
            {'name': 'marco-o1:7b', 'family': 'marco', 'personality': 'Analytical reasoning specialist', 'style': 'technical_analysis'},
            {'name': 'magistral:24b', 'family': 'magistral', 'personality': 'Strategic planning expert', 'style': 'strategic_long_term'},
            {'name': 'command-r:35b', 'family': 'command', 'personality': 'Command and control specialist', 'style': 'systematic_trading'},
            {'name': 'cogito:32b', 'family': 'cogito', 'personality': 'Philosophical thinking expert', 'style': 'fundamental_analysis'},
            {'name': 'gemma3:27b', 'family': 'gemma', 'personality': 'Google advanced reasoning', 'style': 'ml_driven_trading'},
            {'name': 'mistral-small:24b', 'family': 'mistral', 'personality': 'European efficiency specialist', 'style': 'risk_managed_trading'},
            {'name': 'falcon3:10b', 'family': 'falcon', 'personality': 'High-speed decision maker', 'style': 'high_frequency_trading'},
            {'name': 'granite3.3:8b', 'family': 'granite', 'personality': 'Enterprise stability expert', 'style': 'conservative_trading'},
            {'name': 'qwen3:32b', 'family': 'qwen', 'personality': 'Global perspective specialist', 'style': 'global_arbitrage'},
            {'name': 'deepseek-r1:latest', 'family': 'deepseek', 'personality': 'Deep reasoning expert', 'style': 'contrarian_trading'},
            
            # NEW MODELS YOU LIKELY ADDED
            {'name': 'llama3.3:70b', 'family': 'llama', 'personality': 'Massive general intelligence', 'style': 'balanced_trading'},
            {'name': 'qwen2.5:32b', 'family': 'qwen', 'personality': 'Advanced multilingual AI', 'style': 'global_arbitrage'},
            {'name': 'phi4:14b', 'family': 'phi', 'personality': 'Microsoft efficient reasoning', 'style': 'momentum_trading'},
            {'name': 'nemotron:70b', 'family': 'nemotron', 'personality': 'NVIDIA advanced reasoning', 'style': 'pattern_trading'},
            {'name': 'llama3.2:90b', 'family': 'llama', 'personality': 'Ultra-massive intelligence', 'style': 'balanced_trading'},
            
            # ADDITIONAL POTENTIAL NEW MODELS
            {'name': 'deepseek-r1:32b', 'family': 'deepseek', 'personality': 'Reasoning and reflection', 'style': 'contrarian_trading'},
            {'name': 'qwen2.5:72b', 'family': 'qwen', 'personality': 'Massive multilingual', 'style': 'global_arbitrage'},
            {'name': 'mistral-large:123b', 'family': 'mistral', 'personality': 'European excellence', 'style': 'risk_managed_trading'},
            {'name': 'gemma3:70b', 'family': 'gemma', 'personality': 'Google massive reasoning', 'style': 'ml_driven_trading'},
            {'name': 'phi4:32b', 'family': 'phi', 'personality': 'Microsoft advanced reasoning', 'style': 'momentum_trading'}
        ]
        
        # Active agents
        self.active_agents = {}
        self.agent_portfolios = {}
        
        # Market simulation
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'AVAXUSDT']
        self.current_prices = {}
        self.price_history = {}
        
        # System state
        self.total_trades = 0
        self.system_start_time = None
        
        self._initialize_database()

    def _initialize_database(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS max_realism_agents (
                agent_id TEXT PRIMARY KEY,
                model_name TEXT,
                model_family TEXT,
                initial_balance REAL,
                current_balance REAL,
                total_pnl REAL,
                trades_executed INTEGER,
                win_rate REAL,
                personality TEXT,
                trading_style TEXT,
                created_at TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS max_realism_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                value REAL,
                confidence REAL,
                reasoning TEXT,
                pnl REAL,
                success BOOLEAN
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS max_realism_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                prompt TEXT,
                response TEXT,
                decision TEXT,
                confidence REAL,
                executed BOOLEAN
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Database initialized: {self.db_path}")

    def initialize_agents(self):
        """Initialize all agents"""
        logger.info(f"🤖 Initializing {len(self.all_ollama_models)} maximum realism agents...")
        
        for model_config in self.all_ollama_models:
            try:
                # Create agent with realistic configuration
                balance = random.uniform(15000, 50000)
                risk = random.uniform(0.01, 0.04)
                confidence = random.uniform(0.5, 0.8)
                position = random.uniform(0.1, 0.25)
                
                agent_id = f"{model_config['name'].replace(':', '_').replace('.', '_').replace('-', '_')}_max_realism"
                
                agent = MaxRealismAgent(
                    model_name=model_config['name'],
                    agent_id=agent_id,
                    initial_balance=balance,
                    current_balance=balance,
                    risk_tolerance=risk,
                    confidence_threshold=confidence,
                    max_position_size=position,
                    personality=model_config['personality'],
                    trading_style=model_config['style'],
                    model_family=model_config['family']
                )
                
                self.active_agents[agent_id] = agent
                self.agent_portfolios[agent_id] = {
                    'cash': balance,
                    'positions': {},
                    'trades': []
                }
                
                logger.info(f"✅ {model_config['name']} → {model_config['style']} (${balance:,.0f})")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize {model_config['name']}: {e}")
        
        logger.info(f"🚀 Initialized {len(self.active_agents)} maximum realism agents")

    def simulate_realistic_market(self) -> Dict[str, Dict[str, Any]]:
        """Simulate realistic market with advanced features"""
        market_data = {}
        
        # Realistic base prices
        base_prices = {
            'BTCUSDT': 97234.0, 'ETHUSDT': 3345.0, 'ADAUSDT': 0.89, 'SOLUSDT': 189.0,
            'DOTUSDT': 7.12, 'LINKUSDT': 22.45, 'AVAXUSDT': 38.67
        }
        
        # Market conditions
        market_conditions = ['bull', 'bear', 'sideways', 'volatile']
        current_condition = random.choice(market_conditions)
        
        for symbol in self.crypto_pairs:
            if symbol not in self.price_history:
                self.price_history[symbol] = [base_prices[symbol]]
            
            last_price = self.price_history[symbol][-1]
            
            # Realistic price movement
            if current_condition == 'bull':
                trend = 0.002
                volatility = 0.015
            elif current_condition == 'bear':
                trend = -0.002
                volatility = 0.02
            elif current_condition == 'volatile':
                trend = 0.0
                volatility = 0.04
            else:  # sideways
                trend = 0.0
                volatility = 0.01
            
            # Price calculation
            random_change = np.random.normal(trend, volatility)
            new_price = last_price * (1 + random_change)
            
            # Update history
            self.price_history[symbol].append(new_price)
            if len(self.price_history[symbol]) > 50:
                self.price_history[symbol] = self.price_history[symbol][-25:]
            
            self.current_prices[symbol] = new_price
            
            # Calculate indicators
            prices = np.array(self.price_history[symbol])
            
            # RSI
            if len(prices) >= 14:
                deltas = np.diff(prices)
                gains = deltas[deltas > 0]
                losses = -deltas[deltas < 0]
                avg_gain = np.mean(gains) if len(gains) > 0 else 0.001
                avg_loss = np.mean(losses) if len(losses) > 0 else 0.001
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50
            
            # Moving averages
            sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else new_price
            sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else new_price
            
            market_data[symbol] = {
                'price': new_price,
                'change_24h': random_change * 100,
                'volume': random.uniform(1000000, 20000000),
                'rsi': rsi,
                'sma_10': sma_10,
                'sma_20': sma_20,
                'volatility': abs(random_change),
                'market_condition': current_condition,
                'timestamp': datetime.now().isoformat()
            }
        
        return market_data

    def create_realistic_prompt(self, agent: MaxRealismAgent, market_data: Dict[str, Any]) -> str:
        """Create realistic trading prompt"""
        portfolio = self.agent_portfolios[agent.agent_id]
        
        # Portfolio value
        positions_value = sum(
            pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
            for symbol, pos in portfolio['positions'].items()
        )
        total_value = portfolio['cash'] + positions_value
        total_return = (total_value - agent.initial_balance) / agent.initial_balance * 100
        
        prompt = f"""You are {agent.personality}, an elite AI trader using {agent.model_name}.

TRADING PROFILE:
- Model Family: {agent.model_family.upper()}
- Trading Style: {agent.trading_style}
- Specialization: {agent.personality}

PORTFOLIO STATUS:
- Total Value: ${total_value:,.2f}
- Cash: ${portfolio['cash']:,.2f}
- Positions Value: ${positions_value:,.2f}
- Total Return: {total_return:+.2f}%
- Trades Executed: {agent.trades_executed}

CURRENT POSITIONS:
"""
        
        if portfolio['positions']:
            for symbol, pos in portfolio['positions'].items():
                current_price = self.current_prices.get(symbol, pos['avg_price'])
                unrealized_pnl = (current_price - pos['avg_price']) * pos['quantity']
                prompt += f"- {symbol}: {pos['quantity']:.4f} @ ${pos['avg_price']:.2f} → ${current_price:.2f} (P&L: ${unrealized_pnl:+.2f})\n"
        else:
            prompt += "- No current positions\n"
        
        prompt += f"\nMARKET ANALYSIS:\n"
        for symbol, data in market_data.items():
            trend_emoji = "📈" if data['change_24h'] > 0 else "📉"
            rsi_signal = "Overbought" if data['rsi'] > 70 else "Oversold" if data['rsi'] < 30 else "Neutral"
            
            prompt += f"- {symbol}: ${data['price']:.4f} {trend_emoji} {data['change_24h']:+.2f}% | RSI: {data['rsi']:.1f} ({rsi_signal})\n"
        
        prompt += f"""
MARKET CONDITIONS: {market_data[list(market_data.keys())[0]]['market_condition'].upper()}

TRADING PARAMETERS:
- Risk Tolerance: {agent.risk_tolerance*100:.1f}%
- Confidence Threshold: {agent.confidence_threshold*100:.0f}%
- Max Position: {agent.max_position_size*100:.0f}%
- Available Cash: ${portfolio['cash']:,.2f}

DECISION REQUIRED:
As a {agent.trading_style} specialist, make your trading decision.

RESPONSE FORMAT:
DECISION: [BUY/SELL/HOLD]
SYMBOL: [crypto symbol or NONE]
AMOUNT: [dollar amount or NONE]
CONFIDENCE: [0-100]
REASONING: [Your analysis in 2-3 sentences]

Be decisive and use your {agent.model_name} expertise."""

        return prompt

    async def call_ollama_model(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Call Ollama model"""
        try:
            url = f"{self.ollama_url}/api/generate"
            
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.2,
                    "top_p": 0.9,
                    "max_tokens": 300
                }
            }
            
            async with self.session.post(url, json=payload, timeout=90) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': model_name
                    }
                else:
                    return {'success': False, 'error': f"HTTP {response.status}"}
                    
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def parse_decision(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse AI decision"""
        try:
            lines = response.strip().split('\n')
            decision = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('DECISION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('AMOUNT:'):
                    amount_str = line.split(':', 1)[1].strip()
                    if amount_str != 'NONE':
                        try:
                            amount_clean = ''.join(c for c in amount_str if c.isdigit() or c == '.')
                            decision['amount'] = float(amount_clean) if amount_clean else 0
                        except:
                            decision['amount'] = 0
                    else:
                        decision['amount'] = 0
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 50
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
            
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing decision: {e}")
            return None

    async def execute_trading_session(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute trading session with all agents"""
        session_results = {
            'decisions': {},
            'trades_executed': 0,
            'successful_calls': 0,
            'failed_calls': 0
        }

        logger.info(f"🚀 Starting trading session with {len(self.active_agents)} agents")

        for agent_id, agent in self.active_agents.items():
            if not agent.active:
                continue

            try:
                logger.info(f"🤖 Consulting {agent.model_name} ({agent.model_family})...")

                # Create prompt
                prompt = self.create_realistic_prompt(agent, market_data)

                # Call model
                result = await self.call_ollama_model(agent.model_name, prompt)

                if result['success']:
                    # Parse decision
                    decision = self.parse_decision(result['response'])

                    if decision:
                        # Execute trade if not HOLD
                        trade_executed = False
                        if decision['action'] != 'HOLD' and decision.get('symbol'):
                            trade_executed = self.execute_trade(agent_id, decision, market_data)

                            if trade_executed:
                                session_results['trades_executed'] += 1

                        # Save decision
                        self.save_decision(agent_id, prompt, result['response'], decision, trade_executed)

                        session_results['decisions'][agent_id] = {
                            'agent': agent,
                            'decision': decision,
                            'trade_executed': trade_executed
                        }

                        session_results['successful_calls'] += 1

                        # Log decision
                        action_emoji = "🟢" if decision['action'] == 'BUY' else "🔴" if decision['action'] == 'SELL' else "⚪"
                        logger.info(f"   {action_emoji} {agent.model_name}: {decision['action']} "
                                  f"{decision.get('symbol', 'N/A')} (Confidence: {decision.get('confidence', 0):.0f}%)")

                        if trade_executed:
                            logger.info(f"      💰 Trade executed: ${decision.get('amount', 0):,.0f}")
                    else:
                        logger.warning(f"   ⚠️ {agent.model_name}: Could not parse decision")
                        session_results['failed_calls'] += 1
                else:
                    logger.error(f"   ❌ {agent.model_name}: {result.get('error', 'Unknown error')}")
                    session_results['failed_calls'] += 1

                # Rate limiting
                await asyncio.sleep(1.5)

            except Exception as e:
                logger.error(f"❌ Error with {agent_id}: {e}")
                session_results['failed_calls'] += 1

        self.total_trades += session_results['trades_executed']

        logger.info(f"✅ Session complete: {session_results['trades_executed']} trades, "
                   f"{session_results['successful_calls']} successful calls")

        return session_results

    def execute_trade(self, agent_id: str, decision: Dict[str, Any], market_data: Dict[str, Any]) -> bool:
        """Execute trade for agent"""
        try:
            agent = self.active_agents[agent_id]
            portfolio = self.agent_portfolios[agent_id]

            symbol = decision['symbol']
            action = decision['action']
            amount = decision.get('amount', 0)
            confidence = decision.get('confidence', 0)

            if not symbol or symbol not in market_data:
                return False

            current_price = market_data[symbol]['price']

            # Validate confidence
            if confidence < agent.confidence_threshold * 100:
                return False

            if action == 'BUY':
                # Validate amount and cash
                if amount > portfolio['cash'] or amount < 500:  # Min $500 trade
                    return False

                quantity = amount / current_price

                # Execute buy
                portfolio['cash'] -= amount

                if symbol not in portfolio['positions']:
                    portfolio['positions'][symbol] = {'quantity': 0, 'avg_price': 0}

                pos = portfolio['positions'][symbol]
                total_quantity = pos['quantity'] + quantity
                total_cost = (pos['quantity'] * pos['avg_price']) + amount

                portfolio['positions'][symbol] = {
                    'quantity': total_quantity,
                    'avg_price': total_cost / total_quantity
                }

                # Record trade
                trade = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': current_price,
                    'value': amount,
                    'confidence': confidence,
                    'reasoning': decision.get('reasoning', ''),
                    'pnl': 0,
                    'success': None
                }

                portfolio['trades'].append(trade)
                agent.trades_executed += 1

                self.save_trade(trade)

                return True

            elif action == 'SELL':
                if symbol not in portfolio['positions']:
                    return False

                pos = portfolio['positions'][symbol]
                if pos['quantity'] <= 0:
                    return False

                # Calculate sell quantity
                if amount > 0:
                    sell_quantity = min(amount / current_price, pos['quantity'])
                else:
                    sell_quantity = pos['quantity'] * 0.5  # Sell 50%

                sell_value = sell_quantity * current_price

                # Calculate P&L
                cost_basis = sell_quantity * pos['avg_price']
                pnl = sell_value - cost_basis

                # Execute sell
                portfolio['cash'] += sell_value
                pos['quantity'] -= sell_quantity

                if pos['quantity'] < 0.000001:
                    del portfolio['positions'][symbol]

                # Record trade
                trade = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'price': current_price,
                    'value': sell_value,
                    'confidence': confidence,
                    'reasoning': decision.get('reasoning', ''),
                    'pnl': pnl,
                    'success': pnl > 0
                }

                portfolio['trades'].append(trade)
                agent.trades_executed += 1
                agent.total_pnl += pnl

                # Update win rate
                winning_trades = sum(1 for t in portfolio['trades'] if t.get('pnl', 0) > 0)
                agent.win_rate = winning_trades / len(portfolio['trades']) if portfolio['trades'] else 0

                self.save_trade(trade)

                return True

            return False

        except Exception as e:
            logger.error(f"❌ Trade execution error: {e}")
            return False

    def save_trade(self, trade: Dict[str, Any]):
        """Save trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO max_realism_trades
                (timestamp, agent_id, model_name, symbol, action, quantity, price, value,
                 confidence, reasoning, pnl, success)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade['timestamp'], trade['agent_id'], trade['model_name'], trade['symbol'],
                trade['action'], trade['quantity'], trade['price'], trade['value'],
                trade['confidence'], trade['reasoning'], trade['pnl'], trade['success']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving trade: {e}")

    def save_decision(self, agent_id: str, prompt: str, response: str, decision: Dict[str, Any], executed: bool):
        """Save AI decision"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO max_realism_decisions
                (timestamp, agent_id, model_name, prompt, response, decision, confidence, executed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(), agent_id, self.active_agents[agent_id].model_name,
                prompt, response, json.dumps(decision), decision.get('confidence', 0), executed
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving decision: {e}")

    def display_status(self):
        """Display comprehensive status"""
        print(f"\n🚀 MAXIMUM REALISM SYSTEM STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)

        # System overview
        total_value = 0
        total_initial = 0
        total_trades = 0
        total_pnl = 0

        print(f"📊 SYSTEM OVERVIEW:")
        print(f"   Total Models: {len(self.all_ollama_models)}")
        print(f"   Active Agents: {len(self.active_agents)}")
        print(f"   Total Trades: {self.total_trades}")

        if self.system_start_time:
            uptime = (datetime.now() - self.system_start_time).total_seconds() / 3600
            print(f"   System Uptime: {uptime:.2f} hours")

        print(f"\n🤖 AGENT PERFORMANCE:")
        print(f"{'Model Name':<25} {'Family':<12} {'Balance':<12} {'Return':<8} {'Trades':<7} {'Win Rate':<9} {'Style':<20}")
        print("-" * 100)

        # Sort agents by performance
        agent_performance = []
        for agent_id, agent in self.active_agents.items():
            portfolio = self.agent_portfolios[agent_id]

            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )

            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            total_value += portfolio_value
            total_initial += agent.initial_balance
            total_trades += agent.trades_executed
            total_pnl += agent.total_pnl

            agent_performance.append({
                'agent': agent,
                'portfolio_value': portfolio_value,
                'return': agent_return
            })

        # Sort by performance
        agent_performance.sort(key=lambda x: x['return'], reverse=True)

        # Display agents
        for i, perf in enumerate(agent_performance):
            agent = perf['agent']

            status_emoji = "🟢" if perf['return'] > 0 else "🔴" if perf['return'] < -5 else "🟡"
            rank_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "

            print(f"{agent.model_name:<25} {agent.model_family:<12} ${perf['portfolio_value']:>10,.0f} "
                  f"{perf['return']:>+6.1f}% {agent.trades_executed:>6d} {agent.win_rate:>7.1%} "
                  f"{agent.trading_style:<20} {status_emoji} {rank_emoji}")

        # System totals
        system_return = (total_value - total_initial) / total_initial * 100 if total_initial > 0 else 0

        print("-" * 100)
        print(f"{'SYSTEM TOTALS':<25} {'ALL':<12} ${total_value:>10,.0f} "
              f"{system_return:>+6.1f}% {total_trades:>6d} {'N/A':>7} {'ALL STRATEGIES':<20}")

        # Model family performance
        print(f"\n📈 MODEL FAMILY PERFORMANCE:")
        family_stats = {}
        for agent_id, agent in self.active_agents.items():
            family = agent.model_family
            if family not in family_stats:
                family_stats[family] = {'count': 0, 'total_return': 0, 'total_trades': 0}

            portfolio = self.agent_portfolios[agent_id]
            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )
            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            family_stats[family]['count'] += 1
            family_stats[family]['total_return'] += agent_return
            family_stats[family]['total_trades'] += agent.trades_executed

        for family, stats in sorted(family_stats.items(), key=lambda x: x[1]['total_return'], reverse=True):
            avg_return = stats['total_return'] / stats['count'] if stats['count'] > 0 else 0
            print(f"   {family.upper():<12} | Models: {stats['count']:2d} | Avg Return: {avg_return:+6.1f}% | Trades: {stats['total_trades']:3d}")

        # Current market
        if self.current_prices:
            print(f"\n📊 CURRENT MARKET:")
            for symbol, price in self.current_prices.items():
                if symbol in self.price_history and len(self.price_history[symbol]) >= 2:
                    prev_price = self.price_history[symbol][-2]
                    change = (price - prev_price) / prev_price * 100
                    trend_emoji = "📈" if change > 0 else "📉"
                    print(f"   {symbol:10} | ${price:>10.4f} | {change:>+6.2f}% {trend_emoji}")

        print("=" * 100)

    async def run_maximum_realism_system(self, duration_minutes: int = 90):
        """Run maximum realism system"""
        logger.info(f"🚀 Starting MAXIMUM REALISM SYSTEM for {duration_minutes} minutes")

        self.session = aiohttp.ClientSession()
        self.system_start_time = datetime.now()

        try:
            # Initialize agents
            self.initialize_agents()

            # Main loop
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0

            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Maximum realism cycle {cycle_count}")

                # Simulate market
                market_data = self.simulate_realistic_market()

                # Execute trading
                session_results = await self.execute_trading_session(market_data)

                # Display status every 3 cycles
                if cycle_count % 3 == 0:
                    self.display_status()

                # Wait
                await asyncio.sleep(240)  # 4 minutes between cycles

            # Final status
            print(f"\n🏁 MAXIMUM REALISM SYSTEM COMPLETED")
            print(f"Total cycles: {cycle_count}")
            self.display_status()

        finally:
            await self.session.close()

async def main():
    """Main function"""
    print("🚀 MAXIMUM REALISM SYSTEM")
    print("=" * 60)
    print("Features:")
    print("• ALL your Ollama models (including new ones)")
    print("• Maximum realistic market simulation")
    print("• Real AI decision making")
    print("• Actual trade execution")
    print("• Comprehensive performance tracking")
    print("• Model family comparison")
    print("=" * 60)

    system = MaximumRealismSystem()

    print(f"\n🤖 CONFIGURED MODELS ({len(system.all_ollama_models)}):")
    for model in system.all_ollama_models:
        print(f"   {model['name']:<25} | {model['family']:<12} | {model['style']}")

    print(f"\n🎯 Starting maximum realism system...")

    # Run system
    await system.run_maximum_realism_system(duration_minutes=60)

    print(f"\n✅ Maximum realism system completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
