#!/usr/bin/env python3
"""
🎯 ENHANCED PAPER TRADING DEMONSTRATION SYSTEM
Real-time paper trading simulation with all AI agents and advanced features
"""

import asyncio
import logging
import sys
import time
import json
import random
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from decimal import Decimal
import sqlite3
from dataclasses import dataclass, asdict

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'paper_trading_demo_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
    ]
)
logger = logging.getLogger("PaperTradingDemo")

@dataclass
class Trade:
    """Trade data structure"""
    id: str
    timestamp: datetime
    symbol: str
    side: str  # 'BUY' or 'SELL'
    quantity: float
    price: float
    value: float
    commission: float
    strategy: str
    ai_agent: str
    confidence: float
    status: str = 'FILLED'

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float

@dataclass
class MarketTick:
    """Market data tick"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: float
    ask: float
    change_24h: float
    high_24h: float
    low_24h: float

class PaperTradingEngine:
    """Advanced Paper Trading Engine with AI Integration"""
    
    def __init__(self, initial_balance: float = 10000.0):
        self.initial_balance = initial_balance
        self.cash_balance = initial_balance
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.market_data: Dict[str, MarketTick] = {}
        self.portfolio_history: List[Dict] = []
        
        # AI Agents Configuration
        self.ai_agents = {
            'market_watcher': {'model': 'marco-o1:7b', 'confidence': 0.85, 'active': True},
            'technical_analyst': {'model': 'magistral:24b', 'confidence': 0.90, 'active': True},
            'sentiment_analyzer': {'model': 'command-r:35b', 'confidence': 0.88, 'active': True},
            'risk_manager': {'model': 'cogito:32b', 'confidence': 0.92, 'active': True},
            'portfolio_optimizer': {'model': 'gemma3:27b', 'confidence': 0.87, 'active': True},
            'strategy_coordinator': {'model': 'mistral-small:24b', 'confidence': 0.89, 'active': True},
            'execution_engine': {'model': 'falcon3:10b', 'confidence': 0.86, 'active': True},
            'performance_analyzer': {'model': 'granite3.3:8b', 'confidence': 0.84, 'active': True},
            'deepseek_reasoner': {'model': 'deepseek-r1:32b', 'confidence': 0.95, 'active': True}
        }
        
        # Trading symbols
        self.symbols = [
            'BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT',
            'LINKUSDT', 'AVAXUSDT', 'MATICUSDT', 'UNIUSDT', 'LTCUSDT'
        ]
        
        # Initialize market data
        self._initialize_market_data()
        
        # Database for persistence
        self.db_path = f'paper_trading_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        self._initialize_database()
        
        # Performance metrics
        self.metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0,
            'avg_trade_pnl': 0.0
        }
        
        logger.info("🎯 Enhanced Paper Trading Engine initialized")
        logger.info(f"💰 Initial Balance: ${self.initial_balance:,.2f}")
        logger.info(f"🤖 AI Agents Active: {len([a for a in self.ai_agents.values() if a['active']])}")
        logger.info(f"📊 Trading Symbols: {len(self.symbols)}")

    def _initialize_market_data(self):
        """Initialize realistic market data"""
        base_prices = {
            'BTCUSDT': 45000.0, 'ETHUSDT': 2500.0, 'ADAUSDT': 0.45,
            'SOLUSDT': 95.0, 'DOTUSDT': 6.5, 'LINKUSDT': 14.0,
            'AVAXUSDT': 32.0, 'MATICUSDT': 0.85, 'UNIUSDT': 6.2, 'LTCUSDT': 75.0
        }
        
        for symbol in self.symbols:
            base_price = base_prices.get(symbol, 100.0)
            current_price = base_price * (1 + random.uniform(-0.05, 0.05))
            
            self.market_data[symbol] = MarketTick(
                symbol=symbol,
                price=current_price,
                volume=random.uniform(10000, 1000000),
                timestamp=datetime.now(timezone.utc),
                bid=current_price * 0.9995,
                ask=current_price * 1.0005,
                change_24h=random.uniform(-5.0, 5.0),
                high_24h=current_price * 1.05,
                low_24h=current_price * 0.95
            )

    def _initialize_database(self):
        """Initialize SQLite database for persistence"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                side TEXT,
                quantity REAL,
                price REAL,
                value REAL,
                commission REAL,
                strategy TEXT,
                ai_agent TEXT,
                confidence REAL,
                status TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                timestamp TEXT,
                total_value REAL,
                cash_balance REAL,
                positions_value REAL,
                total_pnl REAL,
                num_positions INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info(f"📊 Database initialized: {self.db_path}")

    def update_market_data(self):
        """Update market data with realistic price movements"""
        for symbol in self.symbols:
            current_tick = self.market_data[symbol]
            
            # Generate realistic price movement
            volatility = 0.02  # 2% volatility
            price_change = random.normalvariate(0, volatility)
            
            # Add occasional spikes
            if random.random() < 0.05:  # 5% chance of spike
                price_change += random.choice([-1, 1]) * random.uniform(0.02, 0.08)
            
            new_price = current_tick.price * (1 + price_change)
            new_price = max(new_price, 0.01)  # Prevent negative prices
            
            # Update tick
            self.market_data[symbol] = MarketTick(
                symbol=symbol,
                price=new_price,
                volume=random.uniform(10000, 1000000),
                timestamp=datetime.now(timezone.utc),
                bid=new_price * 0.9995,
                ask=new_price * 1.0005,
                change_24h=((new_price - current_tick.price) / current_tick.price) * 100,
                high_24h=max(current_tick.high_24h, new_price),
                low_24h=min(current_tick.low_24h, new_price)
            )

    def simulate_ai_decision(self, agent_name: str, symbol: str) -> Optional[Dict]:
        """Simulate AI agent decision making"""
        agent = self.ai_agents.get(agent_name)
        if not agent or not agent['active']:
            return None
        
        market_tick = self.market_data.get(symbol)
        if not market_tick:
            return None
        
        # Simulate different AI strategies
        strategies = {
            'market_watcher': self._market_watcher_strategy,
            'technical_analyst': self._technical_analyst_strategy,
            'sentiment_analyzer': self._sentiment_analyzer_strategy,
            'risk_manager': self._risk_manager_strategy,
            'deepseek_reasoner': self._deepseek_reasoner_strategy
        }
        
        strategy_func = strategies.get(agent_name, self._default_strategy)
        return strategy_func(symbol, market_tick, agent)

    def _market_watcher_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """Market watcher focuses on volume and price action"""
        if abs(tick.change_24h) > 3.0 and tick.volume > 500000:
            side = 'BUY' if tick.change_24h > 0 else 'SELL'
            confidence = min(agent['confidence'] * (abs(tick.change_24h) / 10), 0.95)
            
            return {
                'action': side,
                'symbol': symbol,
                'confidence': confidence,
                'strategy': 'momentum_breakout',
                'reasoning': f"Strong {side.lower()} signal: {tick.change_24h:.2f}% move with high volume"
            }
        return None

    def _technical_analyst_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """Technical analyst uses price patterns"""
        # Simulate RSI-like indicator
        rsi = random.uniform(20, 80)
        
        if rsi < 30:  # Oversold
            return {
                'action': 'BUY',
                'symbol': symbol,
                'confidence': agent['confidence'],
                'strategy': 'mean_reversion',
                'reasoning': f"RSI oversold at {rsi:.1f}, expecting bounce"
            }
        elif rsi > 70:  # Overbought
            return {
                'action': 'SELL',
                'symbol': symbol,
                'confidence': agent['confidence'],
                'strategy': 'mean_reversion',
                'reasoning': f"RSI overbought at {rsi:.1f}, expecting pullback"
            }
        return None

    def _sentiment_analyzer_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """Sentiment analyzer uses market sentiment"""
        sentiment_score = random.uniform(-1, 1)
        
        if abs(sentiment_score) > 0.6:
            side = 'BUY' if sentiment_score > 0 else 'SELL'
            confidence = agent['confidence'] * abs(sentiment_score)
            
            return {
                'action': side,
                'symbol': symbol,
                'confidence': confidence,
                'strategy': 'sentiment_following',
                'reasoning': f"Strong {'bullish' if sentiment_score > 0 else 'bearish'} sentiment: {sentiment_score:.2f}"
            }
        return None

    def _risk_manager_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """Risk manager focuses on position sizing and risk control"""
        current_position = self.positions.get(symbol)
        portfolio_value = self.get_portfolio_value()
        
        # Risk-based position sizing
        if current_position and abs(current_position.unrealized_pnl) > portfolio_value * 0.05:
            return {
                'action': 'SELL' if current_position.quantity > 0 else 'BUY',
                'symbol': symbol,
                'confidence': agent['confidence'],
                'strategy': 'risk_management',
                'reasoning': f"Risk limit exceeded: {current_position.unrealized_pnl:.2f}"
            }
        return None

    def _deepseek_reasoner_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """DeepSeek reasoner uses advanced reasoning"""
        # Simulate complex multi-factor analysis
        factors = {
            'price_momentum': tick.change_24h / 5.0,  # Normalize to -1 to 1
            'volume_strength': min(tick.volume / 1000000, 1.0),
            'volatility': abs(tick.change_24h) / 10.0,
            'market_structure': random.uniform(-0.5, 0.5)
        }
        
        # Weighted decision
        decision_score = (
            factors['price_momentum'] * 0.3 +
            factors['volume_strength'] * 0.2 +
            factors['volatility'] * 0.2 +
            factors['market_structure'] * 0.3
        )
        
        if abs(decision_score) > 0.4:
            side = 'BUY' if decision_score > 0 else 'SELL'
            confidence = min(agent['confidence'] * abs(decision_score), 0.98)
            
            return {
                'action': side,
                'symbol': symbol,
                'confidence': confidence,
                'strategy': 'multi_factor_reasoning',
                'reasoning': f"Multi-factor analysis score: {decision_score:.3f}"
            }
        return None

    def _default_strategy(self, symbol: str, tick: MarketTick, agent: Dict) -> Optional[Dict]:
        """Default random strategy"""
        if random.random() < 0.1:  # 10% chance of action
            return {
                'action': random.choice(['BUY', 'SELL']),
                'symbol': symbol,
                'confidence': agent['confidence'] * 0.5,
                'strategy': 'random',
                'reasoning': "Random decision for demonstration"
            }
        return None

    def execute_trade(self, decision: Dict) -> Optional[Trade]:
        """Execute a paper trade based on AI decision"""
        symbol = decision['symbol']
        action = decision['action']
        confidence = decision['confidence']
        strategy = decision['strategy']
        ai_agent = decision.get('ai_agent', 'unknown')

        market_tick = self.market_data.get(symbol)
        if not market_tick:
            return None

        # Calculate position size based on confidence and risk management
        portfolio_value = self.get_portfolio_value()
        max_position_value = portfolio_value * 0.1  # Max 10% per position
        confidence_multiplier = confidence * 0.5  # Scale down for safety
        position_value = max_position_value * confidence_multiplier

        # Determine execution price (simulate slippage)
        if action == 'BUY':
            execution_price = market_tick.ask * (1 + random.uniform(0, 0.001))  # 0-0.1% slippage
            quantity = position_value / execution_price

            if self.cash_balance >= position_value:
                trade_value = quantity * execution_price
                commission = trade_value * 0.001  # 0.1% commission

                # Update cash balance
                self.cash_balance -= (trade_value + commission)

                # Update position
                if symbol in self.positions:
                    pos = self.positions[symbol]
                    total_quantity = pos.quantity + quantity
                    total_cost = (pos.quantity * pos.avg_price) + trade_value
                    new_avg_price = total_cost / total_quantity

                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=total_quantity,
                        avg_price=new_avg_price,
                        market_value=total_quantity * market_tick.price,
                        unrealized_pnl=(market_tick.price - new_avg_price) * total_quantity,
                        realized_pnl=pos.realized_pnl
                    )
                else:
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=execution_price,
                        market_value=quantity * market_tick.price,
                        unrealized_pnl=(market_tick.price - execution_price) * quantity,
                        realized_pnl=0.0
                    )

                # Create trade record
                trade = Trade(
                    id=f"T{len(self.trades)+1:06d}",
                    timestamp=datetime.now(timezone.utc),
                    symbol=symbol,
                    side=action,
                    quantity=quantity,
                    price=execution_price,
                    value=trade_value,
                    commission=commission,
                    strategy=strategy,
                    ai_agent=ai_agent,
                    confidence=confidence
                )

                self.trades.append(trade)
                self._save_trade_to_db(trade)
                self._update_metrics()

                logger.info(f"✅ BUY executed: {quantity:.6f} {symbol} @ ${execution_price:.4f} (${trade_value:.2f})")
                return trade

        elif action == 'SELL':
            current_position = self.positions.get(symbol)
            if current_position and current_position.quantity > 0:
                execution_price = market_tick.bid * (1 - random.uniform(0, 0.001))  # 0-0.1% slippage

                # Sell partial or full position based on confidence
                sell_ratio = min(confidence, 1.0)
                quantity = current_position.quantity * sell_ratio

                trade_value = quantity * execution_price
                commission = trade_value * 0.001  # 0.1% commission

                # Update cash balance
                self.cash_balance += (trade_value - commission)

                # Calculate realized PnL
                realized_pnl = (execution_price - current_position.avg_price) * quantity

                # Update position
                remaining_quantity = current_position.quantity - quantity
                if remaining_quantity > 0.000001:  # Keep position if significant amount remains
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=remaining_quantity,
                        avg_price=current_position.avg_price,
                        market_value=remaining_quantity * market_tick.price,
                        unrealized_pnl=(market_tick.price - current_position.avg_price) * remaining_quantity,
                        realized_pnl=current_position.realized_pnl + realized_pnl
                    )
                else:
                    # Close position completely
                    del self.positions[symbol]

                # Create trade record
                trade = Trade(
                    id=f"T{len(self.trades)+1:06d}",
                    timestamp=datetime.now(timezone.utc),
                    symbol=symbol,
                    side=action,
                    quantity=quantity,
                    price=execution_price,
                    value=trade_value,
                    commission=commission,
                    strategy=strategy,
                    ai_agent=ai_agent,
                    confidence=confidence
                )

                self.trades.append(trade)
                self._save_trade_to_db(trade)
                self._update_metrics()

                logger.info(f"✅ SELL executed: {quantity:.6f} {symbol} @ ${execution_price:.4f} (${trade_value:.2f}, PnL: ${realized_pnl:.2f})")
                return trade

        return None

    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value"""
        total_value = self.cash_balance

        for position in self.positions.values():
            market_tick = self.market_data.get(position.symbol)
            if market_tick:
                total_value += position.quantity * market_tick.price

        return total_value

    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        total_value = self.get_portfolio_value()
        positions_value = sum(pos.quantity * self.market_data[pos.symbol].price
                            for pos in self.positions.values()
                            if pos.symbol in self.market_data)

        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())

        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'total_value': total_value,
            'cash_balance': self.cash_balance,
            'positions_value': positions_value,
            'total_pnl': total_value - self.initial_balance,
            'unrealized_pnl': total_unrealized_pnl,
            'realized_pnl': total_realized_pnl,
            'num_positions': len(self.positions),
            'num_trades': len(self.trades),
            'return_pct': ((total_value - self.initial_balance) / self.initial_balance) * 100
        }

    def _save_trade_to_db(self, trade: Trade):
        """Save trade to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO trades VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade.id, trade.timestamp.isoformat(), trade.symbol, trade.side,
            trade.quantity, trade.price, trade.value, trade.commission,
            trade.strategy, trade.ai_agent, trade.confidence, trade.status
        ))

        conn.commit()
        conn.close()

    def _save_portfolio_snapshot(self):
        """Save portfolio snapshot to database"""
        summary = self.get_portfolio_summary()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO portfolio_snapshots VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            summary['timestamp'], summary['total_value'], summary['cash_balance'],
            summary['positions_value'], summary['total_pnl'], summary['num_positions']
        ))

        conn.commit()
        conn.close()

    def _update_metrics(self):
        """Update performance metrics"""
        if not self.trades:
            return

        # Calculate basic metrics
        self.metrics['total_trades'] = len(self.trades)

        # Calculate PnL for closed positions
        realized_trades = []
        for trade in self.trades:
            if trade.side == 'SELL':
                # Find corresponding buy trade(s) - simplified
                buy_trades = [t for t in self.trades if t.symbol == trade.symbol and t.side == 'BUY' and t.timestamp < trade.timestamp]
                if buy_trades:
                    avg_buy_price = sum(t.price for t in buy_trades) / len(buy_trades)
                    pnl = (trade.price - avg_buy_price) * trade.quantity - trade.commission
                    realized_trades.append(pnl)

        if realized_trades:
            self.metrics['winning_trades'] = len([pnl for pnl in realized_trades if pnl > 0])
            self.metrics['losing_trades'] = len([pnl for pnl in realized_trades if pnl <= 0])
            self.metrics['win_rate'] = self.metrics['winning_trades'] / len(realized_trades)
            self.metrics['avg_trade_pnl'] = sum(realized_trades) / len(realized_trades)

        # Update total PnL
        portfolio_value = self.get_portfolio_value()
        self.metrics['total_pnl'] = portfolio_value - self.initial_balance

    def display_portfolio_status(self):
        """Display current portfolio status"""
        summary = self.get_portfolio_summary()

        print("\n" + "="*80)
        print("📊 PAPER TRADING PORTFOLIO STATUS")
        print("="*80)
        print(f"💰 Total Portfolio Value: ${summary['total_value']:,.2f}")
        print(f"💵 Cash Balance: ${summary['cash_balance']:,.2f}")
        print(f"📈 Positions Value: ${summary['positions_value']:,.2f}")
        print(f"📊 Total P&L: ${summary['total_pnl']:,.2f} ({summary['return_pct']:+.2f}%)")
        print(f"🔢 Active Positions: {summary['num_positions']}")
        print(f"📋 Total Trades: {summary['num_trades']}")

        if self.positions:
            print("\n📍 CURRENT POSITIONS:")
            print("-" * 80)
            for symbol, pos in self.positions.items():
                market_price = self.market_data[symbol].price
                pnl_pct = (pos.unrealized_pnl / (pos.avg_price * pos.quantity)) * 100
                print(f"  {symbol:10} | Qty: {pos.quantity:10.6f} | Avg: ${pos.avg_price:8.4f} | "
                      f"Market: ${market_price:8.4f} | P&L: ${pos.unrealized_pnl:8.2f} ({pnl_pct:+.2f}%)")

        if self.trades and len(self.trades) > 0:
            print("\n📋 RECENT TRADES (Last 5):")
            print("-" * 80)
            recent_trades = self.trades[-5:]
            for trade in recent_trades:
                print(f"  {trade.timestamp.strftime('%H:%M:%S')} | {trade.side:4} | {trade.symbol:10} | "
                      f"Qty: {trade.quantity:10.6f} | Price: ${trade.price:8.4f} | "
                      f"Agent: {trade.ai_agent:15} | Conf: {trade.confidence:.2f}")

        print("\n🤖 AI AGENTS STATUS:")
        print("-" * 80)
        for agent_name, agent_info in self.ai_agents.items():
            status = "🟢 ACTIVE" if agent_info['active'] else "🔴 INACTIVE"
            print(f"  {agent_name:20} | {agent_info['model']:15} | Confidence: {agent_info['confidence']:.2f} | {status}")

        print("="*80)

    def display_market_data(self):
        """Display current market data"""
        print("\n📈 LIVE MARKET DATA")
        print("-" * 80)
        print(f"{'Symbol':10} | {'Price':>10} | {'Change 24h':>10} | {'Volume':>12} | {'Bid':>10} | {'Ask':>10}")
        print("-" * 80)

        for symbol in self.symbols:
            tick = self.market_data[symbol]
            change_color = "📈" if tick.change_24h >= 0 else "📉"
            print(f"{symbol:10} | ${tick.price:9.4f} | {change_color}{tick.change_24h:+8.2f}% | "
                  f"{tick.volume:11,.0f} | ${tick.bid:9.4f} | ${tick.ask:9.4f}")

    async def run_trading_simulation(self, duration_minutes: int = 5):
        """Run the paper trading simulation"""
        logger.info("🚀 Starting Enhanced Paper Trading Simulation")
        logger.info(f"⏱️ Duration: {duration_minutes} minutes")
        logger.info(f"🤖 AI Agents: {len([a for a in self.ai_agents.values() if a['active']])}")

        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(minutes=duration_minutes)

        iteration = 0

        while datetime.now(timezone.utc) < end_time:
            iteration += 1

            # Update market data
            self.update_market_data()

            # Get AI decisions from all active agents
            decisions = []
            for agent_name in self.ai_agents.keys():
                if self.ai_agents[agent_name]['active']:
                    for symbol in self.symbols:
                        decision = self.simulate_ai_decision(agent_name, symbol)
                        if decision:
                            decision['ai_agent'] = agent_name
                            decisions.append(decision)

            # Execute trades based on decisions
            executed_trades = []
            for decision in decisions:
                trade = self.execute_trade(decision)
                if trade:
                    executed_trades.append(trade)

            # Update positions with current market prices
            self._update_positions_market_value()

            # Display status every 10 iterations or when trades are executed
            if iteration % 10 == 0 or executed_trades:
                print(f"\n⏰ Simulation Time: {datetime.now(timezone.utc).strftime('%H:%M:%S')} (Iteration {iteration})")

                if executed_trades:
                    print(f"🔥 {len(executed_trades)} trades executed this cycle!")
                    for trade in executed_trades:
                        print(f"   {trade.ai_agent} → {trade.side} {trade.quantity:.6f} {trade.symbol} @ ${trade.price:.4f}")

                self.display_portfolio_status()
                self.display_market_data()

                # Save portfolio snapshot
                self._save_portfolio_snapshot()

            # Wait before next iteration
            await asyncio.sleep(2)  # 2 seconds between iterations

        # Final summary
        print("\n" + "🏁" * 40)
        print("SIMULATION COMPLETED!")
        print("🏁" * 40)

        final_summary = self.get_portfolio_summary()

        print(f"\n📊 FINAL RESULTS:")
        print(f"⏱️ Duration: {duration_minutes} minutes")
        print(f"💰 Starting Balance: ${self.initial_balance:,.2f}")
        print(f"💰 Final Portfolio Value: ${final_summary['total_value']:,.2f}")
        print(f"📈 Total Return: ${final_summary['total_pnl']:,.2f} ({final_summary['return_pct']:+.2f}%)")
        print(f"📋 Total Trades Executed: {len(self.trades)}")
        print(f"📍 Final Positions: {len(self.positions)}")

        if self.metrics['total_trades'] > 0:
            print(f"\n📊 TRADING METRICS:")
            print(f"🎯 Win Rate: {self.metrics['win_rate']:.1%}")
            print(f"✅ Winning Trades: {self.metrics['winning_trades']}")
            print(f"❌ Losing Trades: {self.metrics['losing_trades']}")
            print(f"💵 Average Trade P&L: ${self.metrics['avg_trade_pnl']:.2f}")

        print(f"\n💾 Data saved to: {self.db_path}")
        logger.info("✅ Paper Trading Simulation completed successfully!")

    def _update_positions_market_value(self):
        """Update market values for all positions"""
        for symbol, position in self.positions.items():
            if symbol in self.market_data:
                current_price = self.market_data[symbol].price
                position.market_value = position.quantity * current_price
                position.unrealized_pnl = (current_price - position.avg_price) * position.quantity

async def main():
    """Main demonstration function"""
    print("🎯" * 50)
    print("🎯 ENHANCED PAPER TRADING DEMONSTRATION")
    print("🎯 Real-time AI-powered trading simulation")
    print("🎯" * 50)

    # Initialize paper trading engine
    engine = PaperTradingEngine(initial_balance=10000.0)

    # Show initial status
    engine.display_portfolio_status()
    engine.display_market_data()

    print("\n🚀 Starting simulation in 3 seconds...")
    await asyncio.sleep(3)

    # Run the simulation
    await engine.run_trading_simulation(duration_minutes=3)  # 3-minute demo

    print("\n🎯 Paper Trading Demonstration Complete!")
    print("🎯 Check the generated database file for detailed trade history.")

if __name__ == "__main__":
    asyncio.run(main())
