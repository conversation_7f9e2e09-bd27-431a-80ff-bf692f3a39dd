#!/usr/bin/env python3
"""
🔄 FEEDBACK LOOPS & LEARNING SYSTEM
Real reinforcement learning from trading results
"""

import asyncio
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple
import joblib
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("FeedbackLearning")

class TradingFeedbackSystem:
    """Real feedback learning system for trading AI"""
    
    def __init__(self, db_path="autonomous_trading.db"):
        self.db_path = db_path
        self.learning_rate = 0.01
        self.experience_buffer = []
        self.model_versions = {}
        self.performance_history = []
        
    def collect_trading_outcomes(self, lookback_hours=24) -> pd.DataFrame:
        """Collect actual trading outcomes for learning"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get decisions from last 24 hours
            query = '''
                SELECT timestamp, agent_id, symbol, action, quantity, price, confidence, reasoning, executed
                FROM autonomous_decisions 
                WHERE timestamp > datetime('now', '-{} hours')
                AND executed = 1
                ORDER BY timestamp
            '''.format(lookback_hours)
            
            decisions_df = pd.read_sql_query(query, conn)
            
            if len(decisions_df) == 0:
                logger.warning("No trading decisions found for feedback")
                return None
            
            # Calculate actual outcomes
            outcomes = []
            for _, decision in decisions_df.iterrows():
                outcome = self._calculate_decision_outcome(decision)
                if outcome is not None:
                    outcomes.append(outcome)
            
            conn.close()
            
            if outcomes:
                outcomes_df = pd.DataFrame(outcomes)
                logger.info(f"✅ Collected {len(outcomes)} trading outcomes for learning")
                return outcomes_df
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error collecting outcomes: {e}")
            return None

    def _calculate_decision_outcome(self, decision: pd.Series) -> Dict:
        """Calculate the actual outcome of a trading decision"""
        try:
            # Simulate outcome calculation (in real system, use actual price data)
            confidence = decision['confidence']
            action = decision['action']
            
            # Simulate returns based on confidence and random market movement
            market_return = np.random.normal(0, 0.02)  # 2% daily volatility
            
            if action == 'BUY':
                actual_return = market_return
                profit = actual_return > 0
            else:  # SELL
                actual_return = -market_return  # Inverse for short positions
                profit = actual_return > 0
            
            # Decision quality based on confidence vs outcome
            if profit:
                quality = confidence * (1 + abs(actual_return))
            else:
                quality = -confidence * (1 + abs(actual_return))
            
            return {
                'agent_id': decision['agent_id'],
                'symbol': decision['symbol'],
                'action': action,
                'confidence': confidence,
                'actual_return': actual_return,
                'profit': profit,
                'decision_quality': np.clip(quality, -1.0, 1.0)
            }
            
        except Exception as e:
            logger.error(f"❌ Error calculating outcome: {e}")
            return None

    def update_agent_models(self, outcomes_df: pd.DataFrame):
        """Update agent models based on feedback"""
        if outcomes_df is None or len(outcomes_df) == 0:
            return
        
        # Group by agent
        for agent_id in outcomes_df['agent_id'].unique():
            agent_outcomes = outcomes_df[outcomes_df['agent_id'] == agent_id]
            
            if len(agent_outcomes) < 3:  # Need minimum samples
                continue
            
            # Calculate agent performance metrics
            win_rate = agent_outcomes['profit'].mean()
            avg_quality = agent_outcomes['decision_quality'].mean()
            confidence_accuracy = self._calculate_confidence_accuracy(agent_outcomes)
            
            # Update agent parameters based on performance
            updates = self._generate_model_updates(agent_id, win_rate, avg_quality, confidence_accuracy)
            
            # Save updates
            self.model_versions[agent_id] = {
                'timestamp': datetime.now().isoformat(),
                'win_rate': win_rate,
                'avg_quality': avg_quality,
                'confidence_accuracy': confidence_accuracy,
                'updates': updates
            }
            
            logger.info(f"🔄 Updated model for {agent_id}: Win rate {win_rate:.2%}, Quality {avg_quality:.3f}")

    def _calculate_confidence_accuracy(self, outcomes: pd.DataFrame) -> float:
        """Calculate how well confidence predicts actual outcomes"""
        if len(outcomes) < 3:
            return 0.0
        
        correlation = outcomes['confidence'].corr(outcomes['decision_quality'])
        return correlation if not np.isnan(correlation) else 0.0

    def _generate_model_updates(self, agent_id: str, win_rate: float, avg_quality: float, confidence_accuracy: float) -> Dict:
        """Generate model parameter updates based on performance"""
        updates = {}
        
        # Adjust risk tolerance based on performance
        if win_rate > 0.6:  # Good performance
            updates['risk_tolerance'] = 0.025  # Increase risk tolerance
            updates['confidence_threshold'] = 0.25  # Lower confidence threshold
        elif win_rate < 0.4:  # Poor performance
            updates['risk_tolerance'] = 0.015  # Decrease risk tolerance
            updates['confidence_threshold'] = 0.4  # Higher confidence threshold
        
        # Adjust position sizing based on quality
        if avg_quality > 0.1:
            updates['max_position_size'] = 0.12  # Increase position size
        elif avg_quality < -0.1:
            updates['max_position_size'] = 0.08  # Decrease position size
        
        # Adjust confidence calibration
        if confidence_accuracy < 0.3:
            updates['confidence_multiplier'] = 0.8  # Reduce overconfidence
        elif confidence_accuracy > 0.7:
            updates['confidence_multiplier'] = 1.2  # Increase confidence
        
        return updates

    def retrain_models_with_feedback(self, outcomes_df: pd.DataFrame):
        """Retrain ML models using feedback data"""
        if outcomes_df is None or len(outcomes_df) < 10:
            logger.warning("Insufficient data for model retraining")
            return None
        
        try:
            # Prepare features and targets
            features = []
            targets = []
            
            for _, outcome in outcomes_df.iterrows():
                # Features: confidence, action type, recent performance
                feature_vector = [
                    outcome['confidence'],
                    1 if outcome['action'] == 'BUY' else 0,
                    outcome['actual_return'],
                    abs(outcome['actual_return'])  # Volatility proxy
                ]
                
                # Target: decision quality
                target = outcome['decision_quality']
                
                features.append(feature_vector)
                targets.append(target)
            
            X = np.array(features)
            y = np.array(targets)
            
            # Train improved model
            model = RandomForestRegressor(
                n_estimators=50,
                max_depth=8,
                min_samples_split=3,
                random_state=42
            )
            
            model.fit(X, y)
            
            # Evaluate improvement
            predictions = model.predict(X)
            mse = mean_squared_error(y, predictions)
            
            # Save retrained model
            model_filename = f'retrained_model_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pkl'
            joblib.dump(model, model_filename)
            
            logger.info(f"🤖 Retrained model with {len(outcomes_df)} feedback samples")
            logger.info(f"   Model MSE: {mse:.6f}")
            logger.info(f"   Saved as: {model_filename}")
            
            return model_filename
            
        except Exception as e:
            logger.error(f"❌ Model retraining error: {e}")
            return None

    def generate_performance_report(self) -> Dict:
        """Generate comprehensive performance report"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get recent performance data
            performance_query = '''
                SELECT agent_id, portfolio_value, total_return, active_positions, timestamp
                FROM agent_performance 
                WHERE timestamp > datetime('now', '-7 days')
                ORDER BY timestamp DESC
            '''
            
            performance_df = pd.read_sql_query(performance_query, conn)
            
            # Get decision statistics
            decisions_query = '''
                SELECT agent_id, COUNT(*) as total_decisions, 
                       SUM(CASE WHEN executed = 1 THEN 1 ELSE 0 END) as executed_decisions,
                       AVG(confidence) as avg_confidence
                FROM autonomous_decisions 
                WHERE timestamp > datetime('now', '-7 days')
                GROUP BY agent_id
            '''
            
            decisions_df = pd.read_sql_query(decisions_query, conn)
            conn.close()
            
            # Compile report
            report = {
                'timestamp': datetime.now().isoformat(),
                'agents': {},
                'system_metrics': {}
            }
            
            if len(performance_df) > 0:
                # Per-agent metrics
                for agent_id in performance_df['agent_id'].unique():
                    agent_perf = performance_df[performance_df['agent_id'] == agent_id]
                    agent_decisions = decisions_df[decisions_df['agent_id'] == agent_id]
                    
                    latest_performance = agent_perf.iloc[0] if len(agent_perf) > 0 else None
                    decision_stats = agent_decisions.iloc[0] if len(agent_decisions) > 0 else None
                    
                    report['agents'][agent_id] = {
                        'current_value': latest_performance['portfolio_value'] if latest_performance is not None else 0,
                        'total_return': latest_performance['total_return'] if latest_performance is not None else 0,
                        'total_decisions': decision_stats['total_decisions'] if decision_stats is not None else 0,
                        'execution_rate': (decision_stats['executed_decisions'] / decision_stats['total_decisions']) if decision_stats is not None and decision_stats['total_decisions'] > 0 else 0,
                        'avg_confidence': decision_stats['avg_confidence'] if decision_stats is not None else 0,
                        'model_updates': len(self.model_versions.get(agent_id, {}))
                    }
                
                # System-wide metrics
                total_value = performance_df.groupby('agent_id')['portfolio_value'].last().sum()
                avg_return = performance_df.groupby('agent_id')['total_return'].last().mean()
                total_decisions = decisions_df['total_decisions'].sum() if len(decisions_df) > 0 else 0
                
                report['system_metrics'] = {
                    'total_portfolio_value': total_value,
                    'average_return': avg_return,
                    'total_decisions': total_decisions,
                    'active_agents': len(performance_df['agent_id'].unique()),
                    'learning_cycles': len(self.performance_history)
                }
            
            logger.info("📊 Generated performance report")
            return report
            
        except Exception as e:
            logger.error(f"❌ Report generation error: {e}")
            return {}

    async def run_feedback_learning_cycle(self):
        """Run one complete feedback learning cycle"""
        logger.info("🔄 Starting feedback learning cycle")
        
        # Collect trading outcomes
        outcomes = self.collect_trading_outcomes(lookback_hours=24)
        
        if outcomes is not None and len(outcomes) > 0:
            # Update agent models based on feedback
            self.update_agent_models(outcomes)
            
            # Retrain models if enough data
            if len(outcomes) >= 10:
                model_file = self.retrain_models_with_feedback(outcomes)
                if model_file:
                    logger.info(f"✅ Model retrained: {model_file}")
            
            # Generate performance report
            report = self.generate_performance_report()
            
            # Save to history
            self.performance_history.append({
                'timestamp': datetime.now().isoformat(),
                'outcomes_processed': len(outcomes),
                'agents_updated': len(self.model_versions),
                'system_performance': report.get('system_metrics', {})
            })
            
            logger.info(f"✅ Feedback learning cycle complete: {len(outcomes)} outcomes processed")
            return report
        else:
            logger.warning("⚠️ No outcomes available for learning")
            return None

async def main():
    """Demonstration of feedback learning system"""
    print("🔄 FEEDBACK LOOPS & LEARNING SYSTEM")
    print("=" * 50)
    
    feedback_system = TradingFeedbackSystem()
    
    # Simulate some trading data first
    print("📊 Simulating trading outcomes...")
    
    # Create sample data
    conn = sqlite3.connect(feedback_system.db_path)
    cursor = conn.cursor()
    
    # Create tables if they don't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS autonomous_decisions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            agent_id TEXT,
            symbol TEXT,
            action TEXT,
            quantity REAL,
            price REAL,
            confidence REAL,
            reasoning TEXT,
            executed BOOLEAN
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS agent_performance (
            timestamp TEXT,
            agent_id TEXT,
            portfolio_value REAL,
            cash_balance REAL,
            total_return REAL,
            active_positions INTEGER
        )
    ''')
    
    # Insert sample decisions
    sample_decisions = [
        ('momentum_trader', 'BTCUSDT', 'BUY', 0.1, 45000, 0.8, 'Strong momentum signal'),
        ('contrarian_trader', 'ETHUSDT', 'SELL', 0.5, 2500, 0.7, 'Overbought conditions'),
        ('volume_trader', 'ADAUSDT', 'BUY', 100, 0.45, 0.6, 'High volume breakout'),
    ]
    
    for i, (agent, symbol, action, qty, price, conf, reason) in enumerate(sample_decisions):
        timestamp = (datetime.now() - timedelta(hours=i)).isoformat()
        cursor.execute('''
            INSERT INTO autonomous_decisions 
            (timestamp, agent_id, symbol, action, quantity, price, confidence, reasoning, executed)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (timestamp, agent, symbol, action, qty, price, conf, reason, True))
    
    # Insert sample performance
    for agent in ['momentum_trader', 'contrarian_trader', 'volume_trader']:
        for i in range(5):
            timestamp = (datetime.now() - timedelta(hours=i)).isoformat()
            value = 2000 + np.random.normal(0, 100)
            return_pct = (value - 2000) / 2000
            cursor.execute('''
                INSERT INTO agent_performance 
                (timestamp, agent_id, portfolio_value, cash_balance, total_return, active_positions)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (timestamp, agent, value, value*0.3, return_pct, np.random.randint(1, 4)))
    
    conn.commit()
    conn.close()
    
    print("✅ Sample data created")
    
    # Run feedback learning cycle
    print("\n🔄 Running feedback learning cycle...")
    report = await feedback_system.run_feedback_learning_cycle()
    
    if report:
        print("\n📊 PERFORMANCE REPORT:")
        print(f"System Portfolio Value: ${report['system_metrics'].get('total_portfolio_value', 0):,.2f}")
        print(f"Average Return: {report['system_metrics'].get('average_return', 0):+.2%}")
        print(f"Total Decisions: {report['system_metrics'].get('total_decisions', 0)}")
        print(f"Active Agents: {report['system_metrics'].get('active_agents', 0)}")
        
        print("\n🤖 AGENT UPDATES:")
        for agent_id, updates in feedback_system.model_versions.items():
            print(f"  {agent_id}: Win rate {updates['win_rate']:.2%}, Quality {updates['avg_quality']:.3f}")
    
    print("\n✅ Feedback learning demonstration complete!")

if __name__ == "__main__":
    asyncio.run(main())
