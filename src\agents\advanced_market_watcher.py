"""
Advanced Market Watcher Agent - Granite3.3:8b Model
Sophisticated real-time market monitoring with advanced pattern recognition,
sentiment analysis, and predictive modeling capabilities.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
import logging

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, MarketSignal, TradingAlert
from src.utils.technical_indicators import TechnicalIndicators
from src.utils.pattern_recognition import PatternRecognition
from src.utils.sentiment_analyzer import SentimentAnalyzer


class MarketRegime(Enum):
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"


@dataclass
class MarketCondition:
    regime: MarketRegime
    confidence: float
    volatility_level: float
    trend_strength: float
    momentum_score: float
    risk_level: str
    timestamp: datetime


@dataclass
class AdvancedMarketSignal:
    symbol: str
    signal_type: str
    strength: float
    confidence: float
    timeframe: str
    conditions: List[str]
    risk_reward_ratio: float
    expected_move: float
    stop_loss: float
    take_profit: List[float]
    ai_reasoning: str
    technical_confluence: int
    timestamp: datetime


class AdvancedMarketWatcher(BaseAgent):
    """
    Advanced Market Watcher using Granite3.3:8b for sophisticated market analysis.
    
    Features:
    - Multi-timeframe analysis (1m, 5m, 15m, 1h, 4h, 1d)
    - Advanced pattern recognition (50+ patterns)
    - Market regime detection
    - Sentiment analysis integration
    - Predictive modeling with confidence intervals
    - Real-time anomaly detection
    - Cross-asset correlation analysis
    - Volume profile analysis
    - Order flow analysis
    - News impact assessment
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_market_watcher"
        self.model_name = "granite3.3:8b"
        
        # Advanced components
        self.technical_indicators = TechnicalIndicators()
        self.pattern_recognition = PatternRecognition()
        self.sentiment_analyzer = SentimentAnalyzer()
        
        # Market data storage
        self.price_data = {}  # Multi-timeframe OHLCV data
        self.volume_profile = {}
        self.order_flow = {}
        self.market_conditions = {}
        self.correlation_matrix = {}
        
        # Analysis parameters
        self.timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.lookback_periods = {
            "1m": 1440,   # 24 hours
            "5m": 288,    # 24 hours  
            "15m": 96,    # 24 hours
            "1h": 168,    # 7 days
            "4h": 168,    # 28 days
            "1d": 365     # 1 year
        }
        
        # Advanced thresholds
        self.volatility_threshold = 0.02  # 2%
        self.volume_spike_threshold = 2.0  # 2x average
        self.correlation_threshold = 0.7
        self.confidence_threshold = 0.75
        
        # Pattern weights for confluence
        self.pattern_weights = {
            "double_top": 0.8,
            "double_bottom": 0.8,
            "head_shoulders": 0.9,
            "triangle": 0.7,
            "flag": 0.6,
            "wedge": 0.7,
            "channel": 0.6,
            "support_resistance": 0.8
        }
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced market watcher components."""
        self.logger.info("🔍 Initializing Advanced Market Watcher with Granite3.3:8b")
        
        # Initialize technical indicators
        await self.technical_indicators.initialize()
        
        # Initialize pattern recognition
        await self.pattern_recognition.initialize()
        
        # Initialize sentiment analyzer
        await self.sentiment_analyzer.initialize()
        
        # Load historical data for initial analysis
        await self._load_historical_data()
        
        self.logger.info("✅ Advanced Market Watcher initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced market monitoring tasks."""
        return [
            asyncio.create_task(self._multi_timeframe_analysis()),
            asyncio.create_task(self._pattern_recognition_scanner()),
            asyncio.create_task(self._market_regime_detector()),
            asyncio.create_task(self._anomaly_detector()),
            asyncio.create_task(self._correlation_analyzer()),
            asyncio.create_task(self._volume_profile_analyzer()),
            asyncio.create_task(self._sentiment_monitor()),
            asyncio.create_task(self._predictive_modeling()),
            asyncio.create_task(self._risk_assessment())
        ]

    async def _multi_timeframe_analysis(self):
        """Perform sophisticated multi-timeframe analysis."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    timeframe_signals = {}
                    
                    for timeframe in self.timeframes:
                        # Get OHLCV data for timeframe
                        data = await self._get_ohlcv_data(symbol, timeframe)
                        
                        if len(data) < 50:  # Need minimum data
                            continue
                        
                        # Calculate advanced technical indicators
                        indicators = await self._calculate_advanced_indicators(data, timeframe)
                        
                        # Analyze timeframe
                        analysis = await self._analyze_timeframe(symbol, timeframe, data, indicators)
                        timeframe_signals[timeframe] = analysis
                    
                    # Synthesize multi-timeframe analysis
                    if timeframe_signals:
                        synthesis = await self._synthesize_timeframe_analysis(symbol, timeframe_signals)
                        await self._process_synthesis(symbol, synthesis)
                
                await asyncio.sleep(30)  # Analyze every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Multi-timeframe analysis error: {e}")
                await asyncio.sleep(5)

    async def _calculate_advanced_indicators(self, data: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """Calculate comprehensive technical indicators."""
        indicators = {}
        
        # Trend indicators
        indicators["sma_20"] = data["close"].rolling(20).mean()
        indicators["sma_50"] = data["close"].rolling(50).mean()
        indicators["sma_200"] = data["close"].rolling(200).mean()
        indicators["ema_12"] = data["close"].ewm(span=12).mean()
        indicators["ema_26"] = data["close"].ewm(span=26).mean()
        
        # MACD
        indicators["macd"] = indicators["ema_12"] - indicators["ema_26"]
        indicators["macd_signal"] = indicators["macd"].ewm(span=9).mean()
        indicators["macd_histogram"] = indicators["macd"] - indicators["macd_signal"]
        
        # RSI
        delta = data["close"].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        indicators["rsi"] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        indicators["bb_middle"] = data["close"].rolling(bb_period).mean()
        bb_std_dev = data["close"].rolling(bb_period).std()
        indicators["bb_upper"] = indicators["bb_middle"] + (bb_std_dev * bb_std)
        indicators["bb_lower"] = indicators["bb_middle"] - (bb_std_dev * bb_std)
        indicators["bb_width"] = (indicators["bb_upper"] - indicators["bb_lower"]) / indicators["bb_middle"]
        
        # Stochastic
        low_14 = data["low"].rolling(14).min()
        high_14 = data["high"].rolling(14).max()
        indicators["stoch_k"] = 100 * ((data["close"] - low_14) / (high_14 - low_14))
        indicators["stoch_d"] = indicators["stoch_k"].rolling(3).mean()
        
        # Volume indicators
        indicators["volume_sma"] = data["volume"].rolling(20).mean()
        indicators["volume_ratio"] = data["volume"] / indicators["volume_sma"]
        indicators["obv"] = (data["volume"] * np.sign(data["close"].diff())).cumsum()
        
        # Volatility indicators
        indicators["atr"] = self._calculate_atr(data)
        indicators["volatility"] = data["close"].pct_change().rolling(20).std() * np.sqrt(252)
        
        # Advanced momentum indicators
        indicators["williams_r"] = -100 * ((high_14 - data["close"]) / (high_14 - low_14))
        indicators["cci"] = self._calculate_cci(data)
        indicators["momentum"] = data["close"] / data["close"].shift(10) - 1
        
        # Support/Resistance levels
        indicators["pivot_points"] = self._calculate_pivot_points(data)
        indicators["support_levels"] = self._identify_support_levels(data)
        indicators["resistance_levels"] = self._identify_resistance_levels(data)
        
        return indicators

    async def _analyze_timeframe(self, symbol: str, timeframe: str, data: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Perform comprehensive timeframe analysis."""
        
        # Get latest values
        latest = data.iloc[-1]
        prev = data.iloc[-2] if len(data) > 1 else latest
        
        analysis = {
            "timeframe": timeframe,
            "timestamp": datetime.utcnow(),
            "price_action": {},
            "trend_analysis": {},
            "momentum_analysis": {},
            "volatility_analysis": {},
            "volume_analysis": {},
            "pattern_analysis": {},
            "support_resistance": {},
            "signals": []
        }
        
        # Price action analysis
        analysis["price_action"] = {
            "current_price": latest["close"],
            "price_change": latest["close"] - prev["close"],
            "price_change_pct": ((latest["close"] - prev["close"]) / prev["close"]) * 100,
            "high_low_range": latest["high"] - latest["low"],
            "body_size": abs(latest["close"] - latest["open"]),
            "upper_wick": latest["high"] - max(latest["open"], latest["close"]),
            "lower_wick": min(latest["open"], latest["close"]) - latest["low"]
        }
        
        # Trend analysis
        sma_20 = indicators["sma_20"].iloc[-1]
        sma_50 = indicators["sma_50"].iloc[-1]
        sma_200 = indicators["sma_200"].iloc[-1] if len(indicators["sma_200"]) > 0 else None
        
        analysis["trend_analysis"] = {
            "short_term_trend": "bullish" if latest["close"] > sma_20 else "bearish",
            "medium_term_trend": "bullish" if latest["close"] > sma_50 else "bearish",
            "long_term_trend": "bullish" if sma_200 and latest["close"] > sma_200 else "bearish",
            "sma_alignment": sma_20 > sma_50 > (sma_200 or 0),
            "trend_strength": abs(indicators["momentum"].iloc[-1]) if len(indicators["momentum"]) > 0 else 0
        }
        
        # Momentum analysis
        rsi = indicators["rsi"].iloc[-1] if len(indicators["rsi"]) > 0 else 50
        macd = indicators["macd"].iloc[-1] if len(indicators["macd"]) > 0 else 0
        macd_signal = indicators["macd_signal"].iloc[-1] if len(indicators["macd_signal"]) > 0 else 0
        
        analysis["momentum_analysis"] = {
            "rsi": rsi,
            "rsi_condition": "overbought" if rsi > 70 else "oversold" if rsi < 30 else "neutral",
            "macd": macd,
            "macd_signal": macd_signal,
            "macd_crossover": "bullish" if macd > macd_signal else "bearish",
            "momentum_divergence": await self._detect_divergence(data, indicators)
        }
        
        # Generate AI-powered analysis
        ai_context = {
            "symbol": symbol,
            "timeframe": timeframe,
            "price_action": analysis["price_action"],
            "trend": analysis["trend_analysis"],
            "momentum": analysis["momentum_analysis"],
            "technical_indicators": {
                "rsi": rsi,
                "macd": macd,
                "bb_position": self._get_bb_position(latest["close"], indicators),
                "volume_ratio": indicators["volume_ratio"].iloc[-1] if len(indicators["volume_ratio"]) > 0 else 1
            }
        }
        
        ai_analysis = await ai_service.generate_response(
            "market_watcher",
            f"""
            As an advanced market analyst using the Granite3.3:8b model, analyze the following {timeframe} timeframe data for {symbol}:
            
            Price Action: {ai_context['price_action']}
            Trend Analysis: {ai_context['trend']}
            Momentum Analysis: {ai_context['momentum']}
            Technical Indicators: {ai_context['technical_indicators']}
            
            Provide a sophisticated analysis including:
            1. Market structure assessment
            2. Key levels and zones
            3. Potential scenarios with probabilities
            4. Risk factors and catalysts
            5. Actionable insights for traders
            
            Focus on confluence of multiple factors and provide specific, actionable guidance.
            """,
            ai_context
        )
        
        analysis["ai_analysis"] = ai_analysis
        
        return analysis

    async def _synthesize_timeframe_analysis(self, symbol: str, timeframe_signals: Dict) -> Dict[str, Any]:
        """Synthesize multi-timeframe analysis into unified view."""
        
        synthesis = {
            "symbol": symbol,
            "timestamp": datetime.utcnow(),
            "overall_bias": "neutral",
            "confidence": 0.0,
            "timeframe_alignment": {},
            "key_levels": {},
            "scenarios": [],
            "risk_assessment": {},
            "recommended_actions": []
        }
        
        # Analyze timeframe alignment
        bullish_timeframes = []
        bearish_timeframes = []
        
        for tf, analysis in timeframe_signals.items():
            trend = analysis.get("trend_analysis", {})
            if trend.get("short_term_trend") == "bullish":
                bullish_timeframes.append(tf)
            elif trend.get("short_term_trend") == "bearish":
                bearish_timeframes.append(tf)
        
        # Determine overall bias
        if len(bullish_timeframes) > len(bearish_timeframes):
            synthesis["overall_bias"] = "bullish"
            synthesis["confidence"] = len(bullish_timeframes) / len(timeframe_signals)
        elif len(bearish_timeframes) > len(bullish_timeframes):
            synthesis["overall_bias"] = "bearish"
            synthesis["confidence"] = len(bearish_timeframes) / len(timeframe_signals)
        else:
            synthesis["overall_bias"] = "neutral"
            synthesis["confidence"] = 0.5
        
        # Generate comprehensive AI synthesis
        synthesis_context = {
            "symbol": symbol,
            "timeframe_signals": timeframe_signals,
            "bullish_timeframes": bullish_timeframes,
            "bearish_timeframes": bearish_timeframes,
            "overall_bias": synthesis["overall_bias"],
            "confidence": synthesis["confidence"]
        }
        
        ai_synthesis = await ai_service.generate_response(
            "market_watcher",
            f"""
            As an expert market analyst, synthesize the following multi-timeframe analysis for {symbol}:
            
            Timeframe Analysis: {synthesis_context}
            
            Provide a comprehensive synthesis including:
            1. Overall market structure and bias
            2. Key confluence zones and levels
            3. Probability-weighted scenarios
            4. Risk/reward assessment
            5. Specific trading recommendations
            6. Market regime classification
            7. Potential catalysts and risks
            
            Focus on actionable insights and specific entry/exit strategies.
            """,
            synthesis_context
        )
        
        synthesis["ai_synthesis"] = ai_synthesis
        
        return synthesis

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range."""
        high_low = data["high"] - data["low"]
        high_close = np.abs(data["high"] - data["close"].shift())
        low_close = np.abs(data["low"] - data["close"].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        return true_range.rolling(period).mean()

    def _calculate_cci(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """Calculate Commodity Channel Index."""
        typical_price = (data["high"] + data["low"] + data["close"]) / 3
        sma_tp = typical_price.rolling(period).mean()
        mad = typical_price.rolling(period).apply(lambda x: np.abs(x - x.mean()).mean())
        
        return (typical_price - sma_tp) / (0.015 * mad)

    def _get_bb_position(self, price: float, indicators: Dict) -> str:
        """Get Bollinger Band position."""
        if len(indicators["bb_upper"]) == 0:
            return "unknown"
            
        bb_upper = indicators["bb_upper"].iloc[-1]
        bb_lower = indicators["bb_lower"].iloc[-1]
        bb_middle = indicators["bb_middle"].iloc[-1]
        
        if price > bb_upper:
            return "above_upper"
        elif price < bb_lower:
            return "below_lower"
        elif price > bb_middle:
            return "upper_half"
        else:
            return "lower_half"

    async def _detect_divergence(self, data: pd.DataFrame, indicators: Dict) -> Dict[str, bool]:
        """Detect momentum divergences."""
        # Simplified divergence detection
        # In production, this would be more sophisticated
        
        if len(data) < 20 or len(indicators["rsi"]) < 20:
            return {"bullish_divergence": False, "bearish_divergence": False}
        
        # Get recent highs/lows
        recent_data = data.tail(20)
        recent_rsi = indicators["rsi"].tail(20)
        
        price_trend = recent_data["close"].iloc[-1] - recent_data["close"].iloc[0]
        rsi_trend = recent_rsi.iloc[-1] - recent_rsi.iloc[0]
        
        bullish_divergence = price_trend < 0 and rsi_trend > 0
        bearish_divergence = price_trend > 0 and rsi_trend < 0
        
        return {
            "bullish_divergence": bullish_divergence,
            "bearish_divergence": bearish_divergence
        }

    async def _load_historical_data(self):
        """Load historical data for analysis."""
        # This would load from your data source
        # For now, we'll initialize empty structures
        self.logger.info("Loading historical data for advanced analysis...")
        
        # Initialize data structures
        for symbol in ["BTCUSDT", "ETHUSDT", "ADAUSDT"]:  # Default symbols
            self.price_data[symbol] = {}
            for timeframe in self.timeframes:
                self.price_data[symbol][timeframe] = pd.DataFrame()

    async def _get_ohlcv_data(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """Get OHLCV data for symbol and timeframe."""
        # This would fetch from your data source
        # For now, return empty DataFrame
        return self.price_data.get(symbol, {}).get(timeframe, pd.DataFrame())

    async def _cleanup_agent(self):
        """Cleanup advanced market watcher resources."""
        self.logger.info("🧹 Cleaning up Advanced Market Watcher resources")
        
        # Clear data structures
        self.price_data.clear()
        self.volume_profile.clear()
        self.order_flow.clear()
        self.market_conditions.clear()
        self.correlation_matrix.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "market_data_update":
            await self._process_market_update(message.content)
        elif message.message_type == "analysis_request":
            await self._process_analysis_request(message.content)
        elif message.message_type == "alert_request":
            await self._process_alert_request(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic comprehensive analysis."""
        while self.running:
            try:
                # Perform comprehensive market scan
                await self._comprehensive_market_scan()

                # Update market conditions
                await self._update_market_conditions()

                # Generate alerts if needed
                await self._generate_alerts()

                await asyncio.sleep(60)  # Every minute

            except Exception as e:
                self.logger.error(f"Periodic analysis error: {e}")
                await asyncio.sleep(10)

    async def _pattern_recognition_scanner(self):
        """Advanced pattern recognition scanner."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    for timeframe in self.timeframes:
                        data = await self._get_ohlcv_data(symbol, timeframe)
                        if len(data) < 50:
                            continue

                        # Detect chart patterns
                        patterns = await self.pattern_recognition.detect_patterns(data)

                        # Analyze pattern confluence
                        confluence_score = self._calculate_pattern_confluence(patterns)

                        if confluence_score > 0.7:  # High confluence
                            await self._process_pattern_signal(symbol, timeframe, patterns, confluence_score)

                await asyncio.sleep(45)  # Every 45 seconds

            except Exception as e:
                self.logger.error(f"Pattern recognition error: {e}")
                await asyncio.sleep(10)

    async def _market_regime_detector(self):
        """Detect and classify market regimes."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    # Analyze multiple timeframes for regime detection
                    regime_data = {}

                    for timeframe in ["1h", "4h", "1d"]:
                        data = await self._get_ohlcv_data(symbol, timeframe)
                        if len(data) < 100:
                            continue

                        # Calculate regime indicators
                        volatility = data["close"].pct_change().rolling(20).std()
                        trend_strength = abs(data["close"].rolling(50).mean().pct_change(20).iloc[-1])
                        volume_trend = data["volume"].rolling(20).mean().pct_change(10).iloc[-1]

                        regime_data[timeframe] = {
                            "volatility": volatility.iloc[-1] if len(volatility) > 0 else 0,
                            "trend_strength": trend_strength,
                            "volume_trend": volume_trend
                        }

                    # Classify market regime
                    if regime_data:
                        regime = await self._classify_market_regime(symbol, regime_data)
                        self.market_conditions[symbol] = regime

                await asyncio.sleep(300)  # Every 5 minutes

            except Exception as e:
                self.logger.error(f"Market regime detection error: {e}")
                await asyncio.sleep(30)

    async def _anomaly_detector(self):
        """Detect market anomalies and unusual patterns."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    data = await self._get_ohlcv_data(symbol, "1m")
                    if len(data) < 100:
                        continue

                    # Detect price anomalies
                    price_z_score = self._calculate_z_score(data["close"], 50)
                    volume_z_score = self._calculate_z_score(data["volume"], 50)

                    # Detect unusual patterns
                    if abs(price_z_score) > 3 or abs(volume_z_score) > 3:
                        anomaly = {
                            "symbol": symbol,
                            "type": "statistical_anomaly",
                            "price_z_score": price_z_score,
                            "volume_z_score": volume_z_score,
                            "timestamp": datetime.utcnow()
                        }
                        await self._process_anomaly(anomaly)

                await asyncio.sleep(30)  # Every 30 seconds

            except Exception as e:
                self.logger.error(f"Anomaly detection error: {e}")
                await asyncio.sleep(10)

    async def _correlation_analyzer(self):
        """Analyze cross-asset correlations."""
        while self.running:
            try:
                # Get price data for all monitored symbols
                price_data = {}
                for symbol in self.monitored_symbols:
                    data = await self._get_ohlcv_data(symbol, "1h")
                    if len(data) > 50:
                        price_data[symbol] = data["close"].pct_change().dropna()

                if len(price_data) > 1:
                    # Calculate correlation matrix
                    df = pd.DataFrame(price_data)
                    correlation_matrix = df.corr()

                    # Analyze correlation changes
                    await self._analyze_correlation_changes(correlation_matrix)

                    self.correlation_matrix = correlation_matrix.to_dict()

                await asyncio.sleep(600)  # Every 10 minutes

            except Exception as e:
                self.logger.error(f"Correlation analysis error: {e}")
                await asyncio.sleep(60)

    async def _volume_profile_analyzer(self):
        """Analyze volume profile and order flow."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    data = await self._get_ohlcv_data(symbol, "5m")
                    if len(data) < 50:
                        continue

                    # Calculate volume profile
                    volume_profile = self._calculate_volume_profile(data)

                    # Identify key volume levels
                    poc = self._find_point_of_control(volume_profile)
                    value_area = self._calculate_value_area(volume_profile)

                    self.volume_profile[symbol] = {
                        "profile": volume_profile,
                        "poc": poc,
                        "value_area": value_area,
                        "timestamp": datetime.utcnow()
                    }

                await asyncio.sleep(180)  # Every 3 minutes

            except Exception as e:
                self.logger.error(f"Volume profile analysis error: {e}")
                await asyncio.sleep(30)

    async def _sentiment_monitor(self):
        """Monitor market sentiment from multiple sources."""
        while self.running:
            try:
                # Analyze sentiment from various sources
                sentiment_data = await self.sentiment_analyzer.get_market_sentiment()

                for symbol in self.monitored_symbols:
                    if symbol in sentiment_data:
                        sentiment = sentiment_data[symbol]

                        # Process sentiment signals
                        if abs(sentiment["score"]) > 0.7:  # Strong sentiment
                            await self._process_sentiment_signal(symbol, sentiment)

                await asyncio.sleep(120)  # Every 2 minutes

            except Exception as e:
                self.logger.error(f"Sentiment monitoring error: {e}")
                await asyncio.sleep(30)

    async def _predictive_modeling(self):
        """Advanced predictive modeling with confidence intervals."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    data = await self._get_ohlcv_data(symbol, "1h")
                    if len(data) < 200:
                        continue

                    # Generate price predictions
                    predictions = await self._generate_price_predictions(symbol, data)

                    # Calculate confidence intervals
                    confidence_intervals = self._calculate_confidence_intervals(predictions)

                    # Store predictions
                    prediction_data = {
                        "symbol": symbol,
                        "predictions": predictions,
                        "confidence_intervals": confidence_intervals,
                        "timestamp": datetime.utcnow()
                    }

                    await self._process_predictions(prediction_data)

                await asyncio.sleep(900)  # Every 15 minutes

            except Exception as e:
                self.logger.error(f"Predictive modeling error: {e}")
                await asyncio.sleep(60)

    async def _risk_assessment(self):
        """Comprehensive risk assessment."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    # Calculate various risk metrics
                    risk_metrics = await self._calculate_risk_metrics(symbol)

                    # Assess overall risk level
                    risk_level = self._assess_risk_level(risk_metrics)

                    # Generate risk alerts if needed
                    if risk_level == "HIGH":
                        await self._generate_risk_alert(symbol, risk_metrics)

                await asyncio.sleep(300)  # Every 5 minutes

            except Exception as e:
                self.logger.error(f"Risk assessment error: {e}")
                await asyncio.sleep(30)

    def _calculate_pattern_confluence(self, patterns: List[Dict]) -> float:
        """Calculate pattern confluence score."""
        if not patterns:
            return 0.0

        total_weight = 0.0
        weighted_score = 0.0

        for pattern in patterns:
            pattern_type = pattern.get("type", "")
            confidence = pattern.get("confidence", 0.0)
            weight = self.pattern_weights.get(pattern_type, 0.5)

            total_weight += weight
            weighted_score += confidence * weight

        return weighted_score / total_weight if total_weight > 0 else 0.0

    def _calculate_z_score(self, series: pd.Series, window: int) -> float:
        """Calculate Z-score for anomaly detection."""
        if len(series) < window:
            return 0.0

        recent_data = series.tail(window)
        mean = recent_data.mean()
        std = recent_data.std()

        if std == 0:
            return 0.0

        return (series.iloc[-1] - mean) / std

    def _calculate_volume_profile(self, data: pd.DataFrame) -> Dict[float, float]:
        """Calculate volume profile."""
        volume_profile = {}

        for _, row in data.iterrows():
            price_level = round(row["close"], 2)
            volume = row["volume"]

            if price_level in volume_profile:
                volume_profile[price_level] += volume
            else:
                volume_profile[price_level] = volume

        return volume_profile

    def _find_point_of_control(self, volume_profile: Dict[float, float]) -> float:
        """Find Point of Control (highest volume price level)."""
        if not volume_profile:
            return 0.0

        return max(volume_profile, key=volume_profile.get)

    def _calculate_value_area(self, volume_profile: Dict[float, float]) -> Dict[str, float]:
        """Calculate Value Area (70% of volume)."""
        if not volume_profile:
            return {"high": 0.0, "low": 0.0}

        total_volume = sum(volume_profile.values())
        target_volume = total_volume * 0.7

        # Sort by volume descending
        sorted_levels = sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)

        accumulated_volume = 0.0
        value_area_levels = []

        for price, volume in sorted_levels:
            accumulated_volume += volume
            value_area_levels.append(price)

            if accumulated_volume >= target_volume:
                break

        return {
            "high": max(value_area_levels) if value_area_levels else 0.0,
            "low": min(value_area_levels) if value_area_levels else 0.0
        }
