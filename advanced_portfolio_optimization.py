#!/usr/bin/env python3
"""
Advanced Portfolio Optimization Engine
Professional-grade portfolio management with Modern Portfolio Theory, Black-Litterman, and advanced optimization
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import json
from scipy.optimize import minimize
from scipy import linalg
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger("AdvancedPortfolioOptimization")


@dataclass
class Asset:
    """Asset data structure."""
    symbol: str
    weight: float
    expected_return: float
    volatility: float
    beta: float
    sector: str
    market_cap: float
    liquidity_score: float


@dataclass
class PortfolioConstraints:
    """Portfolio constraints data structure."""
    max_weight: float = 0.20
    min_weight: float = 0.01
    max_sector_weight: float = 0.40
    max_volatility: float = 0.25
    min_expected_return: float = 0.08
    max_drawdown: float = 0.15
    liquidity_threshold: float = 0.7


class AdvancedPortfolioOptimizer:
    """Advanced portfolio optimization with multiple methodologies."""
    
    def __init__(self):
        self.assets = {}
        self.correlation_matrix = None
        self.covariance_matrix = None
        self.optimization_history = []
        self.performance_metrics = {}
        
        # Risk models
        self.risk_models = {
            "historical": self._historical_risk_model,
            "exponential": self._exponential_risk_model,
            "shrinkage": self._shrinkage_risk_model,
            "factor": self._factor_risk_model
        }
        
        # Optimization methods
        self.optimization_methods = {
            "mean_variance": self._mean_variance_optimization,
            "black_litterman": self._black_litterman_optimization,
            "risk_parity": self._risk_parity_optimization,
            "hierarchical": self._hierarchical_risk_parity,
            "cvar": self._cvar_optimization,
            "robust": self._robust_optimization
        }
        
    def initialize_portfolio(self, assets_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Initialize portfolio with asset data."""
        try:
            self.assets = {}
            
            for asset_data in assets_data:
                asset = Asset(
                    symbol=asset_data.get("symbol", "UNKNOWN"),
                    weight=asset_data.get("weight", 0.0),
                    expected_return=asset_data.get("expected_return", 0.08),
                    volatility=asset_data.get("volatility", 0.20),
                    beta=asset_data.get("beta", 1.0),
                    sector=asset_data.get("sector", "unknown"),
                    market_cap=asset_data.get("market_cap", 1000000000),
                    liquidity_score=asset_data.get("liquidity_score", 0.8)
                )
                self.assets[asset.symbol] = asset
            
            # Generate correlation matrix
            self._generate_correlation_matrix()
            
            logger.info(f"✅ Portfolio initialized with {len(self.assets)} assets")
            
            return {
                "assets_count": len(self.assets),
                "total_weight": sum(asset.weight for asset in self.assets.values()),
                "avg_expected_return": np.mean([asset.expected_return for asset in self.assets.values()]),
                "avg_volatility": np.mean([asset.volatility for asset in self.assets.values()]),
                "sectors": list(set(asset.sector for asset in self.assets.values()))
            }
            
        except Exception as e:
            logger.error(f"Portfolio initialization error: {e}")
            return {"error": str(e)}
    
    def _generate_correlation_matrix(self):
        """Generate realistic correlation matrix."""
        n_assets = len(self.assets)
        symbols = list(self.assets.keys())
        
        # Generate base correlation matrix
        np.random.seed(42)  # Reproducible
        correlation_matrix = np.random.uniform(0.1, 0.8, (n_assets, n_assets))
        
        # Make symmetric and set diagonal to 1
        correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
        np.fill_diagonal(correlation_matrix, 1.0)
        
        # Ensure positive semi-definite
        eigenvals, eigenvecs = linalg.eigh(correlation_matrix)
        eigenvals = np.maximum(eigenvals, 0.01)  # Ensure positive eigenvalues
        correlation_matrix = eigenvecs @ np.diag(eigenvals) @ eigenvecs.T
        
        # Normalize to correlation matrix
        d = np.sqrt(np.diag(correlation_matrix))
        correlation_matrix = correlation_matrix / np.outer(d, d)
        
        self.correlation_matrix = pd.DataFrame(correlation_matrix, index=symbols, columns=symbols)
        
        # Generate covariance matrix
        volatilities = np.array([self.assets[symbol].volatility for symbol in symbols])
        self.covariance_matrix = np.outer(volatilities, volatilities) * correlation_matrix
        
        logger.info(f"✅ Generated {n_assets}x{n_assets} correlation and covariance matrices")
    
    def optimize_portfolio(self, method: str = "mean_variance", 
                          constraints: PortfolioConstraints = None,
                          market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Optimize portfolio using specified method."""
        if not self.assets:
            return {"error": "Portfolio not initialized"}
        
        if constraints is None:
            constraints = PortfolioConstraints()
        
        try:
            optimization_func = self.optimization_methods.get(method)
            if not optimization_func:
                return {"error": f"Unknown optimization method: {method}"}
            
            # Run optimization
            result = optimization_func(constraints, market_views)
            
            # Store optimization history
            optimization_record = {
                "method": method,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "result": result,
                "constraints": constraints.__dict__,
                "market_views": market_views
            }
            self.optimization_history.append(optimization_record)
            
            logger.info(f"✅ Portfolio optimized using {method}")
            
            return result
            
        except Exception as e:
            logger.error(f"Portfolio optimization error: {e}")
            return {"error": str(e)}
    
    def _mean_variance_optimization(self, constraints: PortfolioConstraints, 
                                   market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Mean-variance optimization (Markowitz)."""
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        # Expected returns
        expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
        
        # Objective function: minimize portfolio variance
        def objective(weights):
            portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
            return portfolio_variance
        
        # Constraints
        constraints_list = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},  # Weights sum to 1
        ]
        
        # Bounds for individual weights
        bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
        
        # Initial guess (equal weights)
        initial_weights = np.array([1.0 / n_assets] * n_assets)
        
        # Optimize
        result = minimize(objective, initial_weights, method='SLSQP', 
                         bounds=bounds, constraints=constraints_list)
        
        if result.success:
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_volatility = np.sqrt(np.dot(optimal_weights, np.dot(self.covariance_matrix, optimal_weights)))
            sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Update asset weights
            for i, symbol in enumerate(symbols):
                self.assets[symbol].weight = optimal_weights[i]
            
            return {
                "method": "mean_variance",
                "success": True,
                "optimal_weights": {symbol: optimal_weights[i] for i, symbol in enumerate(symbols)},
                "portfolio_return": round(portfolio_return, 4),
                "portfolio_volatility": round(portfolio_volatility, 4),
                "sharpe_ratio": round(sharpe_ratio, 4),
                "optimization_details": {
                    "iterations": result.nit,
                    "function_evaluations": result.nfev
                }
            }
        else:
            return {"method": "mean_variance", "success": False, "error": result.message}
    
    def _black_litterman_optimization(self, constraints: PortfolioConstraints,
                                     market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Black-Litterman optimization with market views."""
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        # Market capitalization weights (proxy for market portfolio)
        market_caps = np.array([self.assets[symbol].market_cap for symbol in symbols])
        market_weights = market_caps / np.sum(market_caps)
        
        # Risk aversion parameter
        risk_aversion = 3.0
        
        # Implied equilibrium returns
        implied_returns = risk_aversion * np.dot(self.covariance_matrix, market_weights)
        
        # Market views (if provided)
        if market_views:
            # Picking matrix (which assets have views)
            P = np.zeros((len(market_views), n_assets))
            Q = np.zeros(len(market_views))
            
            for i, (symbol, view) in enumerate(market_views.items()):
                if symbol in symbols:
                    asset_idx = symbols.index(symbol)
                    P[i, asset_idx] = 1.0
                    Q[i] = view
            
            # Uncertainty in views (tau parameter)
            tau = 0.025
            
            # Omega matrix (uncertainty in views)
            Omega = np.eye(len(market_views)) * 0.01
            
            # Black-Litterman formula
            M1 = linalg.inv(tau * self.covariance_matrix)
            M2 = np.dot(P.T, np.dot(linalg.inv(Omega), P))
            M3 = np.dot(linalg.inv(tau * self.covariance_matrix), implied_returns)
            M4 = np.dot(P.T, np.dot(linalg.inv(Omega), Q))
            
            # New expected returns
            expected_returns = np.dot(linalg.inv(M1 + M2), M3 + M4)
            
            # New covariance matrix
            new_covariance = linalg.inv(M1 + M2)
        else:
            expected_returns = implied_returns
            new_covariance = self.covariance_matrix
        
        # Optimize with new expected returns
        def objective(weights):
            portfolio_return = np.dot(weights, expected_returns)
            portfolio_variance = np.dot(weights, np.dot(new_covariance, weights))
            return -portfolio_return + 0.5 * risk_aversion * portfolio_variance
        
        # Constraints
        constraints_list = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},
        ]
        
        bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
        initial_weights = market_weights
        
        result = minimize(objective, initial_weights, method='SLSQP',
                         bounds=bounds, constraints=constraints_list)
        
        if result.success:
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_volatility = np.sqrt(np.dot(optimal_weights, np.dot(new_covariance, optimal_weights)))
            sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
            
            return {
                "method": "black_litterman",
                "success": True,
                "optimal_weights": {symbol: optimal_weights[i] for i, symbol in enumerate(symbols)},
                "portfolio_return": round(portfolio_return, 4),
                "portfolio_volatility": round(portfolio_volatility, 4),
                "sharpe_ratio": round(sharpe_ratio, 4),
                "market_views_applied": len(market_views) if market_views else 0,
                "implied_returns": {symbol: implied_returns[i] for i, symbol in enumerate(symbols)}
            }
        else:
            return {"method": "black_litterman", "success": False, "error": result.message}
    
    def _risk_parity_optimization(self, constraints: PortfolioConstraints,
                                 market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Risk parity optimization (equal risk contribution)."""
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        def risk_budget_objective(weights):
            """Objective function for risk parity."""
            portfolio_volatility = np.sqrt(np.dot(weights, np.dot(self.covariance_matrix, weights)))
            marginal_contrib = np.dot(self.covariance_matrix, weights) / portfolio_volatility
            contrib = weights * marginal_contrib
            
            # Target equal risk contribution
            target_contrib = np.ones(n_assets) / n_assets
            return np.sum((contrib / np.sum(contrib) - target_contrib) ** 2)
        
        constraints_list = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},
        ]
        
        bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
        initial_weights = np.array([1.0 / n_assets] * n_assets)
        
        result = minimize(risk_budget_objective, initial_weights, method='SLSQP',
                         bounds=bounds, constraints=constraints_list)
        
        if result.success:
            optimal_weights = result.x
            expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_volatility = np.sqrt(np.dot(optimal_weights, np.dot(self.covariance_matrix, optimal_weights)))
            
            # Calculate risk contributions
            marginal_contrib = np.dot(self.covariance_matrix, optimal_weights) / portfolio_volatility
            risk_contrib = optimal_weights * marginal_contrib
            risk_contrib_pct = risk_contrib / np.sum(risk_contrib)
            
            return {
                "method": "risk_parity",
                "success": True,
                "optimal_weights": {symbol: optimal_weights[i] for i, symbol in enumerate(symbols)},
                "portfolio_return": round(portfolio_return, 4),
                "portfolio_volatility": round(portfolio_volatility, 4),
                "risk_contributions": {symbol: risk_contrib_pct[i] for i, symbol in enumerate(symbols)},
                "risk_concentration": round(np.sum(risk_contrib_pct ** 2), 4)
            }
        else:
            return {"method": "risk_parity", "success": False, "error": result.message}
    
    def _hierarchical_risk_parity(self, constraints: PortfolioConstraints,
                                 market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Hierarchical Risk Parity (HRP) optimization."""
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        # Simplified HRP implementation
        # Step 1: Cluster assets based on correlation
        distance_matrix = np.sqrt(0.5 * (1 - self.correlation_matrix.values))
        
        # Step 2: Hierarchical clustering (simplified)
        # For simplicity, we'll use a basic clustering approach
        clusters = self._simple_clustering(distance_matrix, symbols)
        
        # Step 3: Allocate weights hierarchically
        weights = self._allocate_hrp_weights(clusters, symbols)
        
        expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
        portfolio_return = np.dot(weights, expected_returns)
        portfolio_volatility = np.sqrt(np.dot(weights, np.dot(self.covariance_matrix, weights)))
        
        return {
            "method": "hierarchical_risk_parity",
            "success": True,
            "optimal_weights": {symbol: weights[i] for i, symbol in enumerate(symbols)},
            "portfolio_return": round(portfolio_return, 4),
            "portfolio_volatility": round(portfolio_volatility, 4),
            "clusters": clusters,
            "diversification_ratio": round(self._calculate_diversification_ratio(weights), 4)
        }
    
    def _simple_clustering(self, distance_matrix: np.ndarray, symbols: List[str]) -> Dict[str, List[str]]:
        """Simple clustering based on distance matrix."""
        n_assets = len(symbols)
        
        # Simple clustering: group assets with correlation > 0.6
        clusters = {}
        cluster_id = 0
        assigned = set()
        
        for i in range(n_assets):
            if symbols[i] in assigned:
                continue
                
            cluster = [symbols[i]]
            assigned.add(symbols[i])
            
            for j in range(i + 1, n_assets):
                if symbols[j] not in assigned and distance_matrix[i, j] < 0.6:
                    cluster.append(symbols[j])
                    assigned.add(symbols[j])
            
            clusters[f"cluster_{cluster_id}"] = cluster
            cluster_id += 1
        
        return clusters
    
    def _allocate_hrp_weights(self, clusters: Dict[str, List[str]], symbols: List[str]) -> np.ndarray:
        """Allocate weights using HRP methodology."""
        n_assets = len(symbols)
        weights = np.zeros(n_assets)
        
        # Equal weight within clusters, then risk parity between clusters
        for cluster_assets in clusters.values():
            cluster_weight = 1.0 / len(clusters)  # Equal cluster weights
            asset_weight = cluster_weight / len(cluster_assets)  # Equal within cluster
            
            for asset in cluster_assets:
                if asset in symbols:
                    asset_idx = symbols.index(asset)
                    weights[asset_idx] = asset_weight
        
        return weights
    
    def _calculate_diversification_ratio(self, weights: np.ndarray) -> float:
        """Calculate diversification ratio."""
        symbols = list(self.assets.keys())
        volatilities = np.array([self.assets[symbol].volatility for symbol in symbols])
        
        weighted_avg_vol = np.dot(weights, volatilities)
        portfolio_vol = np.sqrt(np.dot(weights, np.dot(self.covariance_matrix, weights)))
        
        return weighted_avg_vol / portfolio_vol if portfolio_vol > 0 else 1.0
    
    def _cvar_optimization(self, constraints: PortfolioConstraints,
                          market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Conditional Value at Risk (CVaR) optimization."""
        # Simplified CVaR optimization
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        # Generate scenarios (simplified)
        n_scenarios = 1000
        np.random.seed(42)
        scenarios = np.random.multivariate_normal(
            mean=[self.assets[symbol].expected_return for symbol in symbols],
            cov=self.covariance_matrix,
            size=n_scenarios
        )
        
        # CVaR optimization (simplified approach)
        def cvar_objective(weights):
            portfolio_returns = np.dot(scenarios, weights)
            var_95 = np.percentile(portfolio_returns, 5)
            cvar_95 = np.mean(portfolio_returns[portfolio_returns <= var_95])
            return -cvar_95  # Maximize CVaR (minimize negative CVaR)
        
        constraints_list = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},
        ]
        
        bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
        initial_weights = np.array([1.0 / n_assets] * n_assets)
        
        result = minimize(cvar_objective, initial_weights, method='SLSQP',
                         bounds=bounds, constraints=constraints_list)
        
        if result.success:
            optimal_weights = result.x
            expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_volatility = np.sqrt(np.dot(optimal_weights, np.dot(self.covariance_matrix, optimal_weights)))
            
            # Calculate CVaR
            portfolio_returns = np.dot(scenarios, optimal_weights)
            var_95 = np.percentile(portfolio_returns, 5)
            cvar_95 = np.mean(portfolio_returns[portfolio_returns <= var_95])
            
            return {
                "method": "cvar",
                "success": True,
                "optimal_weights": {symbol: optimal_weights[i] for i, symbol in enumerate(symbols)},
                "portfolio_return": round(portfolio_return, 4),
                "portfolio_volatility": round(portfolio_volatility, 4),
                "var_95": round(var_95, 4),
                "cvar_95": round(cvar_95, 4),
                "scenarios_used": n_scenarios
            }
        else:
            return {"method": "cvar", "success": False, "error": result.message}
    
    def _robust_optimization(self, constraints: PortfolioConstraints,
                           market_views: Dict[str, float] = None) -> Dict[str, Any]:
        """Robust optimization with uncertainty in parameters."""
        symbols = list(self.assets.keys())
        n_assets = len(symbols)
        
        # Add uncertainty to expected returns
        expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
        uncertainty_level = 0.1  # 10% uncertainty
        
        def robust_objective(weights):
            # Worst-case expected return (conservative)
            worst_case_returns = expected_returns * (1 - uncertainty_level)
            portfolio_return = np.dot(weights, worst_case_returns)
            portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
            
            # Robust Sharpe ratio
            return -(portfolio_return / np.sqrt(portfolio_variance)) if portfolio_variance > 0 else 0
        
        constraints_list = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1.0},
        ]
        
        bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
        initial_weights = np.array([1.0 / n_assets] * n_assets)
        
        result = minimize(robust_objective, initial_weights, method='SLSQP',
                         bounds=bounds, constraints=constraints_list)
        
        if result.success:
            optimal_weights = result.x
            portfolio_return = np.dot(optimal_weights, expected_returns)
            portfolio_volatility = np.sqrt(np.dot(optimal_weights, np.dot(self.covariance_matrix, optimal_weights)))
            robust_return = np.dot(optimal_weights, expected_returns * (1 - uncertainty_level))
            
            return {
                "method": "robust",
                "success": True,
                "optimal_weights": {symbol: optimal_weights[i] for i, symbol in enumerate(symbols)},
                "portfolio_return": round(portfolio_return, 4),
                "robust_return": round(robust_return, 4),
                "portfolio_volatility": round(portfolio_volatility, 4),
                "uncertainty_level": uncertainty_level,
                "robustness_score": round((robust_return / portfolio_return), 4)
            }
        else:
            return {"method": "robust", "success": False, "error": result.message}
    
    # Risk model implementations (placeholder methods)
    def _historical_risk_model(self): pass
    def _exponential_risk_model(self): pass
    def _shrinkage_risk_model(self): pass
    def _factor_risk_model(self): pass
    
    def get_portfolio_analytics(self) -> Dict[str, Any]:
        """Get comprehensive portfolio analytics."""
        if not self.assets:
            return {"error": "Portfolio not initialized"}
        
        symbols = list(self.assets.keys())
        weights = np.array([self.assets[symbol].weight for symbol in symbols])
        expected_returns = np.array([self.assets[symbol].expected_return for symbol in symbols])
        
        # Portfolio metrics
        portfolio_return = np.dot(weights, expected_returns)
        portfolio_volatility = np.sqrt(np.dot(weights, np.dot(self.covariance_matrix, weights)))
        sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # Concentration metrics
        herfindahl_index = np.sum(weights ** 2)
        effective_assets = 1 / herfindahl_index
        
        # Sector allocation
        sector_allocation = {}
        for asset in self.assets.values():
            sector = asset.sector
            if sector not in sector_allocation:
                sector_allocation[sector] = 0
            sector_allocation[sector] += asset.weight
        
        return {
            "portfolio_metrics": {
                "expected_return": round(portfolio_return, 4),
                "volatility": round(portfolio_volatility, 4),
                "sharpe_ratio": round(sharpe_ratio, 4),
                "diversification_ratio": round(self._calculate_diversification_ratio(weights), 4)
            },
            "concentration_metrics": {
                "herfindahl_index": round(herfindahl_index, 4),
                "effective_number_assets": round(effective_assets, 2),
                "max_weight": round(np.max(weights), 4),
                "min_weight": round(np.min(weights), 4)
            },
            "sector_allocation": {k: round(v, 4) for k, v in sector_allocation.items()},
            "asset_weights": {symbol: round(self.assets[symbol].weight, 4) for symbol in symbols},
            "optimization_history_count": len(self.optimization_history)
        }


# Test the Portfolio Optimizer
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("💼 Testing Advanced Portfolio Optimization Engine")
    print("=" * 70)
    
    # Initialize optimizer
    optimizer = AdvancedPortfolioOptimizer()
    
    # Sample assets data
    assets_data = [
        {"symbol": "BTCUSDT", "weight": 0.3, "expected_return": 0.15, "volatility": 0.4, "beta": 1.5, "sector": "crypto", "market_cap": ************, "liquidity_score": 0.95},
        {"symbol": "ETHUSDT", "weight": 0.25, "expected_return": 0.12, "volatility": 0.35, "beta": 1.3, "sector": "crypto", "market_cap": ************, "liquidity_score": 0.9},
        {"symbol": "ADAUSDT", "weight": 0.15, "expected_return": 0.10, "volatility": 0.45, "beta": 1.2, "sector": "crypto", "market_cap": 50000000000, "liquidity_score": 0.8},
        {"symbol": "SOLUSDT", "weight": 0.15, "expected_return": 0.18, "volatility": 0.5, "beta": 1.4, "sector": "crypto", "market_cap": 40000000000, "liquidity_score": 0.85},
        {"symbol": "DOTUSDT", "weight": 0.15, "expected_return": 0.08, "volatility": 0.3, "beta": 1.1, "sector": "crypto", "market_cap": 20000000000, "liquidity_score": 0.75}
    ]
    
    # Initialize portfolio
    print("🔧 Initializing portfolio...")
    init_result = optimizer.initialize_portfolio(assets_data)
    print(f"Portfolio initialized: {init_result}")
    
    # Test different optimization methods
    methods = ["mean_variance", "black_litterman", "risk_parity", "hierarchical", "cvar", "robust"]
    
    for method in methods:
        print(f"\n📊 Testing {method} optimization...")
        result = optimizer.optimize_portfolio(method)
        if result.get("success"):
            print(f"✅ {method}: Return={result.get('portfolio_return', 0):.2%}, Vol={result.get('portfolio_volatility', 0):.2%}")
        else:
            print(f"❌ {method}: {result.get('error', 'Failed')}")
    
    # Get analytics
    print("\n📈 Portfolio Analytics:")
    analytics = optimizer.get_portfolio_analytics()
    print(f"Sharpe Ratio: {analytics['portfolio_metrics']['sharpe_ratio']:.2f}")
    print(f"Effective Assets: {analytics['concentration_metrics']['effective_number_assets']:.1f}")
    
    print("\n✅ Portfolio Optimization test completed!")
