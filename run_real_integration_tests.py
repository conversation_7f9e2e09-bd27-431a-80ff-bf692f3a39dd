#!/usr/bin/env python3
"""
Real Integration Test Suite for NORYON V2 AI Trading System
Tests actual functionality with real data and your available Ollama models.
"""

import asyncio
import logging
import sys
import time
import json
import requests
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import argparse
import traceback

# Add src to path
sys.path.append('src')

from agents.real_agent_manager import RealAgentManager
from services.ai_service import ai_service


class RealIntegrationTester:
    """
    Comprehensive integration tester for the real AI trading system.

    Tests:
    - Ollama model availability and inference
    - Real market data connectivity
    - Agent initialization and functionality
    - Cross-agent communication
    - Performance under load
    - Error handling and recovery
    - Resource usage monitoring
    """

    def __init__(self):
        self.logger = self._setup_logging()
        self.agent_manager = RealAgentManager()
        self.test_results = {}
        self.start_time = None

    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging for tests."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'integration_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        return logging.getLogger("RealIntegrationTester")

    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive integration tests."""
        self.logger.info("🚀 STARTING REAL INTEGRATION TESTS")
        self.logger.info("=" * 80)
        self.start_time = datetime.utcnow()

        test_suite = [
            ("🔧 System Prerequisites", self._test_system_prerequisites),
            ("🧠 Ollama Model Verification", self._test_ollama_models),
            ("🌐 API Connectivity", self._test_api_connectivity),
            ("🤖 Agent Initialization", self._test_agent_initialization),
            ("📊 Market Data Processing", self._test_market_data_processing),
            ("🧪 AI Model Inference", self._test_ai_inference),
            ("⚡ Agent Functionality", self._test_agent_functionality),
            ("🔄 Cross-Agent Communication", self._test_cross_agent_communication),
            ("📈 Performance Testing", self._test_performance),
            ("🛡️ Error Handling", self._test_error_handling),
            ("💾 Resource Usage", self._test_resource_usage),
            ("🎯 End-to-End Workflow", self._test_end_to_end_workflow)
        ]

        for test_name, test_func in test_suite:
            try:
                self.logger.info(f"\n{'='*20} {test_name} {'='*20}")
                result = await test_func()
                self.test_results[test_name] = result

                if result.get("passed", False):
                    self.logger.info(f"✅ {test_name}: PASSED")
                else:
                    self.logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")

            except Exception as e:
                self.logger.error(f"💥 {test_name}: CRASHED - {e}")
                self.test_results[test_name] = {"passed": False, "error": str(e), "traceback": traceback.format_exc()}

        # Generate final report
        return await self._generate_test_report()

    async def _test_system_prerequisites(self) -> Dict[str, Any]:
        """Test system prerequisites."""
        self.logger.info("🔍 Checking system prerequisites...")

        checks = {}

        # Check Python version
        python_version = sys.version_info
        checks["python_version"] = {
            "required": "3.8+",
            "actual": f"{python_version.major}.{python_version.minor}.{python_version.micro}",
            "passed": python_version >= (3, 8)
        }

        # Check required packages
        required_packages = ["numpy", "pandas", "requests", "asyncio", "talib", "scipy", "psutil"]
        for package in required_packages:
            try:
                __import__(package)
                checks[f"package_{package}"] = {"passed": True}
            except ImportError:
                checks[f"package_{package}"] = {"passed": False, "error": f"{package} not installed"}

        # Check Ollama installation
        try:
            result = subprocess.run(["ollama", "--version"], capture_output=True, text=True, timeout=5)
            checks["ollama_installed"] = {
                "passed": result.returncode == 0,
                "version": result.stdout.strip() if result.returncode == 0 else "Not found"
            }
        except (subprocess.TimeoutExpired, FileNotFoundError):
            checks["ollama_installed"] = {"passed": False, "error": "Ollama not found or not responding"}

        all_passed = all(check.get("passed", False) for check in checks.values())

        return {
            "passed": all_passed,
            "checks": checks,
            "summary": f"{sum(1 for c in checks.values() if c.get('passed', False))}/{len(checks)} checks passed"
        }

    async def _test_ollama_models(self) -> Dict[str, Any]:
        """Test Ollama model availability and functionality."""
        self.logger.info("🧠 Testing Ollama models...")

        # Verify models are available
        model_status = await self.agent_manager.verify_ollama_models()
        available_models = [model for model, status in model_status.items() if status]

        self.logger.info(f"📊 Found {len(available_models)} available models")

        # Test inference on available models
        inference_tests = {}
        for model_name in available_models[:3]:  # Test first 3 models
            self.logger.info(f"🧪 Testing inference for {model_name}...")
            inference_result = await self.agent_manager.test_model_inference(model_name)
            inference_tests[model_name] = inference_result

        successful_inferences = sum(1 for result in inference_tests.values() if result.get("available", False))

        return {
            "passed": len(available_models) > 0 and successful_inferences > 0,
            "available_models": len(available_models),
            "total_models": len(model_status),
            "inference_tests": inference_tests,
            "successful_inferences": successful_inferences
        }

    async def _test_api_connectivity(self) -> Dict[str, Any]:
        """Test external API connectivity."""
        self.logger.info("🌐 Testing API connectivity...")

        api_tests = {}

        # Test Binance API
        try:
            start_time = time.time()
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
            response_time = time.time() - start_time

            api_tests["binance_ping"] = {
                "passed": response.status_code == 200,
                "response_time": response_time,
                "status_code": response.status_code
            }

            # Test actual data endpoint
            ticker_response = requests.get("https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT", timeout=10)
            api_tests["binance_data"] = {
                "passed": ticker_response.status_code == 200,
                "data_received": bool(ticker_response.json() if ticker_response.status_code == 200 else False)
            }

        except Exception as e:
            api_tests["binance"] = {"passed": False, "error": str(e)}

        # Test Ollama API
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            api_tests["ollama_api"] = {
                "passed": response.status_code == 200,
                "models_listed": len(response.json().get("models", [])) if response.status_code == 200 else 0
            }
        except Exception as e:
            api_tests["ollama_api"] = {"passed": False, "error": str(e)}

        all_passed = all(test.get("passed", False) for test in api_tests.values())

        return {
            "passed": all_passed,
            "tests": api_tests,
            "summary": f"{sum(1 for t in api_tests.values() if t.get('passed', False))}/{len(api_tests)} API tests passed"
        }

    async def _test_agent_initialization(self) -> Dict[str, Any]:
        """Test agent initialization."""
        self.logger.info("🤖 Testing agent initialization...")

        # Initialize agents
        init_results = await self.agent_manager.initialize_real_agents()

        successful_agents = init_results["summary"]["successful_agents"]
        total_agents = len(init_results["agents"])

        return {
            "passed": successful_agents > 0,
            "successful_agents": successful_agents,
            "total_agents": total_agents,
            "agent_results": init_results["agents"],
            "system_ready": init_results["summary"]["system_ready"]
        }

    async def _test_market_data_processing(self) -> Dict[str, Any]:
        """Test market data processing capabilities."""
        self.logger.info("📊 Testing market data processing...")

        try:
            # Start agents
            await self.agent_manager.start_real_agents()

            # Wait for data collection
            await asyncio.sleep(10)

            # Test market watcher
            if "real_market_watcher" in self.agent_manager.agents:
                market_data = await self.agent_manager.agents["real_market_watcher"].get_current_market_data()

                data_quality = {
                    "has_prices": bool(market_data.get("prices")),
                    "symbol_count": len(market_data.get("prices", {})),
                    "has_alerts": "alerts" in market_data,
                    "data_fresh": bool(market_data.get("timestamp"))
                }

                return {
                    "passed": data_quality["has_prices"] and data_quality["symbol_count"] > 0,
                    "data_quality": data_quality,
                    "sample_data": {k: v for k, v in list(market_data.get("prices", {}).items())[:2]}
                }
            else:
                return {"passed": False, "error": "Market watcher not available"}

        except Exception as e:
            return {"passed": False, "error": str(e)}

    async def _test_ai_inference(self) -> Dict[str, Any]:
        """Test AI model inference capabilities."""
        self.logger.info("🧪 Testing AI inference...")

        inference_tests = {}

        # Test different types of prompts
        test_prompts = {
            "simple_math": "What is 15 + 27? Respond with just the number.",
            "market_analysis": "Analyze this: BTC price is $45,000, up 5% today with high volume. Bullish or bearish?",
            "risk_assessment": "If a portfolio has 60% BTC and 40% ETH, what are the main risks?"
        }

        available_models = ["marco-o1:7b", "magistral:24b", "command-r:35b"]

        for model_name in available_models:
            model_tests = {}

            for test_name, prompt in test_prompts.items():
                try:
                    start_time = time.time()
                    response = await ai_service.generate_response(model_name, prompt, {})
                    response_time = time.time() - start_time

                    model_tests[test_name] = {
                        "passed": bool(response and len(str(response)) > 10),
                        "response_time": response_time,
                        "response_length": len(str(response)) if response else 0,
                        "sample_response": str(response)[:100] + "..." if response and len(str(response)) > 100 else str(response)
                    }

                except Exception as e:
                    model_tests[test_name] = {"passed": False, "error": str(e)}

            inference_tests[model_name] = model_tests

        # Calculate overall success
        total_tests = sum(len(tests) for tests in inference_tests.values())
        passed_tests = sum(sum(1 for test in tests.values() if test.get("passed", False))
                          for tests in inference_tests.values())

        return {
            "passed": passed_tests > 0,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "model_tests": inference_tests
        }

    async def _test_agent_functionality(self) -> Dict[str, Any]:
        """Test individual agent functionality."""
        self.logger.info("⚡ Testing agent functionality...")

        functionality_tests = {}

        # Test each agent's core functionality
        for agent_name, agent in self.agent_manager.agents.items():
            agent_tests = {}

            try:
                if agent_name == "real_market_watcher":
                    # Test market data retrieval
                    market_data = await agent.get_current_market_data()
                    agent_tests["data_retrieval"] = {
                        "passed": bool(market_data and "prices" in market_data),
                        "symbols_monitored": len(market_data.get("prices", {}))
                    }

                elif agent_name == "real_strategy_researcher":
                    # Test strategy performance
                    strategy_data = await agent.get_strategy_performance()
                    agent_tests["strategy_analysis"] = {
                        "passed": bool(strategy_data and "strategies" in strategy_data),
                        "strategies_count": strategy_data.get("total_strategies", 0)
                    }

                elif agent_name == "real_risk_officer":
                    # Test risk calculations
                    risk_data = await agent.get_risk_summary()
                    agent_tests["risk_calculations"] = {
                        "passed": bool(risk_data and "portfolios" in risk_data),
                        "portfolios_monitored": len(risk_data.get("portfolios", {}))
                    }

                functionality_tests[agent_name] = agent_tests

            except Exception as e:
                functionality_tests[agent_name] = {"error": str(e), "passed": False}

        # Calculate overall functionality score
        total_agents = len(functionality_tests)
        working_agents = sum(1 for tests in functionality_tests.values()
                           if any(test.get("passed", False) for test in tests.values() if isinstance(test, dict)))

        return {
            "passed": working_agents > 0,
            "working_agents": working_agents,
            "total_agents": total_agents,
            "functionality_tests": functionality_tests
        }

    async def _test_performance(self) -> Dict[str, Any]:
        """Test system performance under load."""
        self.logger.info("📈 Testing performance...")

        performance_metrics = {}

        # Test response times
        start_time = time.time()

        # Simulate concurrent requests
        tasks = []
        for i in range(5):  # 5 concurrent requests
            if "real_market_watcher" in self.agent_manager.agents:
                task = self.agent_manager.agents["real_market_watcher"].get_current_market_data()
                tasks.append(task)

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()

            successful_requests = sum(1 for result in results if not isinstance(result, Exception))
            total_time = end_time - start_time

            performance_metrics["concurrent_requests"] = {
                "total_requests": len(tasks),
                "successful_requests": successful_requests,
                "total_time": total_time,
                "avg_response_time": total_time / len(tasks),
                "success_rate": successful_requests / len(tasks)
            }

        # Test memory usage
        try:
            import psutil
            process = psutil.Process()
            performance_metrics["resource_usage"] = {
                "memory_mb": process.memory_info().rss / 1024 / 1024,
                "cpu_percent": process.cpu_percent()
            }
        except:
            performance_metrics["resource_usage"] = {"error": "Could not measure resources"}

        return {
            "passed": performance_metrics.get("concurrent_requests", {}).get("success_rate", 0) > 0.5,
            "metrics": performance_metrics
        }

    async def _test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and recovery."""
        self.logger.info("🛡️ Testing error handling...")

        error_tests = {}

        # Test invalid API calls
        try:
            response = requests.get("https://api.binance.com/api/v3/invalid_endpoint", timeout=5)
            error_tests["invalid_api_call"] = {
                "passed": response.status_code != 200,  # Should fail gracefully
                "status_code": response.status_code
            }
        except Exception as e:
            error_tests["invalid_api_call"] = {"passed": True, "handled_exception": str(e)}

        # Test invalid model inference
        try:
            response = await ai_service.generate_response("invalid_model", "test", {})
            error_tests["invalid_model"] = {
                "passed": response is None or "error" in str(response).lower(),
                "response": str(response)[:100] if response else "None"
            }
        except Exception as e:
            error_tests["invalid_model"] = {"passed": True, "handled_exception": str(e)}

        return {
            "passed": all(test.get("passed", False) for test in error_tests.values()),
            "error_tests": error_tests
        }

    async def _test_end_to_end_workflow(self) -> Dict[str, Any]:
        """Test complete end-to-end workflow."""
        self.logger.info("🎯 Testing end-to-end workflow...")

        workflow_steps = {}

        try:
            # Step 1: Get market data
            if "real_market_watcher" in self.agent_manager.agents:
                market_data = await self.agent_manager.agents["real_market_watcher"].get_current_market_data()
                workflow_steps["market_data"] = {"passed": bool(market_data), "data_points": len(market_data.get("prices", {}))}

            # Step 2: Analyze strategies
            if "real_strategy_researcher" in self.agent_manager.agents:
                strategy_data = await self.agent_manager.agents["real_strategy_researcher"].get_strategy_performance()
                workflow_steps["strategy_analysis"] = {"passed": bool(strategy_data), "strategies": strategy_data.get("total_strategies", 0)}

            # Step 3: Assess risk
            if "real_risk_officer" in self.agent_manager.agents:
                risk_data = await self.agent_manager.agents["real_risk_officer"].get_risk_summary()
                workflow_steps["risk_assessment"] = {"passed": bool(risk_data), "portfolios": len(risk_data.get("portfolios", {}))}

            # Step 4: Generate system status
            system_status = await self.agent_manager.get_system_status()
            workflow_steps["system_status"] = {"passed": bool(system_status), "active_agents": system_status.get("system_health", {}).get("active_agents", 0)}

            all_steps_passed = all(step.get("passed", False) for step in workflow_steps.values())

            return {
                "passed": all_steps_passed,
                "workflow_steps": workflow_steps,
                "completed_steps": sum(1 for step in workflow_steps.values() if step.get("passed", False))
            }

        except Exception as e:
            return {"passed": False, "error": str(e)}

    async def _test_cross_agent_communication(self) -> Dict[str, Any]:
        """Test communication between agents."""
        self.logger.info("🔄 Testing cross-agent communication...")

        # For now, test that agents can share data
        communication_tests = {}

        try:
            # Test data sharing between agents
            system_status = await self.agent_manager.get_system_status()

            communication_tests["system_coordination"] = {
                "passed": bool(system_status),
                "agents_reporting": len(system_status.get("agents", {}))
            }

            return {
                "passed": communication_tests["system_coordination"]["passed"],
                "tests": communication_tests
            }

        except Exception as e:
            return {"passed": False, "error": str(e)}

    async def _test_resource_usage(self) -> Dict[str, Any]:
        """Test resource usage monitoring."""
        self.logger.info("💾 Testing resource usage...")

        try:
            import psutil

            # Get current resource usage
            memory_usage = psutil.virtual_memory()
            cpu_usage = psutil.cpu_percent(interval=1)

            resource_metrics = {
                "memory_total_gb": memory_usage.total / 1024 / 1024 / 1024,
                "memory_used_gb": memory_usage.used / 1024 / 1024 / 1024,
                "memory_percent": memory_usage.percent,
                "cpu_percent": cpu_usage
            }

            # Check if usage is within reasonable limits
            resource_ok = (
                resource_metrics["memory_percent"] < 90 and  # Less than 90% memory
                resource_metrics["cpu_percent"] < 80         # Less than 80% CPU
            )

            return {
                "passed": resource_ok,
                "metrics": resource_metrics,
                "warnings": [] if resource_ok else ["High resource usage detected"]
            }

        except Exception as e:
            return {"passed": False, "error": str(e)}

    async def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        end_time = datetime.utcnow()
        total_time = (end_time - self.start_time).total_seconds() if self.start_time else 0

        # Calculate overall statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("passed", False))
        success_rate = passed_tests / total_tests if total_tests > 0 else 0

        # Generate summary
        report = {
            "test_summary": {
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": end_time.isoformat(),
                "total_duration": total_time,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": success_rate,
                "overall_status": "PASSED" if success_rate >= 0.7 else "FAILED"
            },
            "detailed_results": self.test_results,
            "recommendations": self._generate_recommendations()
        }

        # Log final summary
        self.logger.info("\n" + "=" * 80)
        self.logger.info("📊 FINAL TEST REPORT")
        self.logger.info("=" * 80)
        self.logger.info(f"🕐 Total Duration: {total_time:.1f} seconds")
        self.logger.info(f"📋 Tests Run: {total_tests}")
        self.logger.info(f"✅ Passed: {passed_tests}")
        self.logger.info(f"❌ Failed: {total_tests - passed_tests}")
        self.logger.info(f"📈 Success Rate: {success_rate:.1%}")
        self.logger.info(f"🎯 Overall Status: {report['test_summary']['overall_status']}")
        self.logger.info("=" * 80)

        return report

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        for test_name, result in self.test_results.items():
            if not result.get("passed", False):
                if "Ollama" in test_name:
                    recommendations.append("Ensure Ollama is installed and running with required models")
                elif "API" in test_name:
                    recommendations.append("Check internet connectivity and API endpoints")
                elif "Agent" in test_name:
                    recommendations.append("Review agent initialization and dependencies")
                elif "Performance" in test_name:
                    recommendations.append("Consider optimizing system resources or reducing load")

        if not recommendations:
            recommendations.append("All tests passed! System is ready for production use.")

        return recommendations

    async def cleanup(self):
        """Cleanup test resources."""
        try:
            await self.agent_manager.stop_all_agents()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")


async def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="Run NORYON V2 Real Integration Tests")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    tester = RealIntegrationTester()

    try:
        # Run tests
        results = await tester.run_comprehensive_tests()

        # Save results to file
        with open(f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
            json.dump(results, f, indent=2, default=str)

        # Exit with appropriate code
        exit_code = 0 if results["test_summary"]["overall_status"] == "PASSED" else 1
        sys.exit(exit_code)

    except KeyboardInterrupt:
        tester.logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        tester.logger.error(f"Test runner crashed: {e}")
        traceback.print_exc()
        sys.exit(1)
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    asyncio.run(main())