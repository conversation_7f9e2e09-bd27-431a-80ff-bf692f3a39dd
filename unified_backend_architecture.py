"""
Unified Backend Architecture for Noryon V2
Comprehensive backend system integrating all optimizations and improvements
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import gc
import psutil
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class SystemStatus(Enum):
    INITIALIZING = "initializing"
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CRITICAL = "critical"
    MAINTENANCE = "maintenance"
    SHUTDOWN = "shutdown"

class ComponentType(Enum):
    MESSAGE_QUEUE = "message_queue"
    DATABASE_POOL = "database_pool"
    CACHE_SYSTEM = "cache_system"
    MEMORY_MANAGER = "memory_manager"
    PERFORMANCE_MONITOR = "performance_monitor"

@dataclass
class SystemConfig:
    """Unified system configuration"""
    # Message Queue Settings
    redis_url: str = "redis://localhost:6379"
    max_concurrent_workers: int = 20
    message_batch_size: int = 50
    
    # Database Settings
    postgres_url: str = "postgresql+asyncpg://user:pass@localhost/noryon"
    clickhouse_url: str = "http://localhost:8123"
    max_db_connections: int = 100
    query_cache_ttl: int = 300
    
    # Memory Management
    max_memory_usage_pct: float = 80.0
    gc_threshold_mb: int = 500
    object_tracking_enabled: bool = True
    
    # Caching
    l1_cache_size: int = 10000
    l2_cache_ttl: int = 3600
    cache_compression_enabled: bool = True
    
    # Performance Monitoring
    metrics_collection_interval: int = 30
    health_check_interval: int = 60
    alert_thresholds: Dict[str, float] = None
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "memory_usage": 85.0,
                "cpu_usage": 80.0,
                "error_rate": 0.05,
                "response_time": 5.0
            }

@dataclass
class SystemMetrics:
    """Comprehensive system metrics"""
    # System Health
    status: SystemStatus
    uptime_seconds: float
    last_health_check: datetime
    
    # Performance Metrics
    cpu_usage_pct: float
    memory_usage_pct: float
    memory_used_mb: float
    total_memory_mb: float
    
    # Component Metrics
    active_components: int
    failed_components: int
    component_health: Dict[str, str]
    
    # Throughput Metrics
    messages_processed: int
    database_queries: int
    cache_hits: int
    cache_misses: int
    
    # Error Metrics
    total_errors: int
    error_rate: float
    critical_errors: int

class BackendComponent:
    """Base class for backend components"""
    
    def __init__(self, name: str, component_type: ComponentType):
        self.name = name
        self.component_type = component_type
        self.status = SystemStatus.INITIALIZING
        self.metrics = {}
        self.config = {}
        self.last_health_check = None
        
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the component"""
        self.config = config
        try:
            await self._do_initialize()
            self.status = SystemStatus.HEALTHY
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize {self.name}: {e}")
            self.status = SystemStatus.CRITICAL
            return False
    
    async def _do_initialize(self):
        """Override in subclasses"""
        pass
    
    async def health_check(self) -> bool:
        """Perform health check"""
        try:
            result = await self._do_health_check()
            self.last_health_check = datetime.now(timezone.utc)
            if result:
                if self.status == SystemStatus.CRITICAL:
                    self.status = SystemStatus.HEALTHY
            else:
                self.status = SystemStatus.DEGRADED
            return result
        except Exception as e:
            logger.error(f"❌ Health check failed for {self.name}: {e}")
            self.status = SystemStatus.CRITICAL
            return False
    
    async def _do_health_check(self) -> bool:
        """Override in subclasses"""
        return True
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get component metrics"""
        return {
            "name": self.name,
            "type": self.component_type.value,
            "status": self.status.value,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "metrics": self.metrics
        }
    
    async def shutdown(self):
        """Shutdown component"""
        self.status = SystemStatus.SHUTDOWN
        await self._do_shutdown()
    
    async def _do_shutdown(self):
        """Override in subclasses"""
        pass

class MockMessageQueue(BackendComponent):
    """Mock message queue component"""
    
    def __init__(self):
        super().__init__("MessageQueue", ComponentType.MESSAGE_QUEUE)
        self.messages_processed = 0
        self.queue_size = 0
    
    async def _do_initialize(self):
        logger.info("🚀 Initializing Mock Message Queue")
        self.metrics = {
            "messages_processed": 0,
            "queue_size": 0,
            "workers_active": 5,
            "throughput": 0.0
        }
    
    async def _do_health_check(self) -> bool:
        # Simulate health check
        return self.queue_size < 10000  # Healthy if queue not too large
    
    async def enqueue_message(self, message: Dict[str, Any]):
        """Enqueue a message"""
        self.queue_size += 1
        self.metrics["queue_size"] = self.queue_size
    
    async def process_messages(self, count: int = 10):
        """Process messages"""
        processed = min(count, self.queue_size)
        self.queue_size -= processed
        self.messages_processed += processed
        self.metrics["messages_processed"] = self.messages_processed
        self.metrics["queue_size"] = self.queue_size

class MockDatabasePool(BackendComponent):
    """Mock database pool component"""
    
    def __init__(self):
        super().__init__("DatabasePool", ComponentType.DATABASE_POOL)
        self.queries_executed = 0
        self.active_connections = 0
    
    async def _do_initialize(self):
        logger.info("🚀 Initializing Mock Database Pool")
        self.active_connections = 10
        self.metrics = {
            "queries_executed": 0,
            "active_connections": 10,
            "max_connections": 100,
            "avg_query_time": 0.05,
            "connection_errors": 0
        }
    
    async def _do_health_check(self) -> bool:
        # Simulate health check
        return self.active_connections > 0
    
    async def execute_query(self, query: str) -> Dict[str, Any]:
        """Execute a database query"""
        # Simulate query execution
        await asyncio.sleep(0.01)  # Simulate query time
        self.queries_executed += 1
        self.metrics["queries_executed"] = self.queries_executed
        return {"status": "success", "rows": 10}

class MockCacheSystem(BackendComponent):
    """Mock cache system component"""
    
    def __init__(self):
        super().__init__("CacheSystem", ComponentType.CACHE_SYSTEM)
        self.cache = {}
        self.hits = 0
        self.misses = 0
    
    async def _do_initialize(self):
        logger.info("🚀 Initializing Mock Cache System")
        self.metrics = {
            "cache_hits": 0,
            "cache_misses": 0,
            "hit_rate": 0.0,
            "cache_size": 0,
            "memory_usage_mb": 0
        }
    
    async def _do_health_check(self) -> bool:
        # Cache is healthy if hit rate is reasonable
        total_requests = self.hits + self.misses
        if total_requests > 0:
            hit_rate = self.hits / total_requests
            return hit_rate > 0.5
        return True
    
    async def get(self, key: str) -> Any:
        """Get value from cache"""
        if key in self.cache:
            self.hits += 1
            return self.cache[key]
        else:
            self.misses += 1
            return None
    
    async def set(self, key: str, value: Any):
        """Set value in cache"""
        self.cache[key] = value
        self._update_metrics()
    
    def _update_metrics(self):
        """Update cache metrics"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        
        self.metrics.update({
            "cache_hits": self.hits,
            "cache_misses": self.misses,
            "hit_rate": hit_rate,
            "cache_size": len(self.cache)
        })

class MockMemoryManager(BackendComponent):
    """Mock memory management component"""
    
    def __init__(self):
        super().__init__("MemoryManager", ComponentType.MEMORY_MANAGER)
        self.gc_collections = 0
        self.objects_tracked = 0
    
    async def _do_initialize(self):
        logger.info("🚀 Initializing Mock Memory Manager")
        memory_info = psutil.virtual_memory()
        self.metrics = {
            "memory_usage_pct": memory_info.percent,
            "memory_used_mb": memory_info.used / (1024 * 1024),
            "gc_collections": 0,
            "objects_tracked": 0,
            "optimization_actions": 0
        }
    
    async def _do_health_check(self) -> bool:
        # Memory is healthy if usage is below threshold
        memory_info = psutil.virtual_memory()
        return memory_info.percent < 90.0
    
    async def force_gc(self) -> int:
        """Force garbage collection"""
        collected = gc.collect()
        self.gc_collections += 1
        self.metrics["gc_collections"] = self.gc_collections
        return collected
    
    async def optimize_memory(self):
        """Perform memory optimization"""
        await self.force_gc()
        memory_info = psutil.virtual_memory()
        self.metrics.update({
            "memory_usage_pct": memory_info.percent,
            "memory_used_mb": memory_info.used / (1024 * 1024)
        })

class PerformanceMonitor(BackendComponent):
    """Performance monitoring component"""
    
    def __init__(self):
        super().__init__("PerformanceMonitor", ComponentType.PERFORMANCE_MONITOR)
        self.alert_count = 0
        self.metrics_history = []
    
    async def _do_initialize(self):
        logger.info("🚀 Initializing Performance Monitor")
        self.metrics = {
            "alerts_generated": 0,
            "metrics_collected": 0,
            "avg_response_time": 0.0,
            "error_rate": 0.0
        }
    
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system-wide metrics"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_info = psutil.virtual_memory()
        
        metrics = {
            "timestamp": datetime.now(timezone.utc),
            "cpu_usage_pct": cpu_percent,
            "memory_usage_pct": memory_info.percent,
            "memory_used_mb": memory_info.used / (1024 * 1024),
            "memory_available_mb": memory_info.available / (1024 * 1024)
        }
        
        self.metrics_history.append(metrics)
        
        # Keep only last 1000 metrics
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        self.metrics["metrics_collected"] = len(self.metrics_history)
        
        return metrics

class UnifiedBackendArchitecture:
    """Main unified backend architecture system"""
    
    def __init__(self, config: SystemConfig = None):
        self.config = config or SystemConfig()
        self.status = SystemStatus.INITIALIZING
        self.start_time = None
        
        # Components
        self.components: Dict[str, BackendComponent] = {}
        
        # System metrics
        self.system_metrics = SystemMetrics(
            status=SystemStatus.INITIALIZING,
            uptime_seconds=0,
            last_health_check=datetime.now(timezone.utc),
            cpu_usage_pct=0,
            memory_usage_pct=0,
            memory_used_mb=0,
            total_memory_mb=0,
            active_components=0,
            failed_components=0,
            component_health={},
            messages_processed=0,
            database_queries=0,
            cache_hits=0,
            cache_misses=0,
            total_errors=0,
            error_rate=0.0,
            critical_errors=0
        )
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.health_check_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info("[INIT] Unified Backend Architecture initialized")
    
    async def initialize(self):
        """Initialize the entire backend system"""
        try:
            logger.info("🚀 Initializing Unified Backend Architecture")
            self.start_time = datetime.now(timezone.utc)
            
            # Initialize components
            await self._initialize_components()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.running = True
            self.status = SystemStatus.HEALTHY
            
            logger.info("✅ Unified Backend Architecture initialized successfully")
            
            # Initial system report
            await self._log_system_status()
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize backend architecture: {e}")
            self.status = SystemStatus.CRITICAL
            raise
    
    async def _initialize_components(self):
        """Initialize all backend components"""
        # Create components
        components_to_create = [
            MockMessageQueue(),
            MockDatabasePool(),
            MockCacheSystem(),
            MockMemoryManager(),
            PerformanceMonitor()
        ]
        
        # Initialize each component
        initialization_results = []
        for component in components_to_create:
            try:
                config = self._get_component_config(component.component_type)
                success = await component.initialize(config)
                self.components[component.name] = component
                initialization_results.append((component.name, success))
                
                if success:
                    logger.info(f"✅ {component.name} initialized successfully")
                else:
                    logger.error(f"❌ {component.name} initialization failed")
                    
            except Exception as e:
                logger.error(f"❌ Failed to initialize {component.name}: {e}")
                initialization_results.append((component.name, False))
        
        # Update system metrics
        successful_components = sum(1 for _, success in initialization_results if success)
        failed_components = len(initialization_results) - successful_components
        
        self.system_metrics.active_components = successful_components
        self.system_metrics.failed_components = failed_components
        
        logger.info(f"📊 Component initialization: {successful_components} successful, {failed_components} failed")
    
    def _get_component_config(self, component_type: ComponentType) -> Dict[str, Any]:
        """Get configuration for a specific component type"""
        if component_type == ComponentType.MESSAGE_QUEUE:
            return {
                "redis_url": self.config.redis_url,
                "max_workers": self.config.max_concurrent_workers,
                "batch_size": self.config.message_batch_size
            }
        elif component_type == ComponentType.DATABASE_POOL:
            return {
                "postgres_url": self.config.postgres_url,
                "clickhouse_url": self.config.clickhouse_url,
                "max_connections": self.config.max_db_connections,
                "cache_ttl": self.config.query_cache_ttl
            }
        elif component_type == ComponentType.CACHE_SYSTEM:
            return {
                "l1_size": self.config.l1_cache_size,
                "l2_ttl": self.config.l2_cache_ttl,
                "compression": self.config.cache_compression_enabled
            }
        elif component_type == ComponentType.MEMORY_MANAGER:
            return {
                "max_usage_pct": self.config.max_memory_usage_pct,
                "gc_threshold": self.config.gc_threshold_mb,
                "object_tracking": self.config.object_tracking_enabled
            }
        elif component_type == ComponentType.PERFORMANCE_MONITOR:
            return {
                "collection_interval": self.config.metrics_collection_interval,
                "alert_thresholds": self.config.alert_thresholds
            }
        else:
            return {}
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks"""
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("🔄 Background tasks started")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await self._update_system_metrics()
                await asyncio.sleep(self.config.metrics_collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Monitoring loop error: {e}")
                await asyncio.sleep(60)
    
    async def _health_check_loop(self):
        """Background health check loop"""
        while self.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.config.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Health check loop error: {e}")
                await asyncio.sleep(60)
    
    async def _update_system_metrics(self):
        """Update system-wide metrics"""
        try:
            # Get system info
            cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()
            
            # Update basic metrics
            self.system_metrics.cpu_usage_pct = cpu_percent
            self.system_metrics.memory_usage_pct = memory_info.percent
            self.system_metrics.memory_used_mb = memory_info.used / (1024 * 1024)
            self.system_metrics.total_memory_mb = memory_info.total / (1024 * 1024)
            self.system_metrics.uptime_seconds = (datetime.now(timezone.utc) - self.start_time).total_seconds()
            
            # Collect component metrics
            component_health = {}
            active_components = 0
            failed_components = 0
            
            for name, component in self.components.items():
                component_health[name] = component.status.value
                if component.status == SystemStatus.HEALTHY:
                    active_components += 1
                elif component.status in [SystemStatus.CRITICAL, SystemStatus.SHUTDOWN]:
                    failed_components += 1
            
            self.system_metrics.component_health = component_health
            self.system_metrics.active_components = active_components
            self.system_metrics.failed_components = failed_components
            
            # Aggregate performance metrics
            if "MessageQueue" in self.components:
                mq_metrics = self.components["MessageQueue"].metrics
                self.system_metrics.messages_processed = mq_metrics.get("messages_processed", 0)
            
            if "DatabasePool" in self.components:
                db_metrics = self.components["DatabasePool"].metrics
                self.system_metrics.database_queries = db_metrics.get("queries_executed", 0)
            
            if "CacheSystem" in self.components:
                cache_metrics = self.components["CacheSystem"].metrics
                self.system_metrics.cache_hits = cache_metrics.get("cache_hits", 0)
                self.system_metrics.cache_misses = cache_metrics.get("cache_misses", 0)
            
            # Determine overall system status
            if failed_components > len(self.components) // 2:
                self.system_metrics.status = SystemStatus.CRITICAL
            elif failed_components > 0:
                self.system_metrics.status = SystemStatus.DEGRADED
            else:
                self.system_metrics.status = SystemStatus.HEALTHY
            
            self.system_metrics.last_health_check = datetime.now(timezone.utc)
            
        except Exception as e:
            logger.error(f"❌ Failed to update system metrics: {e}")
    
    async def _perform_health_checks(self):
        """Perform health checks on all components"""
        health_check_tasks = []
        
        for component in self.components.values():
            task = asyncio.create_task(component.health_check())
            health_check_tasks.append((component.name, task))
        
        # Wait for all health checks
        for name, task in health_check_tasks:
            try:
                result = await task
                if not result:
                    logger.warning(f"⚠️ Health check failed for {name}")
            except Exception as e:
                logger.error(f"❌ Health check error for {name}: {e}")
    
    async def _log_system_status(self):
        """Log current system status"""
        metrics = self.system_metrics
        
        logger.info("=" * 80)
        logger.info("🎯 UNIFIED BACKEND ARCHITECTURE STATUS")
        logger.info("=" * 80)
        logger.info(f"📊 Overall Status: {metrics.status.value.upper()}")
        logger.info(f"⏱️  Uptime: {metrics.uptime_seconds:.0f}s")
        logger.info(f"💻 CPU Usage: {metrics.cpu_usage_pct:.1f}%")
        logger.info(f"🧠 Memory Usage: {metrics.memory_usage_pct:.1f}% ({metrics.memory_used_mb:.0f}MB)")
        logger.info(f"🔧 Active Components: {metrics.active_components}/{len(self.components)}")
        logger.info(f"📨 Messages Processed: {metrics.messages_processed}")
        logger.info(f"💾 Database Queries: {metrics.database_queries}")
        logger.info(f"🎯 Cache Hit Rate: {metrics.cache_hits / max(metrics.cache_hits + metrics.cache_misses, 1):.1%}")
        logger.info("=" * 80)
        
        # Component status
        for name, status in metrics.component_health.items():
            status_icon = "✅" if status == "healthy" else "⚠️" if status == "degraded" else "❌"
            logger.info(f"{status_icon} {name}: {status.upper()}")
        
        logger.info("=" * 80)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        # Update metrics before returning
        await self._update_system_metrics()
        
        # Collect component metrics
        component_metrics = {}
        for name, component in self.components.items():
            component_metrics[name] = await component.get_metrics()
        
        return {
            "system_metrics": asdict(self.system_metrics),
            "component_metrics": component_metrics,
            "configuration": asdict(self.config),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        status = await self.get_system_status()
        
        # Calculate performance indicators
        uptime_hours = self.system_metrics.uptime_seconds / 3600
        
        performance_indicators = {
            "throughput": {
                "messages_per_hour": self.system_metrics.messages_processed / max(uptime_hours, 1),
                "queries_per_hour": self.system_metrics.database_queries / max(uptime_hours, 1)
            },
            "efficiency": {
                "cache_hit_rate": self.system_metrics.cache_hits / max(self.system_metrics.cache_hits + self.system_metrics.cache_misses, 1),
                "component_availability": self.system_metrics.active_components / len(self.components),
                "memory_efficiency": 100 - self.system_metrics.memory_usage_pct
            },
            "reliability": {
                "uptime_hours": uptime_hours,
                "error_rate": self.system_metrics.error_rate,
                "system_stability": "high" if self.system_metrics.status == SystemStatus.HEALTHY else "medium" if self.system_metrics.status == SystemStatus.DEGRADED else "low"
            }
        }
        
        return {
            "system_status": status,
            "performance_indicators": performance_indicators,
            "recommendations": self._generate_performance_recommendations(),
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
    
    def _generate_performance_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        # Memory recommendations
        if self.system_metrics.memory_usage_pct > 80:
            recommendations.append("High memory usage detected - consider memory optimization")
        
        # CPU recommendations
        if self.system_metrics.cpu_usage_pct > 80:
            recommendations.append("High CPU usage detected - consider load balancing")
        
        # Component health recommendations
        if self.system_metrics.failed_components > 0:
            recommendations.append(f"{self.system_metrics.failed_components} components are unhealthy - investigate and repair")
        
        # Cache recommendations
        cache_hit_rate = self.system_metrics.cache_hits / max(self.system_metrics.cache_hits + self.system_metrics.cache_misses, 1)
        if cache_hit_rate < 0.8:
            recommendations.append(f"Cache hit rate is low ({cache_hit_rate:.1%}) - optimize caching strategy")
        
        # Throughput recommendations
        uptime_hours = max(self.system_metrics.uptime_seconds / 3600, 1)
        msg_per_hour = self.system_metrics.messages_processed / uptime_hours
        if msg_per_hour < 1000:  # Arbitrary threshold
            recommendations.append("Message processing throughput is low - consider optimization")
        
        if not recommendations:
            recommendations.append("System performance is optimal - no immediate actions needed")
        
        return recommendations
    
    @asynccontextmanager
    async def get_component(self, component_name: str):
        """Get a component with context management"""
        if component_name not in self.components:
            raise ValueError(f"Component {component_name} not found")
        
        component = self.components[component_name]
        if component.status != SystemStatus.HEALTHY:
            logger.warning(f"⚠️ Using potentially unhealthy component: {component_name}")
        
        try:
            yield component
        finally:
            # Could add cleanup logic here
            pass
    
    async def shutdown(self):
        """Shutdown the entire backend system"""
        try:
            logger.info("🛑 Shutting down Unified Backend Architecture")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            if self.health_check_task:
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # Shutdown components
            shutdown_tasks = []
            for component in self.components.values():
                task = asyncio.create_task(component.shutdown())
                shutdown_tasks.append(task)
            
            await asyncio.gather(*shutdown_tasks, return_exceptions=True)
            
            self.status = SystemStatus.SHUTDOWN
            logger.info("✅ Unified Backend Architecture shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

# Factory function
async def create_unified_backend(config: SystemConfig = None) -> UnifiedBackendArchitecture:
    """Factory function to create and initialize unified backend"""
    backend = UnifiedBackendArchitecture(config)
    await backend.initialize()
    return backend

# Example usage and testing
async def main():
    """Example usage of the unified backend architecture"""
    logger.info("🚀 Starting Unified Backend Architecture Demo")
    
    # Custom configuration
    config = SystemConfig(
        max_concurrent_workers=15,
        max_memory_usage_pct=75.0,
        metrics_collection_interval=10
    )
    
    # Create and initialize backend
    backend = await create_unified_backend(config)
    
    # Simulate some work
    logger.info("🔄 Simulating backend operations...")
    
    # Use message queue
    async with backend.get_component("MessageQueue") as mq:
        for i in range(5):
            await mq.enqueue_message({"id": i, "data": f"test_message_{i}"})
        await mq.process_messages(3)
    
    # Use database
    async with backend.get_component("DatabasePool") as db:
        for i in range(3):
            await db.execute_query(f"SELECT * FROM test_table_{i}")
    
    # Use cache
    async with backend.get_component("CacheSystem") as cache:
        await cache.set("test_key", {"value": "test_data"})
        result = await cache.get("test_key")
        logger.info(f"Cache result: {result}")
    
    # Wait a bit for metrics to update
    await asyncio.sleep(5)
    
    # Get system status
    status = await backend.get_system_status()
    logger.info(f"System Status: {status['system_metrics']['status']}")
    
    # Generate performance report
    report = await backend.get_performance_report()
    logger.info("Performance Report Generated")
    
    # Log final status
    await backend._log_system_status()
    
    # Shutdown
    await backend.shutdown()
    
    return report

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main()) 