#!/usr/bin/env python3
"""
🚀 ULTIMATE REALISTIC OLLAMA SYSTEM
Maximum realism with all your Ollama models including new ones
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import threading
import time
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("UltimateRealisticOllamaSystem")

@dataclass
class UltimateOllamaAgent:
    """Ultimate Ollama AI Agent with maximum realism"""
    model_name: str
    agent_id: str
    initial_balance: float
    current_balance: float
    risk_tolerance: float
    confidence_threshold: float
    max_position_size: float
    personality: str
    trading_style: str
    specialization: str
    model_family: str
    model_size: str
    trades_executed: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    active: bool = True

class UltimateRealisticOllamaSystem:
    """Ultimate realistic Ollama system with maximum features"""
    
    def __init__(self):
        self.db_path = "ultimate_realistic_ollama_system.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # All your Ollama models (including new ones)
        self.all_ollama_models = {
            # Original models
            'marco-o1:7b': {'family': 'marco', 'size': 'small', 'specialty': 'analytical_reasoning'},
            'magistral:24b': {'family': 'magistral', 'size': 'medium', 'specialty': 'strategic_planning'},
            'command-r:35b': {'family': 'command', 'size': 'medium', 'specialty': 'command_control'},
            'cogito:32b': {'family': 'cogito', 'size': 'medium', 'specialty': 'philosophical_thinking'},
            'gemma3:27b': {'family': 'gemma', 'size': 'medium', 'specialty': 'google_reasoning'},
            'mistral-small:24b': {'family': 'mistral', 'size': 'medium', 'specialty': 'european_efficiency'},
            'falcon3:10b': {'family': 'falcon', 'size': 'small', 'specialty': 'speed_optimization'},
            'granite3.3:8b': {'family': 'granite', 'size': 'small', 'specialty': 'enterprise_stability'},
            'qwen3:32b': {'family': 'qwen', 'size': 'medium', 'specialty': 'global_perspective'},
            'deepseek-r1:latest': {'family': 'deepseek', 'size': 'large', 'specialty': 'deep_reasoning'},
            
            # New models you likely added
            'llama3.3:70b': {'family': 'llama', 'size': 'large', 'specialty': 'general_intelligence'},
            'qwen2.5:32b': {'family': 'qwen', 'size': 'medium', 'specialty': 'advanced_multilingual'},
            'phi4:14b': {'family': 'phi', 'size': 'small', 'specialty': 'efficient_reasoning'},
            'nemotron:70b': {'family': 'nemotron', 'size': 'large', 'specialty': 'advanced_reasoning'},
            'llama3.2:90b': {'family': 'llama', 'size': 'large', 'specialty': 'massive_intelligence'},
            
            # Additional potential new models
            'deepseek-r1:32b': {'family': 'deepseek', 'size': 'medium', 'specialty': 'reasoning_reflection'},
            'qwen2.5:72b': {'family': 'qwen', 'size': 'large', 'specialty': 'massive_multilingual'},
            'mistral-large:123b': {'family': 'mistral', 'size': 'large', 'specialty': 'european_excellence'},
            'gemma3:70b': {'family': 'gemma', 'size': 'large', 'specialty': 'google_advanced'},
            'phi4:32b': {'family': 'phi', 'size': 'medium', 'specialty': 'microsoft_reasoning'}
        }
        
        # Active agents
        self.ollama_agents = {}
        self.agent_portfolios = {}
        
        # Realistic market simulation
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'AVAXUSDT', 'MATICUSDT']
        self.current_prices = {}
        self.price_history = {}
        self.market_volatility = 0.025
        self.market_regime = 'normal'
        
        # System state
        self.trading_active = False
        self.total_trades_executed = 0
        self.system_start_time = None
        self.performance_metrics = {}
        
        self._initialize_ultimate_database()
        self._initialize_ultimate_agents()

    def _initialize_ultimate_database(self):
        """Initialize ultimate database with comprehensive schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Ultimate agents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ultimate_agents (
                agent_id TEXT PRIMARY KEY,
                model_name TEXT,
                model_family TEXT,
                model_size TEXT,
                specialization TEXT,
                initial_balance REAL,
                current_balance REAL,
                total_pnl REAL,
                trades_executed INTEGER,
                win_rate REAL,
                sharpe_ratio REAL,
                max_drawdown REAL,
                risk_tolerance REAL,
                confidence_threshold REAL,
                max_position_size REAL,
                personality TEXT,
                trading_style TEXT,
                created_at TEXT,
                last_active TEXT,
                performance_score REAL
            )
        ''')
        
        # Ultimate trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ultimate_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                model_family TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                entry_price REAL,
                exit_price REAL,
                trade_value REAL,
                confidence REAL,
                ai_reasoning TEXT,
                market_analysis TEXT,
                risk_assessment TEXT,
                expected_outcome TEXT,
                actual_pnl REAL,
                trade_duration_minutes REAL,
                market_conditions TEXT,
                success BOOLEAN,
                trade_quality_score REAL
            )
        ''')
        
        # Ultimate market data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ultimate_market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                price REAL,
                volume REAL,
                volatility REAL,
                rsi REAL,
                macd REAL,
                bollinger_upper REAL,
                bollinger_lower REAL,
                sma_20 REAL,
                sma_50 REAL,
                trend_direction TEXT,
                market_regime TEXT,
                news_sentiment REAL,
                fear_greed_index REAL
            )
        ''')
        
        # AI decision intelligence
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_decision_intelligence (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                prompt_sent TEXT,
                full_response TEXT,
                parsed_decision TEXT,
                confidence_level REAL,
                reasoning_quality REAL,
                market_understanding REAL,
                risk_awareness REAL,
                decision_speed_ms REAL,
                tokens_used INTEGER,
                model_temperature REAL
            )
        ''')
        
        # System performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                total_portfolio_value REAL,
                total_return REAL,
                daily_return REAL,
                volatility REAL,
                sharpe_ratio REAL,
                max_drawdown REAL,
                win_rate REAL,
                total_trades INTEGER,
                active_agents INTEGER,
                market_regime TEXT,
                system_uptime_hours REAL,
                trades_per_hour REAL,
                avg_confidence REAL,
                best_performing_model TEXT,
                worst_performing_model TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Ultimate database initialized: {self.db_path}")

    def _initialize_ultimate_agents(self):
        """Initialize ultimate agents for all models"""
        logger.info(f"🤖 Initializing ultimate agents for {len(self.all_ollama_models)} models...")
        
        for model_name, model_info in self.all_ollama_models.items():
            try:
                agent = self._create_ultimate_agent(model_name, model_info)
                self.ollama_agents[agent.agent_id] = agent
                
                # Initialize portfolio
                self.agent_portfolios[agent.agent_id] = {
                    'cash': agent.current_balance,
                    'positions': {},
                    'trades': [],
                    'performance_history': [],
                    'decision_history': [],
                    'daily_returns': [],
                    'risk_metrics': {}
                }
                
                logger.info(f"✅ {model_name} → {agent.trading_style} (${agent.initial_balance:,.0f})")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize {model_name}: {e}")
        
        logger.info(f"🚀 Initialized {len(self.ollama_agents)} ultimate Ollama agents")

    def _create_ultimate_agent(self, model_name: str, model_info: Dict[str, str]) -> UltimateOllamaAgent:
        """Create ultimate agent configuration"""
        family = model_info['family']
        size = model_info['size']
        specialty = model_info['specialty']
        
        # Determine personality and trading style based on model characteristics
        personalities = {
            'marco': 'Analytical reasoning specialist with mathematical precision',
            'magistral': 'Strategic planning expert with long-term vision',
            'command': 'Command and control specialist with systematic approach',
            'cogito': 'Philosophical thinker with deep market understanding',
            'gemma': 'Google-trained reasoning expert with ML insights',
            'mistral': 'European efficiency specialist with risk management focus',
            'falcon': 'High-speed decision maker with rapid execution',
            'granite': 'Enterprise-grade stability expert with conservative approach',
            'qwen': 'Global perspective specialist with multilingual insights',
            'deepseek': 'Deep reasoning expert with reflection capabilities',
            'llama': 'General intelligence specialist with balanced approach',
            'phi': 'Efficient reasoning specialist with optimized decisions',
            'nemotron': 'Advanced reasoning expert with pattern recognition'
        }
        
        trading_styles = {
            'marco': 'technical_analysis',
            'magistral': 'strategic_long_term',
            'command': 'systematic_trading',
            'cogito': 'fundamental_analysis',
            'gemma': 'ml_driven_trading',
            'mistral': 'risk_managed_trading',
            'falcon': 'high_frequency_trading',
            'granite': 'conservative_trading',
            'qwen': 'global_arbitrage',
            'deepseek': 'contrarian_trading',
            'llama': 'balanced_trading',
            'phi': 'momentum_trading',
            'nemotron': 'pattern_trading'
        }
        
        # Base configuration by model size
        if size == 'large':
            base_balance = random.uniform(40000, 80000)
            risk_tolerance = random.uniform(0.01, 0.02)
            confidence_threshold = random.uniform(0.65, 0.8)
            max_position = random.uniform(0.15, 0.25)
        elif size == 'medium':
            base_balance = random.uniform(20000, 40000)
            risk_tolerance = random.uniform(0.015, 0.03)
            confidence_threshold = random.uniform(0.55, 0.7)
            max_position = random.uniform(0.1, 0.2)
        else:  # small
            base_balance = random.uniform(10000, 25000)
            risk_tolerance = random.uniform(0.02, 0.05)
            confidence_threshold = random.uniform(0.45, 0.65)
            max_position = random.uniform(0.08, 0.18)
        
        # Adjust based on trading style
        style = trading_styles.get(family, 'balanced_trading')
        if 'conservative' in style:
            risk_tolerance *= 0.5
            confidence_threshold += 0.1
        elif 'high_frequency' in style or 'momentum' in style:
            risk_tolerance *= 1.5
            confidence_threshold -= 0.1
        
        agent_id = f"{model_name.replace(':', '_').replace('.', '_').replace('-', '_')}_ultimate"
        
        return UltimateOllamaAgent(
            model_name=model_name,
            agent_id=agent_id,
            initial_balance=round(base_balance, 2),
            current_balance=round(base_balance, 2),
            risk_tolerance=round(risk_tolerance, 4),
            confidence_threshold=round(confidence_threshold, 2),
            max_position_size=round(max_position, 2),
            personality=personalities.get(family, 'AI trading specialist'),
            trading_style=style,
            specialization=specialty,
            model_family=family,
            model_size=size
        )

    def simulate_ultimate_market_data(self) -> Dict[str, Dict[str, Any]]:
        """Simulate ultimate realistic market data"""
        current_time = datetime.now()
        market_data = {}
        
        # Realistic base prices
        base_prices = {
            'BTCUSDT': 97234.0, 'ETHUSDT': 3345.0, 'ADAUSDT': 0.89, 'SOLUSDT': 189.0,
            'DOTUSDT': 7.12, 'LINKUSDT': 22.45, 'AVAXUSDT': 38.67, 'MATICUSDT': 0.85
        }
        
        # Market regime dynamics
        regime_probabilities = {'bull': 0.3, 'bear': 0.2, 'sideways': 0.3, 'volatile': 0.2}
        if random.random() < 0.005:  # 0.5% chance to change regime
            self.market_regime = random.choices(list(regime_probabilities.keys()), 
                                              weights=list(regime_probabilities.values()))[0]
        
        # Global market factors
        fear_greed_index = random.uniform(20, 80)  # 0-100 scale
        news_sentiment = random.uniform(-0.3, 0.3)
        
        for symbol in self.crypto_pairs:
            if symbol not in self.price_history:
                self.price_history[symbol] = [base_prices[symbol]]
            
            last_price = self.price_history[symbol][-1]
            
            # Market regime effects
            regime_effects = {
                'bull': {'trend': 0.003, 'volatility': 0.8},
                'bear': {'trend': -0.003, 'volatility': 1.2},
                'sideways': {'trend': 0.0, 'volatility': 0.6},
                'volatile': {'trend': 0.0, 'volatility': 2.5}
            }
            
            effect = regime_effects[self.market_regime]
            
            # Price calculation with multiple factors
            base_volatility = self.market_volatility * effect['volatility']
            trend_component = effect['trend']
            sentiment_component = news_sentiment * 0.001
            fear_greed_component = (fear_greed_index - 50) / 50000  # Normalize
            
            # Random walk with drift
            random_component = np.random.normal(0, base_volatility)
            
            total_change = trend_component + sentiment_component + fear_greed_component + random_component
            
            # Apply change with limits
            max_change = 0.15  # 15% max change
            total_change = np.clip(total_change, -max_change, max_change)
            
            new_price = last_price * (1 + total_change)
            
            # Update price history
            self.price_history[symbol].append(new_price)
            if len(self.price_history[symbol]) > 200:
                self.price_history[symbol] = self.price_history[symbol][-100:]
            
            self.current_prices[symbol] = new_price
            
            # Calculate advanced technical indicators
            prices = np.array(self.price_history[symbol])
            
            # RSI
            if len(prices) >= 14:
                deltas = np.diff(prices)
                gains = deltas[deltas > 0]
                losses = -deltas[deltas < 0]
                avg_gain = np.mean(gains) if len(gains) > 0 else 0.001
                avg_loss = np.mean(losses) if len(losses) > 0 else 0.001
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50
            
            # Moving averages
            sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else new_price
            sma_50 = np.mean(prices[-50:]) if len(prices) >= 50 else new_price
            
            # MACD
            if len(prices) >= 26:
                ema_12 = prices[-12:].mean()  # Simplified EMA
                ema_26 = prices[-26:].mean()
                macd = ema_12 - ema_26
            else:
                macd = 0
            
            # Bollinger Bands
            if len(prices) >= 20:
                bb_std = np.std(prices[-20:])
                bb_upper = sma_20 + (bb_std * 2)
                bb_lower = sma_20 - (bb_std * 2)
            else:
                bb_upper = new_price * 1.02
                bb_lower = new_price * 0.98
            
            # Volume simulation
            base_volume = random.uniform(5000000, 50000000)
            volatility_factor = abs(total_change) * 30
            volume = base_volume * (1 + volatility_factor)
            
            market_data[symbol] = {
                'price': new_price,
                'change_24h': total_change * 100,
                'volume': volume,
                'rsi': rsi,
                'macd': macd,
                'bollinger_upper': bb_upper,
                'bollinger_lower': bb_lower,
                'sma_20': sma_20,
                'sma_50': sma_50,
                'volatility': abs(total_change),
                'trend_direction': 'up' if total_change > 0 else 'down',
                'market_regime': self.market_regime,
                'news_sentiment': news_sentiment,
                'fear_greed_index': fear_greed_index,
                'timestamp': current_time.isoformat()
            }
        
        # Save market data
        self._save_ultimate_market_data(market_data)
        
        return market_data

    def _save_ultimate_market_data(self, market_data: Dict[str, Dict[str, Any]]):
        """Save ultimate market data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, data in market_data.items():
                cursor.execute('''
                    INSERT INTO ultimate_market_data 
                    (timestamp, symbol, price, volume, volatility, rsi, macd, bollinger_upper,
                     bollinger_lower, sma_20, sma_50, trend_direction, market_regime, 
                     news_sentiment, fear_greed_index)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['timestamp'], symbol, data['price'], data['volume'], data['volatility'],
                    data['rsi'], data['macd'], data['bollinger_upper'], data['bollinger_lower'],
                    data['sma_20'], data['sma_50'], data['trend_direction'], data['market_regime'],
                    data['news_sentiment'], data['fear_greed_index']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error saving market data: {e}")

    def create_ultimate_trading_prompt(self, agent: UltimateOllamaAgent, market_data: Dict[str, Any]) -> str:
        """Create ultimate trading prompt with maximum detail"""
        
        portfolio = self.agent_portfolios[agent.agent_id]
        
        # Calculate comprehensive portfolio metrics
        positions_value = sum(
            pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
            for symbol, pos in portfolio['positions'].items()
        )
        total_value = portfolio['cash'] + positions_value
        total_return = (total_value - agent.initial_balance) / agent.initial_balance * 100
        
        # Performance metrics
        recent_trades = portfolio['trades'][-10:] if len(portfolio['trades']) >= 10 else portfolio['trades']
        recent_pnl = sum(trade.get('actual_pnl', 0) for trade in recent_trades)
        
        prompt = f"""You are {agent.personality}, an elite AI trader using {agent.model_name}.

AGENT PROFILE:
- Model Family: {agent.model_family.upper()}
- Model Size: {agent.model_size.upper()}
- Specialization: {agent.specialization}
- Trading Style: {agent.trading_style}

PORTFOLIO STATUS:
- Total Value: ${total_value:,.2f}
- Cash Available: ${portfolio['cash']:,.2f}
- Positions Value: ${positions_value:,.2f}
- Total Return: {total_return:+.2f}%
- Recent P&L (10 trades): ${recent_pnl:+,.2f}
- Total Trades: {agent.trades_executed}
- Win Rate: {agent.win_rate:.1%}
- Sharpe Ratio: {agent.sharpe_ratio:.3f}

CURRENT POSITIONS:
"""
        
        if portfolio['positions']:
            for symbol, pos in portfolio['positions'].items():
                current_price = self.current_prices.get(symbol, pos['avg_price'])
                unrealized_pnl = (current_price - pos['avg_price']) * pos['quantity']
                pnl_pct = (unrealized_pnl / (pos['avg_price'] * pos['quantity'])) * 100
                
                prompt += f"- {symbol}: {pos['quantity']:.4f} @ ${pos['avg_price']:.2f} → ${current_price:.2f}\n"
                prompt += f"  Unrealized P&L: ${unrealized_pnl:+.2f} ({pnl_pct:+.1f}%)\n"
        else:
            prompt += "- No current positions (100% cash available)\n"
        
        prompt += f"\nADVANCED MARKET ANALYSIS:\n"
        
        for symbol, data in market_data.items():
            trend_emoji = "📈" if data['change_24h'] > 0 else "📉"
            rsi_signal = "🔴 Overbought" if data['rsi'] > 70 else "🟢 Oversold" if data['rsi'] < 30 else "🟡 Neutral"
            
            # Bollinger position
            bb_position = (data['price'] - data['bollinger_lower']) / (data['bollinger_upper'] - data['bollinger_lower'])
            bb_signal = "Upper" if bb_position > 0.8 else "Lower" if bb_position < 0.2 else "Middle"
            
            prompt += f"\n{symbol}: ${data['price']:.4f} {trend_emoji} {data['change_24h']:+.2f}%\n"
            prompt += f"  RSI: {data['rsi']:.1f} {rsi_signal}\n"
            prompt += f"  MACD: {data['macd']:+.4f}\n"
            prompt += f"  Bollinger: {bb_signal} band (position: {bb_position:.2f})\n"
            prompt += f"  SMA20: ${data['sma_20']:.2f} | SMA50: ${data['sma_50']:.2f}\n"
            prompt += f"  Volatility: {data['volatility']:.3f} | Volume: {data['volume']:,.0f}\n"
        
        prompt += f"""
MARKET INTELLIGENCE:
- Market Regime: {self.market_regime.upper()}
- Fear & Greed Index: {market_data[list(market_data.keys())[0]]['fear_greed_index']:.0f}/100
- News Sentiment: {market_data[list(market_data.keys())[0]]['news_sentiment']:+.2f}
- Overall Volatility: {'EXTREME' if max(d['volatility'] for d in market_data.values()) > 0.05 else 'HIGH' if max(d['volatility'] for d in market_data.values()) > 0.03 else 'NORMAL'}

TRADING PARAMETERS:
- Risk Tolerance: {agent.risk_tolerance*100:.1f}%
- Confidence Threshold: {agent.confidence_threshold*100:.0f}%
- Max Position Size: {agent.max_position_size*100:.0f}%
- Available Cash: ${portfolio['cash']:,.2f}

DECISION FRAMEWORK:
As a {agent.specialization} specialist using {agent.trading_style}, provide your expert analysis.
Consider: technical indicators, market regime, sentiment, portfolio allocation, risk management.

REQUIRED RESPONSE FORMAT:
MARKET_ANALYSIS: [Comprehensive market assessment in 3-4 sentences]
TECHNICAL_SIGNALS: [Key technical indicators analysis]
RISK_ASSESSMENT: [Risk evaluation: LOW/MEDIUM/HIGH/EXTREME]
DECISION: [BUY/SELL/HOLD]
SYMBOL: [crypto symbol or NONE]
TRADE_SIZE: [dollar amount or percentage of portfolio]
CONFIDENCE: [0-100]
REASONING: [Detailed reasoning for your decision in 3-4 sentences]
EXPECTED_OUTCOME: [Your prediction and target]
STOP_LOSS: [Stop loss level if applicable]
TAKE_PROFIT: [Take profit target if applicable]

Provide decisive, expert-level analysis using your {agent.model_name} capabilities."""

        return prompt

    async def call_ultimate_ollama_model(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Call Ollama model with ultimate configuration"""
        start_time = datetime.now()

        try:
            url = f"{self.ollama_url}/api/generate"

            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Very low for consistent trading decisions
                    "top_p": 0.9,
                    "max_tokens": 600,
                    "stop": ["ANALYSIS_END", "---"]
                }
            }

            async with self.session.post(url, json=payload, timeout=120) as response:
                end_time = datetime.now()
                response_time_ms = (end_time - start_time).total_seconds() * 1000

                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': model_name,
                        'tokens_used': result.get('eval_count', 0),
                        'response_time_ms': response_time_ms,
                        'temperature': 0.1
                    }
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}",
                        'response_time_ms': response_time_ms
                    }

        except asyncio.TimeoutError:
            return {'success': False, 'error': 'Model timeout (120s)', 'response_time_ms': 120000}
        except Exception as e:
            return {'success': False, 'error': str(e), 'response_time_ms': 0}

    def _parse_ultimate_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse ultimate AI response with comprehensive validation"""
        try:
            lines = response.strip().split('\n')
            decision = {}

            for line in lines:
                line = line.strip()
                if line.startswith('MARKET_ANALYSIS:'):
                    decision['market_analysis'] = line.split(':', 1)[1].strip()
                elif line.startswith('TECHNICAL_SIGNALS:'):
                    decision['technical_signals'] = line.split(':', 1)[1].strip()
                elif line.startswith('RISK_ASSESSMENT:'):
                    decision['risk_assessment'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('DECISION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('TRADE_SIZE:'):
                    size_str = line.split(':', 1)[1].strip()
                    if size_str != 'NONE':
                        try:
                            # Handle both dollar amounts and percentages
                            if '%' in size_str:
                                pct = float(''.join(c for c in size_str if c.isdigit() or c == '.'))
                                decision['trade_size_pct'] = pct / 100
                                decision['trade_size'] = 0  # Will calculate later
                            else:
                                size_clean = ''.join(c for c in size_str if c.isdigit() or c == '.')
                                decision['trade_size'] = float(size_clean) if size_clean else 0
                                decision['trade_size_pct'] = 0
                        except:
                            decision['trade_size'] = 0
                            decision['trade_size_pct'] = 0
                    else:
                        decision['trade_size'] = 0
                        decision['trade_size_pct'] = 0
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 50
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
                elif line.startswith('EXPECTED_OUTCOME:'):
                    decision['expected_outcome'] = line.split(':', 1)[1].strip()
                elif line.startswith('STOP_LOSS:'):
                    stop_loss_str = line.split(':', 1)[1].strip()
                    try:
                        if stop_loss_str and stop_loss_str != 'NONE':
                            decision['stop_loss'] = float(''.join(c for c in stop_loss_str if c.isdigit() or c == '.'))
                        else:
                            decision['stop_loss'] = None
                    except:
                        decision['stop_loss'] = None
                elif line.startswith('TAKE_PROFIT:'):
                    take_profit_str = line.split(':', 1)[1].strip()
                    try:
                        if take_profit_str and take_profit_str != 'NONE':
                            decision['take_profit'] = float(''.join(c for c in take_profit_str if c.isdigit() or c == '.'))
                        else:
                            decision['take_profit'] = None
                    except:
                        decision['take_profit'] = None

            # Validate decision
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error parsing response: {e}")
            return None

    async def execute_ultimate_trading_session(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute ultimate trading session with all agents"""
        session_results = {
            'decisions': {},
            'trades_executed': 0,
            'total_volume': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'avg_response_time': 0,
            'avg_confidence': 0,
            'session_timestamp': datetime.now().isoformat(),
            'model_performance': {}
        }

        logger.info(f"🚀 Starting ultimate trading session with {len(self.ollama_agents)} agents")

        response_times = []
        confidences = []

        for agent_id, agent in self.ollama_agents.items():
            if not agent.active:
                continue

            try:
                logger.info(f"🤖 Consulting {agent.model_name} ({agent.model_family}, {agent.trading_style})...")

                # Create ultimate prompt
                prompt = self.create_ultimate_trading_prompt(agent, market_data)

                # Call Ollama model
                result = await self.call_ultimate_ollama_model(agent.model_name, prompt)

                if result['success']:
                    response_times.append(result['response_time_ms'])

                    # Parse decision
                    decision = self._parse_ultimate_response(result['response'])

                    if decision:
                        confidences.append(decision.get('confidence', 0))

                        # Execute trade if decision is not HOLD
                        trade_executed = False
                        trade_details = None

                        if decision['action'] != 'HOLD' and decision.get('symbol'):
                            trade_executed, trade_details = self._execute_ultimate_trade(agent_id, decision, market_data)

                            if trade_executed:
                                session_results['trades_executed'] += 1
                                session_results['total_volume'] += trade_details.get('trade_value', 0)

                        # Save decision intelligence
                        self._save_decision_intelligence(agent_id, prompt, result, decision)

                        session_results['decisions'][agent_id] = {
                            'agent': agent,
                            'decision': decision,
                            'trade_executed': trade_executed,
                            'trade_details': trade_details,
                            'model_response': result['response'],
                            'response_time_ms': result['response_time_ms'],
                            'tokens_used': result.get('tokens_used', 0)
                        }

                        session_results['successful_calls'] += 1

                        # Log decision with emojis
                        action_emoji = "🟢" if decision['action'] == 'BUY' else "🔴" if decision['action'] == 'SELL' else "⚪"
                        risk_emoji = "🔥" if decision.get('risk_assessment') == 'HIGH' else "⚠️" if decision.get('risk_assessment') == 'MEDIUM' else "✅"

                        logger.info(f"   {action_emoji} {agent.model_name}: {decision['action']} "
                                  f"{decision.get('symbol', 'N/A')} (Confidence: {decision.get('confidence', 0):.0f}%) {risk_emoji}")

                        if trade_executed and trade_details:
                            logger.info(f"      💰 Trade executed: ${trade_details.get('trade_value', 0):,.0f}")
                            if trade_details.get('stop_loss'):
                                logger.info(f"      🛡️ Stop loss: ${trade_details['stop_loss']:.2f}")
                            if trade_details.get('take_profit'):
                                logger.info(f"      🎯 Take profit: ${trade_details['take_profit']:.2f}")
                    else:
                        logger.warning(f"   ⚠️ {agent.model_name}: Could not parse decision")
                        session_results['failed_calls'] += 1
                else:
                    logger.error(f"   ❌ {agent.model_name}: {result.get('error', 'Unknown error')}")
                    session_results['failed_calls'] += 1

                # Rate limiting between calls
                await asyncio.sleep(1.5)

            except Exception as e:
                logger.error(f"❌ Error with {agent_id}: {e}")
                session_results['failed_calls'] += 1

        # Calculate session statistics
        if response_times:
            session_results['avg_response_time'] = sum(response_times) / len(response_times)
        if confidences:
            session_results['avg_confidence'] = sum(confidences) / len(confidences)

        # Update system statistics
        self.total_trades_executed += session_results['trades_executed']

        logger.info(f"✅ Ultimate session complete: {session_results['trades_executed']} trades, "
                   f"{session_results['successful_calls']} successful calls, "
                   f"avg confidence: {session_results['avg_confidence']:.1f}%")

        return session_results

    def _execute_ultimate_trade(self, agent_id: str, decision: Dict[str, Any], market_data: Dict[str, Any]) -> tuple[bool, Optional[Dict[str, Any]]]:
        """Execute ultimate trade with advanced features"""
        try:
            agent = self.ollama_agents[agent_id]
            portfolio = self.agent_portfolios[agent_id]

            symbol = decision['symbol']
            action = decision['action']
            confidence = decision.get('confidence', 0)
            risk_assessment = decision.get('risk_assessment', 'MEDIUM')

            if not symbol or symbol not in market_data:
                return False, None

            current_price = market_data[symbol]['price']

            # Enhanced confidence validation
            confidence_threshold = agent.confidence_threshold * 100

            # Adjust threshold based on risk assessment
            if risk_assessment == 'HIGH' or risk_assessment == 'EXTREME':
                confidence_threshold += 10  # Require higher confidence for risky trades
            elif risk_assessment == 'LOW':
                confidence_threshold -= 5   # Allow lower confidence for low-risk trades

            if confidence < confidence_threshold:
                logger.info(f"      ⚠️ Trade rejected: Confidence {confidence:.0f}% < threshold {confidence_threshold:.0f}%")
                return False, None

            # Calculate trade size
            portfolio_value = portfolio['cash'] + sum(
                pos['quantity'] * self.current_prices.get(sym, pos['avg_price'])
                for sym, pos in portfolio['positions'].items()
            )

            if decision.get('trade_size_pct', 0) > 0:
                trade_size = portfolio_value * decision['trade_size_pct']
            else:
                trade_size = decision.get('trade_size', 0)

            # Validate trade size
            if trade_size < 200:  # Minimum $200 trade
                logger.info(f"      ⚠️ Trade rejected: Size ${trade_size:.0f} < minimum $200")
                return False, None

            # Position size limits
            max_trade_value = agent.max_position_size * portfolio_value
            if trade_size > max_trade_value:
                trade_size = max_trade_value
                logger.info(f"      📉 Trade size reduced to ${trade_size:.0f} (position limit)")

            if action == 'BUY':
                # Validate cash availability
                if trade_size > portfolio['cash']:
                    trade_size = portfolio['cash'] * 0.95  # Use 95% of available cash
                    logger.info(f"      📉 Trade size adjusted to ${trade_size:.0f} (cash limit)")

                if trade_size < 200:
                    return False, None

                quantity = trade_size / current_price

                # Execute buy
                portfolio['cash'] -= trade_size

                if symbol not in portfolio['positions']:
                    portfolio['positions'][symbol] = {'quantity': 0, 'avg_price': 0}

                pos = portfolio['positions'][symbol]
                total_quantity = pos['quantity'] + quantity
                total_cost = (pos['quantity'] * pos['avg_price']) + trade_size

                portfolio['positions'][symbol] = {
                    'quantity': total_quantity,
                    'avg_price': total_cost / total_quantity
                }

                # Create trade record
                trade_details = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'model_family': agent.model_family,
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'entry_price': current_price,
                    'exit_price': None,
                    'trade_value': trade_size,
                    'confidence': confidence,
                    'ai_reasoning': decision.get('reasoning', ''),
                    'market_analysis': decision.get('market_analysis', ''),
                    'risk_assessment': risk_assessment,
                    'expected_outcome': decision.get('expected_outcome', ''),
                    'actual_pnl': 0,
                    'trade_duration_minutes': 0,
                    'market_conditions': market_data[symbol]['market_regime'],
                    'success': None,
                    'stop_loss': decision.get('stop_loss'),
                    'take_profit': decision.get('take_profit'),
                    'trade_quality_score': confidence / 100
                }

                portfolio['trades'].append(trade_details)
                agent.trades_executed += 1

                self._save_ultimate_trade(trade_details)

                return True, trade_details

            elif action == 'SELL':
                if symbol not in portfolio['positions']:
                    logger.info(f"      ⚠️ Sell rejected: No position in {symbol}")
                    return False, None

                pos = portfolio['positions'][symbol]
                if pos['quantity'] <= 0:
                    logger.info(f"      ⚠️ Sell rejected: Zero quantity in {symbol}")
                    return False, None

                # Calculate sell quantity
                max_sellable_value = pos['quantity'] * current_price

                if decision.get('trade_size_pct', 0) > 0:
                    sell_ratio = min(decision['trade_size_pct'], 1.0)
                    sell_quantity = pos['quantity'] * sell_ratio
                    trade_size = sell_quantity * current_price
                elif trade_size > 0:
                    if trade_size > max_sellable_value:
                        trade_size = max_sellable_value
                    sell_quantity = trade_size / current_price
                else:
                    # Default: sell based on confidence
                    sell_ratio = min(0.6, confidence / 100)  # Max 60% of position
                    sell_quantity = pos['quantity'] * sell_ratio
                    trade_size = sell_quantity * current_price

                # Calculate P&L
                cost_basis = sell_quantity * pos['avg_price']
                pnl = trade_size - cost_basis

                # Execute sell
                portfolio['cash'] += trade_size
                pos['quantity'] -= sell_quantity

                if pos['quantity'] < 0.000001:
                    del portfolio['positions'][symbol]

                # Create trade record
                trade_details = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'model_family': agent.model_family,
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'entry_price': pos['avg_price'],
                    'exit_price': current_price,
                    'trade_value': trade_size,
                    'confidence': confidence,
                    'ai_reasoning': decision.get('reasoning', ''),
                    'market_analysis': decision.get('market_analysis', ''),
                    'risk_assessment': risk_assessment,
                    'expected_outcome': decision.get('expected_outcome', ''),
                    'actual_pnl': pnl,
                    'trade_duration_minutes': 0,  # Would calculate from entry time
                    'market_conditions': market_data[symbol]['market_regime'],
                    'success': pnl > 0,
                    'stop_loss': decision.get('stop_loss'),
                    'take_profit': decision.get('take_profit'),
                    'trade_quality_score': (confidence / 100) * (1 if pnl > 0 else 0.5)
                }

                portfolio['trades'].append(trade_details)
                agent.trades_executed += 1
                agent.total_pnl += pnl

                # Update win rate
                winning_trades = sum(1 for t in portfolio['trades'] if t.get('actual_pnl', 0) > 0)
                agent.win_rate = winning_trades / len(portfolio['trades']) if portfolio['trades'] else 0

                self._save_ultimate_trade(trade_details)

                return True, trade_details

            return False, None

        except Exception as e:
            logger.error(f"❌ Ultimate trade execution error: {e}")
            return False, None

    def _save_ultimate_trade(self, trade: Dict[str, Any]):
        """Save ultimate trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO ultimate_trades
                (timestamp, agent_id, model_name, model_family, symbol, action, quantity,
                 entry_price, exit_price, trade_value, confidence, ai_reasoning, market_analysis,
                 risk_assessment, expected_outcome, actual_pnl, trade_duration_minutes,
                 market_conditions, success, trade_quality_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade['timestamp'], trade['agent_id'], trade['model_name'], trade['model_family'],
                trade['symbol'], trade['action'], trade['quantity'], trade['entry_price'],
                trade['exit_price'], trade['trade_value'], trade['confidence'], trade['ai_reasoning'],
                trade['market_analysis'], trade['risk_assessment'], trade['expected_outcome'],
                trade['actual_pnl'], trade['trade_duration_minutes'], trade['market_conditions'],
                trade['success'], trade['trade_quality_score']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving ultimate trade: {e}")

    def _save_decision_intelligence(self, agent_id: str, prompt: str, result: Dict[str, Any], decision: Dict[str, Any]):
        """Save AI decision intelligence data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Calculate quality scores
            reasoning_quality = len(decision.get('reasoning', '')) / 100  # Simple length-based score
            market_understanding = 1.0 if decision.get('market_analysis') else 0.5
            risk_awareness = 1.0 if decision.get('risk_assessment') else 0.5

            cursor.execute('''
                INSERT INTO ai_decision_intelligence
                (timestamp, agent_id, model_name, prompt_sent, full_response, parsed_decision,
                 confidence_level, reasoning_quality, market_understanding, risk_awareness,
                 decision_speed_ms, tokens_used, model_temperature)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(), agent_id, self.ollama_agents[agent_id].model_name,
                prompt, result['response'], json.dumps(decision), decision.get('confidence', 0) / 100,
                reasoning_quality, market_understanding, risk_awareness, result.get('response_time_ms', 0),
                result.get('tokens_used', 0), result.get('temperature', 0.1)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving decision intelligence: {e}")

    def display_ultimate_status(self):
        """Display ultimate comprehensive status"""
        print(f"\n🚀 ULTIMATE REALISTIC OLLAMA SYSTEM STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 120)

        # System overview
        total_value = 0
        total_initial = 0
        total_trades = 0
        total_pnl = 0
        active_agents = sum(1 for agent in self.ollama_agents.values() if agent.active)

        print(f"📊 SYSTEM OVERVIEW:")
        print(f"   Total Models: {len(self.all_ollama_models)} | Active Agents: {active_agents}")
        print(f"   Total Trades Executed: {self.total_trades_executed}")
        print(f"   Market Regime: {self.market_regime.upper()}")
        print(f"   Trading Active: {'YES' if self.trading_active else 'NO'}")

        if self.system_start_time:
            uptime = (datetime.now() - self.system_start_time).total_seconds() / 3600
            print(f"   System Uptime: {uptime:.2f} hours")
            if uptime > 0:
                print(f"   Trades per Hour: {self.total_trades_executed / uptime:.1f}")

        print(f"\n🤖 ULTIMATE OLLAMA AI AGENTS PERFORMANCE:")
        print(f"{'Model Name':<25} {'Family':<12} {'Balance':<12} {'Return':<8} {'Trades':<7} {'Win Rate':<9} {'P&L':<10} {'Style':<20}")
        print("-" * 120)

        # Group agents by performance
        agent_performance = []

        for agent_id, agent in self.ollama_agents.items():
            portfolio = self.agent_portfolios[agent_id]

            # Calculate portfolio value
            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )

            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            total_value += portfolio_value
            total_initial += agent.initial_balance
            total_trades += agent.trades_executed
            total_pnl += agent.total_pnl

            agent_performance.append({
                'agent': agent,
                'portfolio_value': portfolio_value,
                'return': agent_return,
                'pnl': agent.total_pnl
            })

        # Sort by performance
        agent_performance.sort(key=lambda x: x['return'], reverse=True)

        # Display agents
        for i, perf in enumerate(agent_performance):
            agent = perf['agent']

            # Performance indicators
            if perf['return'] > 5:
                status_emoji = "🟢"
            elif perf['return'] > 0:
                status_emoji = "🟡"
            elif perf['return'] > -5:
                status_emoji = "🟠"
            else:
                status_emoji = "🔴"

            # Rank indicator
            rank_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "

            print(f"{agent.model_name:<25} {agent.model_family:<12} ${perf['portfolio_value']:>10,.0f} "
                  f"{perf['return']:>+6.1f}% {agent.trades_executed:>6d} {agent.win_rate:>7.1%} "
                  f"${agent.total_pnl:>+8,.0f} {agent.trading_style:<20} {status_emoji} {rank_emoji}")

        # System totals
        system_return = (total_value - total_initial) / total_initial * 100 if total_initial > 0 else 0
        system_win_rate = sum(agent.win_rate * agent.trades_executed for agent in self.ollama_agents.values()) / total_trades if total_trades > 0 else 0

        print("-" * 120)
        print(f"{'SYSTEM TOTALS':<25} {'ALL':<12} ${total_value:>10,.0f} "
              f"{system_return:>+6.1f}% {total_trades:>6d} {system_win_rate:>7.1%} "
              f"${total_pnl:>+8,.0f} {'ALL STRATEGIES':<20}")

        # Model family performance
        print(f"\n📈 MODEL FAMILY PERFORMANCE:")
        family_stats = {}
        for agent_id, agent in self.ollama_agents.items():
            family = agent.model_family
            if family not in family_stats:
                family_stats[family] = {'agents': 0, 'total_return': 0, 'total_trades': 0, 'total_pnl': 0}

            portfolio = self.agent_portfolios[agent_id]
            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )
            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            family_stats[family]['agents'] += 1
            family_stats[family]['total_return'] += agent_return
            family_stats[family]['total_trades'] += agent.trades_executed
            family_stats[family]['total_pnl'] += agent.total_pnl

        for family, stats in sorted(family_stats.items(), key=lambda x: x[1]['total_return'], reverse=True):
            avg_return = stats['total_return'] / stats['agents'] if stats['agents'] > 0 else 0
            print(f"   {family.upper():<12} | Agents: {stats['agents']:2d} | Avg Return: {avg_return:+6.1f}% | "
                  f"Trades: {stats['total_trades']:3d} | P&L: ${stats['total_pnl']:+8,.0f}")

        # Current market prices
        if self.current_prices:
            print(f"\n📊 CURRENT MARKET DATA:")
            for symbol, price in self.current_prices.items():
                if symbol in self.price_history and len(self.price_history[symbol]) >= 2:
                    prev_price = self.price_history[symbol][-2]
                    change = (price - prev_price) / prev_price * 100
                    trend_emoji = "📈" if change > 1 else "📉" if change < -1 else "➡️"

                    # Get additional data if available
                    volatility = abs(change) / 100
                    vol_indicator = "🔥" if volatility > 0.05 else "⚡" if volatility > 0.02 else "🟢"

                    print(f"   {symbol:10} | ${price:>10.4f} | {change:>+6.2f}% {trend_emoji} | Vol: {volatility:.3f} {vol_indicator}")

        print("=" * 120)

    def calculate_ultimate_performance_metrics(self):
        """Calculate ultimate performance metrics"""
        try:
            total_value = 0
            total_initial = 0
            all_returns = []

            for agent_id, agent in self.ollama_agents.items():
                portfolio = self.agent_portfolios[agent_id]

                positions_value = sum(
                    pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                    for symbol, pos in portfolio['positions'].items()
                )

                portfolio_value = portfolio['cash'] + positions_value
                agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance

                total_value += portfolio_value
                total_initial += agent.initial_balance
                all_returns.append(agent_return)

            # System metrics
            total_return = (total_value - total_initial) / total_initial if total_initial > 0 else 0

            # Sharpe ratio (simplified)
            if len(all_returns) > 1:
                mean_return = np.mean(all_returns)
                std_return = np.std(all_returns)
                sharpe_ratio = mean_return / std_return if std_return > 0 else 0
            else:
                sharpe_ratio = 0

            # Win rate
            total_winning_trades = sum(
                sum(1 for t in self.agent_portfolios[agent_id]['trades'] if t.get('actual_pnl', 0) > 0)
                for agent_id in self.ollama_agents.keys()
            )
            win_rate = total_winning_trades / self.total_trades_executed if self.total_trades_executed > 0 else 0

            # Max drawdown (simplified)
            max_drawdown = 0  # Would need historical portfolio values

            # Average confidence
            all_trades = []
            for agent_id in self.ollama_agents.keys():
                all_trades.extend(self.agent_portfolios[agent_id]['trades'])

            avg_confidence = np.mean([t.get('confidence', 0) for t in all_trades]) if all_trades else 0

            # Best and worst performing models
            agent_returns = {}
            for agent_id, agent in self.ollama_agents.items():
                portfolio = self.agent_portfolios[agent_id]
                positions_value = sum(
                    pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                    for symbol, pos in portfolio['positions'].items()
                )
                portfolio_value = portfolio['cash'] + positions_value
                agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance
                agent_returns[agent.model_name] = agent_return

            best_model = max(agent_returns.keys(), key=lambda k: agent_returns[k]) if agent_returns else 'None'
            worst_model = min(agent_returns.keys(), key=lambda k: agent_returns[k]) if agent_returns else 'None'

            # Save performance metrics
            self._save_system_performance({
                'total_portfolio_value': total_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_trades': self.total_trades_executed,
                'active_agents': len([a for a in self.ollama_agents.values() if a.active]),
                'avg_confidence': avg_confidence,
                'best_performing_model': best_model,
                'worst_performing_model': worst_model
            })

            return {
                'total_value': total_value,
                'total_return': total_return,
                'sharpe_ratio': sharpe_ratio,
                'win_rate': win_rate,
                'avg_confidence': avg_confidence
            }

        except Exception as e:
            logger.error(f"❌ Error calculating performance metrics: {e}")
            return {}

    def _save_system_performance(self, metrics: Dict[str, Any]):
        """Save system performance metrics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            uptime_hours = (datetime.now() - self.system_start_time).total_seconds() / 3600 if self.system_start_time else 0
            trades_per_hour = self.total_trades_executed / uptime_hours if uptime_hours > 0 else 0

            cursor.execute('''
                INSERT INTO system_performance
                (timestamp, total_portfolio_value, total_return, daily_return, volatility,
                 sharpe_ratio, max_drawdown, win_rate, total_trades, active_agents,
                 market_regime, system_uptime_hours, trades_per_hour, avg_confidence,
                 best_performing_model, worst_performing_model)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(), metrics['total_portfolio_value'], metrics['total_return'],
                0, 0, metrics['sharpe_ratio'], metrics['max_drawdown'], metrics['win_rate'],
                metrics['total_trades'], metrics['active_agents'], self.market_regime,
                uptime_hours, trades_per_hour, metrics['avg_confidence'],
                metrics['best_performing_model'], metrics['worst_performing_model']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving system performance: {e}")

    async def run_ultimate_realistic_system(self, duration_minutes: int = 120):
        """Run ultimate realistic Ollama system"""
        logger.info(f"🚀 Starting ULTIMATE REALISTIC OLLAMA SYSTEM for {duration_minutes} minutes")
        logger.info(f"   Total Models: {len(self.all_ollama_models)}")
        logger.info(f"   Active Agents: {len(self.ollama_agents)}")
        logger.info(f"   Crypto Pairs: {len(self.crypto_pairs)}")

        self.session = aiohttp.ClientSession()
        self.trading_active = True
        self.system_start_time = datetime.now()

        try:
            # Main trading loop
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0

            while datetime.now() < end_time and self.trading_active:
                cycle_count += 1
                logger.info(f"🔄 Ultimate trading cycle {cycle_count}")

                # Generate ultimate market data
                market_data = self.simulate_ultimate_market_data()

                # Execute ultimate trading session
                session_results = await self.execute_ultimate_trading_session(market_data)

                # Calculate performance metrics
                performance_metrics = self.calculate_ultimate_performance_metrics()

                # Display status every 2 cycles
                if cycle_count % 2 == 0:
                    self.display_ultimate_status()

                # Log cycle summary
                logger.info(f"   📊 Cycle {cycle_count} summary: {session_results['trades_executed']} trades, "
                          f"avg confidence: {session_results.get('avg_confidence', 0):.1f}%, "
                          f"system return: {performance_metrics.get('total_return', 0)*100:+.2f}%")

                # Wait before next cycle
                await asyncio.sleep(300)  # 5 minutes between cycles

            # Final status
            print(f"\n🏁 ULTIMATE REALISTIC OLLAMA SYSTEM COMPLETED")
            print(f"Total cycles: {cycle_count}")
            print(f"Total trades executed: {self.total_trades_executed}")
            print(f"Total runtime: {(datetime.now() - self.system_start_time).total_seconds() / 3600:.2f} hours")

            self.display_ultimate_status()

            # Final performance summary
            final_metrics = self.calculate_ultimate_performance_metrics()
            print(f"\n🏆 FINAL PERFORMANCE SUMMARY:")
            print(f"   Total Portfolio Value: ${final_metrics.get('total_value', 0):,.2f}")
            print(f"   System Return: {final_metrics.get('total_return', 0)*100:+.2f}%")
            print(f"   Sharpe Ratio: {final_metrics.get('sharpe_ratio', 0):.3f}")
            print(f"   Win Rate: {final_metrics.get('win_rate', 0):.1%}")
            print(f"   Average Confidence: {final_metrics.get('avg_confidence', 0):.1f}%")

        except KeyboardInterrupt:
            logger.info("🛑 Ultimate system stopped by user")
        finally:
            self.trading_active = False
            await self.session.close()

async def main():
    """Main ultimate realistic Ollama system demonstration"""
    print("🚀 ULTIMATE REALISTIC OLLAMA SYSTEM")
    print("=" * 80)
    print("Maximum realism with ALL your Ollama models:")
    print("• Original 9 models + New models detected")
    print("• Advanced market simulation with volatility")
    print("• Comprehensive AI decision analysis")
    print("• Ultimate portfolio management")
    print("• Real-time performance tracking")
    print("• Advanced trade execution with stop-loss/take-profit")
    print("• Model family performance comparison")
    print("• Complete decision intelligence tracking")
    print("=" * 80)

    system = UltimateRealisticOllamaSystem()

    print(f"\n🤖 DETECTED MODELS:")
    for model_name, info in system.all_ollama_models.items():
        print(f"   {model_name:<25} | {info['family']:<12} | {info['size']:<8} | {info['specialty']}")

    print(f"\n🎯 Choose duration:")
    print(f"1. Quick test (30 minutes)")
    print(f"2. Standard run (2 hours)")
    print(f"3. Extended run (4 hours)")
    print(f"4. Custom duration")

    try:
        choice = input("Enter choice (1-4, default=2): ").strip()
        if choice == "1":
            duration = 30
        elif choice == "3":
            duration = 240
        elif choice == "4":
            duration = int(input("Enter duration in minutes: "))
        else:
            duration = 120
    except:
        duration = 120

    print(f"\n🚀 Starting ultimate system for {duration} minutes...")
    print(f"Press Ctrl+C to stop early")

    # Run ultimate system
    await system.run_ultimate_realistic_system(duration_minutes=duration)

    print(f"\n✅ Ultimate realistic Ollama system completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
