#!/usr/bin/env python3
"""
🧪 TEST ULTIMATE SYSTEM
Quick test of the ultimate realistic Ollama system
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("TestUltimateSystem")

class TestUltimateSystem:
    """Test the ultimate system components"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        
        # Your complete model list (including new ones)
        self.all_models = {
            # Original models
            'marco-o1:7b': {'family': 'marco', 'size': 'small', 'specialty': 'analytical_reasoning'},
            'magistral:24b': {'family': 'magistral', 'size': 'medium', 'specialty': 'strategic_planning'},
            'command-r:35b': {'family': 'command', 'size': 'medium', 'specialty': 'command_control'},
            'cogito:32b': {'family': 'cogito', 'size': 'medium', 'specialty': 'philosophical_thinking'},
            'gemma3:27b': {'family': 'gemma', 'size': 'medium', 'specialty': 'google_reasoning'},
            'mistral-small:24b': {'family': 'mistral', 'size': 'medium', 'specialty': 'european_efficiency'},
            'falcon3:10b': {'family': 'falcon', 'size': 'small', 'specialty': 'speed_optimization'},
            'granite3.3:8b': {'family': 'granite', 'size': 'small', 'specialty': 'enterprise_stability'},
            'qwen3:32b': {'family': 'qwen', 'size': 'medium', 'specialty': 'global_perspective'},
            'deepseek-r1:latest': {'family': 'deepseek', 'size': 'large', 'specialty': 'deep_reasoning'},
            
            # New models you likely added
            'llama3.3:70b': {'family': 'llama', 'size': 'large', 'specialty': 'general_intelligence'},
            'qwen2.5:32b': {'family': 'qwen', 'size': 'medium', 'specialty': 'advanced_multilingual'},
            'phi4:14b': {'family': 'phi', 'size': 'small', 'specialty': 'efficient_reasoning'},
            'nemotron:70b': {'family': 'nemotron', 'size': 'large', 'specialty': 'advanced_reasoning'},
            'llama3.2:90b': {'family': 'llama', 'size': 'large', 'specialty': 'massive_intelligence'},
            
            # Additional potential models
            'deepseek-r1:32b': {'family': 'deepseek', 'size': 'medium', 'specialty': 'reasoning_reflection'},
            'qwen2.5:72b': {'family': 'qwen', 'size': 'large', 'specialty': 'massive_multilingual'},
            'mistral-large:123b': {'family': 'mistral', 'size': 'large', 'specialty': 'european_excellence'},
            'gemma3:70b': {'family': 'gemma', 'size': 'large', 'specialty': 'google_advanced'},
            'phi4:32b': {'family': 'phi', 'size': 'medium', 'specialty': 'microsoft_reasoning'}
        }

    async def test_model_availability(self, model_name: str) -> dict:
        """Test if a model is available"""
        try:
            url = f"{self.ollama_url}/api/generate"
            payload = {
                "model": model_name,
                "prompt": "Hello! Respond with just 'READY' if you can trade.",
                "stream": False,
                "options": {"max_tokens": 10, "temperature": 0.1}
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=30) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            'available': True,
                            'response': result.get('response', ''),
                            'model': model_name
                        }
                    else:
                        return {'available': False, 'error': f"HTTP {response.status}"}
        except Exception as e:
            return {'available': False, 'error': str(e)}

    async def test_trading_decision(self, model_name: str) -> dict:
        """Test trading decision from a model"""
        try:
            prompt = f"""You are an AI trader using {model_name}.

MARKET DATA:
- BTCUSDT: $97,234 (+0.5%)
- ETHUSDT: $3,345 (+1.2%)
- SOLUSDT: $189 (+2.1%)

Make a quick trading decision:
DECISION: [BUY/SELL/HOLD]
SYMBOL: [symbol or NONE]
CONFIDENCE: [0-100]
REASON: [brief reason]

Be decisive and specific."""

            url = f"{self.ollama_url}/api/generate"
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {"max_tokens": 200, "temperature": 0.2}
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=60) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {
                            'success': True,
                            'response': result.get('response', ''),
                            'model': model_name
                        }
                    else:
                        return {'success': False, 'error': f"HTTP {response.status}"}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def parse_decision(self, response: str) -> dict:
        """Parse trading decision"""
        decision = {}
        lines = response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('DECISION:'):
                decision['action'] = line.split(':', 1)[1].strip().upper()
            elif line.startswith('SYMBOL:'):
                symbol = line.split(':', 1)[1].strip().upper()
                decision['symbol'] = symbol if symbol != 'NONE' else None
            elif line.startswith('CONFIDENCE:'):
                try:
                    confidence = float(line.split(':', 1)[1].strip())
                    decision['confidence'] = min(100, max(0, confidence))
                except:
                    decision['confidence'] = 50
            elif line.startswith('REASON:'):
                decision['reason'] = line.split(':', 1)[1].strip()
        
        return decision

    async def run_comprehensive_test(self):
        """Run comprehensive test of all models"""
        print("🧪 TESTING ULTIMATE REALISTIC OLLAMA SYSTEM")
        print("=" * 80)
        
        print(f"📋 TESTING {len(self.all_models)} MODELS:")
        for model_name, info in self.all_models.items():
            print(f"   {model_name:<25} | {info['family']:<12} | {info['size']:<8} | {info['specialty']}")
        
        print(f"\n🔍 PHASE 1: AVAILABILITY TEST")
        print("-" * 50)
        
        available_models = []
        unavailable_models = []
        
        for model_name in self.all_models.keys():
            print(f"Testing {model_name}...", end=" ")
            result = await self.test_model_availability(model_name)
            
            if result['available']:
                available_models.append(model_name)
                print("✅ Available")
            else:
                unavailable_models.append((model_name, result.get('error', 'Unknown error')))
                print(f"❌ {result.get('error', 'Not available')}")
        
        print(f"\n📊 AVAILABILITY RESULTS:")
        print(f"   ✅ Available: {len(available_models)}")
        print(f"   ❌ Unavailable: {len(unavailable_models)}")
        
        if available_models:
            print(f"\n🤖 AVAILABLE MODELS:")
            for model in available_models:
                info = self.all_models[model]
                print(f"   ✅ {model:<25} | {info['family']:<12} | {info['specialty']}")
        
        if unavailable_models:
            print(f"\n❌ UNAVAILABLE MODELS:")
            for model, error in unavailable_models:
                print(f"   ❌ {model:<25} | {error}")
        
        # Test trading decisions with available models
        if available_models:
            print(f"\n🎯 PHASE 2: TRADING DECISION TEST")
            print("-" * 50)
            
            # Test first 5 available models
            test_models = available_models[:5]
            decisions = []
            
            for model_name in test_models:
                print(f"Getting trading decision from {model_name}...")
                result = await self.test_trading_decision(model_name)
                
                if result['success']:
                    decision = self.parse_decision(result['response'])
                    decisions.append({
                        'model': model_name,
                        'decision': decision,
                        'raw_response': result['response']
                    })
                    
                    action_emoji = "🟢" if decision.get('action') == 'BUY' else "🔴" if decision.get('action') == 'SELL' else "⚪"
                    print(f"   {action_emoji} {model_name}: {decision.get('action', 'UNKNOWN')} "
                          f"{decision.get('symbol', 'N/A')} (Confidence: {decision.get('confidence', 0):.0f}%)")
                else:
                    print(f"   ❌ {model_name}: {result.get('error', 'Failed')}")
            
            # Summary of decisions
            if decisions:
                print(f"\n📈 TRADING DECISIONS SUMMARY:")
                buy_decisions = [d for d in decisions if d['decision'].get('action') == 'BUY']
                sell_decisions = [d for d in decisions if d['decision'].get('action') == 'SELL']
                hold_decisions = [d for d in decisions if d['decision'].get('action') == 'HOLD']
                
                print(f"   🟢 BUY decisions: {len(buy_decisions)}")
                print(f"   🔴 SELL decisions: {len(sell_decisions)}")
                print(f"   ⚪ HOLD decisions: {len(hold_decisions)}")
                
                if decisions:
                    avg_confidence = sum(d['decision'].get('confidence', 0) for d in decisions) / len(decisions)
                    print(f"   📊 Average confidence: {avg_confidence:.1f}%")
                
                # Most popular symbol
                symbols = [d['decision'].get('symbol') for d in decisions if d['decision'].get('symbol')]
                if symbols:
                    from collections import Counter
                    symbol_counts = Counter(symbols)
                    most_popular = symbol_counts.most_common(1)[0]
                    print(f"   🎯 Most popular symbol: {most_popular[0]} ({most_popular[1]} votes)")
        
        print(f"\n🏁 TEST COMPLETED")
        print(f"   Total models tested: {len(self.all_models)}")
        print(f"   Available models: {len(available_models)}")
        print(f"   Success rate: {len(available_models)/len(self.all_models)*100:.1f}%")
        
        if len(available_models) >= 5:
            print(f"\n✅ SYSTEM READY FOR FULL DEPLOYMENT!")
            print(f"   Recommended: Run ultimate_realistic_ollama_system.py")
        elif len(available_models) >= 2:
            print(f"\n⚠️ PARTIAL SYSTEM READY")
            print(f"   {len(available_models)} models available for trading")
        else:
            print(f"\n❌ INSUFFICIENT MODELS")
            print(f"   Only {len(available_models)} models available")
            print(f"   Check Ollama installation and model availability")
        
        return {
            'available_models': available_models,
            'unavailable_models': unavailable_models,
            'decisions': decisions if 'decisions' in locals() else []
        }

async def main():
    """Main test function"""
    tester = TestUltimateSystem()
    results = await tester.run_comprehensive_test()
    
    print(f"\n💾 TEST RESULTS SUMMARY:")
    print(f"   Available models: {len(results['available_models'])}")
    print(f"   Trading decisions tested: {len(results['decisions'])}")
    
    if results['available_models']:
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Run: python ultimate_realistic_ollama_system.py")
        print(f"2. Or run: python enhanced_ollama_trading.py")
        print(f"3. Or run: python comprehensive_long_term_system.py")

if __name__ == "__main__":
    asyncio.run(main())
