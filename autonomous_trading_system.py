#!/usr/bin/env python3
"""
🤖 AUTONOMOUS AI TRADING SYSTEM
AI agents that trade independently without human control
"""

import asyncio
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import joblib
import numpy as np
import pandas as pd
from dataclasses import dataclass
import aiohttp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AutonomousTrading")

@dataclass
class TradingDecision:
    symbol: str
    action: str  # BUY, SELL, HOLD
    quantity: float
    confidence: float
    reasoning: str
    timestamp: datetime
    agent_id: str

@dataclass
class Portfolio:
    cash_balance: float
    positions: Dict[str, Dict[str, float]]  # {symbol: {quantity, avg_price}}
    total_value: float
    unrealized_pnl: float

class AutonomousAgent:
    """Independent AI trading agent"""
    
    def __init__(self, agent_id: str, initial_balance: float = 1000.0):
        self.agent_id = agent_id
        self.initial_balance = initial_balance
        self.portfolio = Portfolio(
            cash_balance=initial_balance,
            positions={},
            total_value=initial_balance,
            unrealized_pnl=0.0
        )
        self.decisions_history = []
        self.model = None
        self.scaler = None
        self.risk_tolerance = 0.02  # 2% max risk per trade
        self.max_position_size = 0.1  # 10% max per position
        
        # Load trained model if available
        try:
            self.model = joblib.load('best_model_random_forest.pkl')
            self.scaler = joblib.load('feature_scaler.pkl')
            logger.info(f"✅ {agent_id}: Loaded trained model")
        except:
            logger.warning(f"⚠️ {agent_id}: No trained model found")

    def analyze_market_data(self, market_data: Dict) -> Dict[str, float]:
        """Analyze current market conditions"""
        analysis = {}
        
        for symbol, data in market_data.items():
            # Basic technical analysis
            price = data['price']
            volume = data['volume']
            change_24h = data['change_24h']
            
            # Volatility assessment
            volatility = abs(change_24h) / 100
            
            # Volume strength
            volume_strength = min(volume / 1000000, 1.0)  # Normalize
            
            # Momentum
            momentum = change_24h / 100
            
            # Risk assessment
            risk_score = volatility * 0.6 + (1 - volume_strength) * 0.4
            
            analysis[symbol] = {
                'price': price,
                'volatility': volatility,
                'momentum': momentum,
                'volume_strength': volume_strength,
                'risk_score': risk_score,
                'attractiveness': momentum * volume_strength * (1 - risk_score)
            }
        
        return analysis

    def make_trading_decision(self, market_analysis: Dict) -> List[TradingDecision]:
        """Make autonomous trading decisions"""
        decisions = []
        
        for symbol, analysis in market_analysis.items():
            # Skip if too risky
            if analysis['risk_score'] > self.risk_tolerance * 10:
                continue
            
            # Decision logic based on analysis
            attractiveness = analysis['attractiveness']
            confidence = min(abs(attractiveness) * 5, 1.0)
            
            # Only act if confidence is high enough
            if confidence < 0.3:
                continue
            
            # Determine action
            if attractiveness > 0.02:  # Positive signal
                action = 'BUY'
                reasoning = f"Positive momentum ({analysis['momentum']:.3f}) with good volume"
            elif attractiveness < -0.02:  # Negative signal
                action = 'SELL'
                reasoning = f"Negative momentum ({analysis['momentum']:.3f}), reducing exposure"
            else:
                continue
            
            # Calculate position size
            max_investment = self.portfolio.cash_balance * self.max_position_size
            position_size = max_investment * confidence
            quantity = position_size / analysis['price']
            
            # Check if we have existing position for SELL orders
            if action == 'SELL':
                current_position = self.portfolio.positions.get(symbol, {}).get('quantity', 0)
                if current_position == 0:
                    continue
                quantity = min(quantity, current_position * 0.5)  # Sell max 50%
            
            decision = TradingDecision(
                symbol=symbol,
                action=action,
                quantity=quantity,
                confidence=confidence,
                reasoning=reasoning,
                timestamp=datetime.now(),
                agent_id=self.agent_id
            )
            
            decisions.append(decision)
        
        return decisions

    def execute_decision(self, decision: TradingDecision, current_price: float) -> bool:
        """Execute a trading decision"""
        try:
            if decision.action == 'BUY':
                cost = decision.quantity * current_price
                
                if cost <= self.portfolio.cash_balance:
                    # Update cash
                    self.portfolio.cash_balance -= cost
                    
                    # Update position
                    if decision.symbol not in self.portfolio.positions:
                        self.portfolio.positions[decision.symbol] = {'quantity': 0, 'avg_price': 0}
                    
                    pos = self.portfolio.positions[decision.symbol]
                    total_quantity = pos['quantity'] + decision.quantity
                    total_cost = (pos['quantity'] * pos['avg_price']) + cost
                    
                    self.portfolio.positions[decision.symbol] = {
                        'quantity': total_quantity,
                        'avg_price': total_cost / total_quantity
                    }
                    
                    logger.info(f"✅ {self.agent_id}: BUY {decision.quantity:.6f} {decision.symbol} @ ${current_price:.4f}")
                    return True
            
            elif decision.action == 'SELL':
                if decision.symbol in self.portfolio.positions:
                    pos = self.portfolio.positions[decision.symbol]
                    
                    if pos['quantity'] >= decision.quantity:
                        # Execute sell
                        revenue = decision.quantity * current_price
                        self.portfolio.cash_balance += revenue
                        
                        # Update position
                        pos['quantity'] -= decision.quantity
                        
                        # Remove position if quantity is negligible
                        if pos['quantity'] < 0.000001:
                            del self.portfolio.positions[decision.symbol]
                        
                        logger.info(f"✅ {self.agent_id}: SELL {decision.quantity:.6f} {decision.symbol} @ ${current_price:.4f}")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ {self.agent_id}: Execution error: {e}")
            return False

    def update_portfolio_value(self, market_data: Dict):
        """Update portfolio value based on current market prices"""
        positions_value = 0
        unrealized_pnl = 0
        
        for symbol, position in self.portfolio.positions.items():
            if symbol in market_data:
                current_price = market_data[symbol]['price']
                position_value = position['quantity'] * current_price
                positions_value += position_value
                
                # Calculate unrealized P&L
                cost_basis = position['quantity'] * position['avg_price']
                unrealized_pnl += (position_value - cost_basis)
        
        self.portfolio.total_value = self.portfolio.cash_balance + positions_value
        self.portfolio.unrealized_pnl = unrealized_pnl

    def get_performance_metrics(self) -> Dict:
        """Get agent performance metrics"""
        total_return = (self.portfolio.total_value - self.initial_balance) / self.initial_balance
        
        return {
            'agent_id': self.agent_id,
            'initial_balance': self.initial_balance,
            'current_value': self.portfolio.total_value,
            'cash_balance': self.portfolio.cash_balance,
            'total_return': total_return,
            'unrealized_pnl': self.portfolio.unrealized_pnl,
            'active_positions': len(self.portfolio.positions),
            'total_decisions': len(self.decisions_history)
        }

class AutonomousTradingSystem:
    """System managing multiple autonomous trading agents"""
    
    def __init__(self):
        self.agents = {}
        self.market_data = {}
        self.system_db = "autonomous_trading.db"
        self._initialize_database()
        
    def _initialize_database(self):
        """Initialize database for autonomous trading"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS autonomous_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                confidence REAL,
                reasoning TEXT,
                executed BOOLEAN
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_performance (
                timestamp TEXT,
                agent_id TEXT,
                portfolio_value REAL,
                cash_balance REAL,
                total_return REAL,
                active_positions INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()

    def add_agent(self, agent_id: str, initial_balance: float = 1000.0):
        """Add a new autonomous agent"""
        agent = AutonomousAgent(agent_id, initial_balance)
        self.agents[agent_id] = agent
        logger.info(f"🤖 Added autonomous agent: {agent_id}")

    async def fetch_market_data(self):
        """Fetch real-time market data"""
        try:
            # Use Binance API for real data
            async with aiohttp.ClientSession() as session:
                url = "https://api.binance.com/api/v3/ticker/24hr"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Filter for major pairs
                        major_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
                        
                        for ticker in data:
                            if ticker['symbol'] in major_pairs:
                                self.market_data[ticker['symbol']] = {
                                    'price': float(ticker['lastPrice']),
                                    'volume': float(ticker['volume']),
                                    'change_24h': float(ticker['priceChangePercent']),
                                    'high_24h': float(ticker['highPrice']),
                                    'low_24h': float(ticker['lowPrice'])
                                }
                        
                        logger.info(f"📊 Updated market data for {len(self.market_data)} symbols")
                        return True
        except Exception as e:
            logger.error(f"❌ Market data fetch error: {e}")
            return False

    async def run_trading_cycle(self):
        """Run one complete trading cycle for all agents"""
        # Fetch latest market data
        if not await self.fetch_market_data():
            return
        
        # Update all agent portfolios
        for agent in self.agents.values():
            agent.update_portfolio_value(self.market_data)
        
        # Each agent makes independent decisions
        all_decisions = []
        
        for agent in self.agents.values():
            # Analyze market
            analysis = agent.analyze_market_data(self.market_data)
            
            # Make decisions
            decisions = agent.make_trading_decision(analysis)
            
            # Execute decisions
            for decision in decisions:
                current_price = self.market_data[decision.symbol]['price']
                executed = agent.execute_decision(decision, current_price)
                
                # Save decision to database
                self._save_decision(decision, current_price, executed)
                
                if executed:
                    agent.decisions_history.append(decision)
                    all_decisions.append(decision)
        
        # Save performance metrics
        for agent in self.agents.values():
            self._save_performance(agent.get_performance_metrics())
        
        logger.info(f"🔄 Trading cycle complete: {len(all_decisions)} decisions executed")
        return all_decisions

    def _save_decision(self, decision: TradingDecision, price: float, executed: bool):
        """Save trading decision to database"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO autonomous_decisions 
            (timestamp, agent_id, symbol, action, quantity, price, confidence, reasoning, executed)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            decision.timestamp.isoformat(),
            decision.agent_id,
            decision.symbol,
            decision.action,
            decision.quantity,
            price,
            decision.confidence,
            decision.reasoning,
            executed
        ))
        
        conn.commit()
        conn.close()

    def _save_performance(self, metrics: Dict):
        """Save agent performance to database"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO agent_performance 
            (timestamp, agent_id, portfolio_value, cash_balance, total_return, active_positions)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(),
            metrics['agent_id'],
            metrics['current_value'],
            metrics['cash_balance'],
            metrics['total_return'],
            metrics['active_positions']
        ))
        
        conn.commit()
        conn.close()

    def display_system_status(self):
        """Display current system status"""
        print("\n🤖 AUTONOMOUS TRADING SYSTEM STATUS")
        print("=" * 60)
        
        total_value = 0
        total_initial = 0
        
        for agent_id, agent in self.agents.items():
            metrics = agent.get_performance_metrics()
            total_value += metrics['current_value']
            total_initial += metrics['initial_balance']
            
            print(f"\n🔹 {agent_id.upper()}")
            print(f"   Portfolio Value: ${metrics['current_value']:,.2f}")
            print(f"   Cash Balance: ${metrics['cash_balance']:,.2f}")
            print(f"   Total Return: {metrics['total_return']:+.2%}")
            print(f"   Active Positions: {metrics['active_positions']}")
            print(f"   Decisions Made: {metrics['total_decisions']}")
        
        system_return = (total_value - total_initial) / total_initial if total_initial > 0 else 0
        
        print(f"\n📊 SYSTEM TOTALS:")
        print(f"   Total Portfolio Value: ${total_value:,.2f}")
        print(f"   System Return: {system_return:+.2%}")
        print(f"   Active Agents: {len(self.agents)}")

    async def run_autonomous_trading(self, duration_minutes: int = 30):
        """Run autonomous trading for specified duration"""
        logger.info(f"🚀 Starting autonomous trading for {duration_minutes} minutes")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        cycle_count = 0
        
        while datetime.now() < end_time:
            cycle_count += 1
            logger.info(f"🔄 Trading cycle {cycle_count}")
            
            # Run trading cycle
            decisions = await self.run_trading_cycle()
            
            # Display status
            if cycle_count % 5 == 0:  # Every 5 cycles
                self.display_system_status()
            
            # Wait before next cycle
            await asyncio.sleep(60)  # 1 minute between cycles
        
        # Final status
        print(f"\n🏁 AUTONOMOUS TRADING COMPLETED")
        print(f"Total cycles: {cycle_count}")
        self.display_system_status()

async def main():
    """Main autonomous trading demonstration"""
    print("🤖 AUTONOMOUS AI TRADING SYSTEM")
    print("=" * 50)
    
    # Create trading system
    system = AutonomousTradingSystem()
    
    # Add multiple autonomous agents
    system.add_agent("momentum_trader", 2000.0)
    system.add_agent("contrarian_trader", 2000.0)
    system.add_agent("volume_trader", 2000.0)
    
    print(f"✅ Created {len(system.agents)} autonomous agents")
    
    # Run autonomous trading
    await system.run_autonomous_trading(duration_minutes=5)  # 5-minute demo
    
    print(f"\n🎯 Autonomous trading demonstration complete!")
    print(f"Database: {system.system_db}")

if __name__ == "__main__":
    asyncio.run(main())
