#!/usr/bin/env python3
"""
🚀 ENHANCED OLLAMA TRADING - NEXT STEPS
Enhanced version with trade execution and portfolio management
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import threading
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("EnhancedOllamaTrading")

@dataclass
class EnhancedOllamaAgent:
    """Enhanced Ollama AI Agent with trade execution"""
    model_name: str
    agent_id: str
    initial_balance: float
    current_balance: float
    risk_tolerance: float
    confidence_threshold: float
    max_position_size: float
    personality: str
    trades_executed: int = 0
    total_pnl: float = 0.0
    active: bool = True

class EnhancedOllamaTrading:
    """Enhanced Ollama trading with actual trade execution"""
    
    def __init__(self):
        self.db_path = "enhanced_ollama_trading.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # Enhanced Ollama agents with trade execution
        self.ollama_agents = {}
        self.agent_portfolios = {}
        
        # Market data and prices
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        self.current_prices = {}
        
        self._initialize_enhanced_database()
        self._initialize_enhanced_agents()

    def _initialize_enhanced_database(self):
        """Initialize enhanced database with trade execution"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Enhanced agents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_agents (
                agent_id TEXT PRIMARY KEY,
                model_name TEXT,
                initial_balance REAL,
                current_balance REAL,
                total_pnl REAL,
                trades_executed INTEGER,
                win_rate REAL,
                sharpe_ratio REAL,
                max_drawdown REAL,
                last_updated TEXT
            )
        ''')
        
        # Executed trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS executed_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                value REAL,
                confidence REAL,
                ai_reasoning TEXT,
                pnl REAL,
                portfolio_value_after REAL
            )
        ''')
        
        # Portfolio positions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                symbol TEXT,
                quantity REAL,
                avg_price REAL,
                current_price REAL,
                unrealized_pnl REAL,
                position_value REAL
            )
        ''')
        
        # Performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                portfolio_value REAL,
                total_return REAL,
                daily_return REAL,
                volatility REAL,
                sharpe_ratio REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Enhanced database initialized: {self.db_path}")

    def _initialize_enhanced_agents(self):
        """Initialize enhanced Ollama agents with trade execution capability"""
        agent_configs = [
            # Top performing models from previous run
            EnhancedOllamaAgent("marco-o1:7b", "marco_trader", 15000.0, 15000.0, 0.02, 0.7, 0.15, "Analytical trader"),
            EnhancedOllamaAgent("cogito:32b", "cogito_trader", 20000.0, 20000.0, 0.015, 0.75, 0.12, "Philosophical trader"),
            EnhancedOllamaAgent("command-r:35b", "command_trader", 30000.0, 30000.0, 0.03, 0.6, 0.18, "Command trader"),
            EnhancedOllamaAgent("gemma3:27b", "gemma_trader", 22000.0, 22000.0, 0.02, 0.65, 0.14, "Google AI trader"),
            EnhancedOllamaAgent("mistral-small:24b", "mistral_trader", 16000.0, 16000.0, 0.025, 0.6, 0.16, "European trader"),
            EnhancedOllamaAgent("falcon3:10b", "falcon_trader", 14000.0, 14000.0, 0.04, 0.5, 0.2, "Speed trader"),
            EnhancedOllamaAgent("granite3.3:8b", "granite_trader", 19000.0, 19000.0, 0.015, 0.7, 0.1, "Enterprise trader"),
            EnhancedOllamaAgent("qwen3:32b", "qwen_trader", 21000.0, 21000.0, 0.02, 0.6, 0.15, "Global trader")
        ]
        
        for agent in agent_configs:
            self.ollama_agents[agent.agent_id] = agent
            self.agent_portfolios[agent.agent_id] = {
                'cash': agent.current_balance,
                'positions': {},
                'trades': [],
                'performance_history': []
            }
        
        logger.info(f"✅ Initialized {len(self.ollama_agents)} enhanced Ollama agents")

    async def call_ollama_for_trading(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Enhanced Ollama call with better error handling"""
        try:
            url = f"{self.ollama_url}/api/generate"
            
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,  # Lower temperature for more consistent trading decisions
                    "top_p": 0.8,
                    "max_tokens": 300
                }
            }
            
            async with self.session.post(url, json=payload, timeout=60) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': model_name
                    }
                else:
                    return {'success': False, 'error': f"API error {response.status}"}
                    
        except asyncio.TimeoutError:
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def create_enhanced_trading_prompt(self, agent: EnhancedOllamaAgent, market_data: Dict[str, Any]) -> str:
        """Create enhanced trading prompt with portfolio context"""
        
        portfolio = self.agent_portfolios[agent.agent_id]
        total_value = portfolio['cash'] + sum(
            pos['quantity'] * self.current_prices.get(symbol, pos['avg_price']) 
            for symbol, pos in portfolio['positions'].items()
        )
        
        total_return = (total_value - agent.initial_balance) / agent.initial_balance * 100
        
        prompt = f"""You are {agent.personality} using {agent.model_name}.

PORTFOLIO STATUS:
- Total Value: ${total_value:,.2f}
- Cash: ${portfolio['cash']:,.2f}
- Return: {total_return:+.2f}%
- Trades: {agent.trades_executed}
- P&L: ${agent.total_pnl:+,.2f}

CURRENT POSITIONS:
"""
        
        if portfolio['positions']:
            for symbol, pos in portfolio['positions'].items():
                current_price = self.current_prices.get(symbol, pos['avg_price'])
                unrealized_pnl = (current_price - pos['avg_price']) * pos['quantity']
                prompt += f"- {symbol}: {pos['quantity']:.4f} @ ${pos['avg_price']:.2f} (P&L: ${unrealized_pnl:+.2f})\n"
        else:
            prompt += "- No current positions\n"
        
        prompt += f"\nMARKET DATA:\n"
        for symbol, data in market_data.items():
            prompt += f"- {symbol}: ${data['price']:.4f} ({data['change_24h']:+.2f}%)\n"
        
        prompt += f"""
TRADING RULES:
- Risk Tolerance: {agent.risk_tolerance*100:.1f}%
- Min Confidence: {agent.confidence_threshold*100:.0f}%
- Max Position: {agent.max_position_size*100:.0f}%
- Available Cash: ${portfolio['cash']:,.2f}

DECISION REQUIRED:
Analyze and decide. Respond EXACTLY:
ACTION: [BUY/SELL/HOLD]
SYMBOL: [symbol or NONE]
AMOUNT: [dollar amount or NONE]
CONFIDENCE: [0-100]
REASON: [brief explanation]

Be decisive and specific."""

        return prompt

    async def execute_ollama_trading_decisions(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute trading decisions from Ollama models"""
        decisions = {}
        trades_executed = 0
        
        for agent_id, agent in self.ollama_agents.items():
            if not agent.active:
                continue
            
            try:
                # Get AI decision
                prompt = self.create_enhanced_trading_prompt(agent, market_data)
                result = await self.call_ollama_for_trading(agent.model_name, prompt)
                
                if result['success']:
                    decision = self._parse_enhanced_response(result['response'])
                    
                    if decision and decision['action'] != 'HOLD':
                        # Execute the trade
                        trade_executed = self._execute_trade(agent_id, decision, market_data)
                        
                        if trade_executed:
                            trades_executed += 1
                            decisions[agent_id] = {
                                'decision': decision,
                                'trade_executed': True,
                                'model': agent.model_name
                            }
                            
                            logger.info(f"✅ {agent.model_name}: {decision['action']} {decision['symbol']} "
                                      f"${decision['amount']} (Confidence: {decision['confidence']}%)")
                        else:
                            decisions[agent_id] = {
                                'decision': decision,
                                'trade_executed': False,
                                'reason': 'Trade execution failed'
                            }
                    else:
                        decisions[agent_id] = {
                            'decision': decision or {'action': 'HOLD'},
                            'trade_executed': False,
                            'reason': 'HOLD decision or parsing failed'
                        }
                
                await asyncio.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"❌ Error with {agent_id}: {e}")
        
        return {'decisions': decisions, 'trades_executed': trades_executed}

    def _parse_enhanced_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse enhanced AI response"""
        try:
            lines = response.strip().split('\n')
            decision = {}
            
            for line in lines:
                line = line.strip()
                if line.startswith('ACTION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('AMOUNT:'):
                    amount_str = line.split(':', 1)[1].strip()
                    if amount_str != 'NONE':
                        try:
                            # Extract number from string (remove $ and commas)
                            amount_clean = ''.join(c for c in amount_str if c.isdigit() or c == '.')
                            decision['amount'] = float(amount_clean) if amount_clean else 0
                        except:
                            decision['amount'] = 0
                    else:
                        decision['amount'] = 0
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 50
                elif line.startswith('REASON:'):
                    decision['reason'] = line.split(':', 1)[1].strip()
            
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing response: {e}")
            return None

    def _execute_trade(self, agent_id: str, decision: Dict[str, Any], market_data: Dict[str, Any]) -> bool:
        """Execute actual trade for agent"""
        try:
            agent = self.ollama_agents[agent_id]
            portfolio = self.agent_portfolios[agent_id]
            
            symbol = decision['symbol']
            action = decision['action']
            amount = decision['amount']
            confidence = decision['confidence']
            
            if not symbol or symbol not in market_data:
                return False
            
            current_price = market_data[symbol]['price']
            
            # Validate confidence threshold
            if confidence < agent.confidence_threshold * 100:
                return False
            
            if action == 'BUY':
                # Validate amount and cash
                if amount > portfolio['cash'] or amount < 100:  # Min $100 trade
                    return False
                
                quantity = amount / current_price
                
                # Execute buy
                portfolio['cash'] -= amount
                
                if symbol not in portfolio['positions']:
                    portfolio['positions'][symbol] = {'quantity': 0, 'avg_price': 0}
                
                pos = portfolio['positions'][symbol]
                total_quantity = pos['quantity'] + quantity
                total_cost = (pos['quantity'] * pos['avg_price']) + amount
                
                portfolio['positions'][symbol] = {
                    'quantity': total_quantity,
                    'avg_price': total_cost / total_quantity
                }
                
                # Record trade
                trade = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': current_price,
                    'value': amount,
                    'confidence': confidence,
                    'reasoning': decision.get('reason', ''),
                    'pnl': 0  # No P&L on buy
                }
                
                portfolio['trades'].append(trade)
                agent.trades_executed += 1
                
                self._save_trade(trade)
                
                return True
                
            elif action == 'SELL':
                if symbol not in portfolio['positions']:
                    return False
                
                pos = portfolio['positions'][symbol]
                if pos['quantity'] <= 0:
                    return False
                
                # Calculate sell quantity based on amount or percentage
                if amount > 0:
                    sell_quantity = min(amount / current_price, pos['quantity'])
                else:
                    sell_quantity = pos['quantity'] * 0.5  # Sell 50% if no amount specified
                
                sell_value = sell_quantity * current_price
                
                # Calculate P&L
                cost_basis = sell_quantity * pos['avg_price']
                pnl = sell_value - cost_basis
                
                # Execute sell
                portfolio['cash'] += sell_value
                pos['quantity'] -= sell_quantity
                
                if pos['quantity'] < 0.000001:
                    del portfolio['positions'][symbol]
                
                # Record trade
                trade = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'price': current_price,
                    'value': sell_value,
                    'confidence': confidence,
                    'reasoning': decision.get('reason', ''),
                    'pnl': pnl
                }
                
                portfolio['trades'].append(trade)
                agent.trades_executed += 1
                agent.total_pnl += pnl
                
                self._save_trade(trade)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Trade execution error: {e}")
            return False

    def _save_trade(self, trade: Dict[str, Any]):
        """Save executed trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO executed_trades 
                (timestamp, agent_id, model_name, symbol, action, quantity, price, value, 
                 confidence, ai_reasoning, pnl, portfolio_value_after)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade['timestamp'], trade['agent_id'], trade['model_name'], trade['symbol'],
                trade['action'], trade['quantity'], trade['price'], trade['value'],
                trade['confidence'], trade['reasoning'], trade['pnl'], 0  # Portfolio value calculated separately
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error saving trade: {e}")

    async def collect_enhanced_market_data(self):
        """Collect market data with price tracking"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': 'bitcoin,ethereum,cardano,solana,polkadot',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    symbol_map = {
                        'bitcoin': 'BTCUSDT',
                        'ethereum': 'ETHUSDT',
                        'cardano': 'ADAUSDT',
                        'solana': 'SOLUSDT',
                        'polkadot': 'DOTUSDT'
                    }
                    
                    market_data = {}
                    for coin_id, coin_data in data.items():
                        if coin_id in symbol_map:
                            symbol = symbol_map[coin_id]
                            price = coin_data['usd']
                            
                            market_data[symbol] = {
                                'price': price,
                                'change_24h': coin_data.get('usd_24h_change', 0),
                                'timestamp': datetime.now().isoformat()
                            }
                            
                            self.current_prices[symbol] = price
                    
                    return market_data
                    
        except Exception as e:
            logger.error(f"❌ Market data error: {e}")
            return {}

    def display_enhanced_status(self):
        """Display enhanced trading status"""
        print(f"\n🚀 ENHANCED OLLAMA TRADING STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 90)
        
        total_value = 0
        total_initial = 0
        total_trades = 0
        total_pnl = 0
        
        for agent_id, agent in self.ollama_agents.items():
            portfolio = self.agent_portfolios[agent_id]
            
            # Calculate portfolio value
            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )
            
            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100
            
            total_value += portfolio_value
            total_initial += agent.initial_balance
            total_trades += agent.trades_executed
            total_pnl += agent.total_pnl
            
            print(f"🤖 {agent.model_name:20} | ${portfolio_value:8,.2f} | {agent_return:+6.2f}% | "
                  f"{agent.trades_executed:3d} trades | P&L: ${agent.total_pnl:+7.2f}")
        
        system_return = (total_value - total_initial) / total_initial * 100 if total_initial > 0 else 0
        
        print(f"\n📊 SYSTEM TOTALS:")
        print(f"   Total Portfolio Value: ${total_value:,.2f}")
        print(f"   System Return: {system_return:+.2f}%")
        print(f"   Total Trades: {total_trades}")
        print(f"   Total P&L: ${total_pnl:+,.2f}")
        
        if self.current_prices:
            print(f"\n📈 CURRENT PRICES:")
            for symbol, price in self.current_prices.items():
                print(f"   {symbol:10} | ${price:8.4f}")
        
        print("=" * 90)

    async def run_enhanced_ollama_trading(self, duration_minutes: int = 60):
        """Run enhanced Ollama trading with actual execution"""
        logger.info(f"🚀 Starting enhanced Ollama trading for {duration_minutes} minutes")
        
        self.session = aiohttp.ClientSession()
        
        try:
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0
            
            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Enhanced trading cycle {cycle_count}")
                
                # Collect market data
                market_data = await self.collect_enhanced_market_data()
                
                if market_data:
                    # Execute trading decisions
                    results = await self.execute_ollama_trading_decisions(market_data)
                    
                    if results['trades_executed'] > 0:
                        logger.info(f"💰 Executed {results['trades_executed']} trades this cycle")
                    
                    # Display status every 2 cycles
                    if cycle_count % 2 == 0:
                        self.display_enhanced_status()
                
                # Wait before next cycle
                await asyncio.sleep(180)  # 3 minutes between cycles
            
            # Final status
            print(f"\n🏁 ENHANCED OLLAMA TRADING COMPLETED")
            self.display_enhanced_status()
            
        finally:
            await self.session.close()

async def main():
    """Main enhanced Ollama trading demonstration"""
    print("🚀 ENHANCED OLLAMA TRADING SYSTEM")
    print("=" * 60)
    print("Next-level Ollama AI trading with:")
    print("• Real trade execution")
    print("• Portfolio management")
    print("• P&L tracking")
    print("• Enhanced decision making")
    print("=" * 60)
    
    system = EnhancedOllamaTrading()
    
    # Run enhanced trading
    await system.run_enhanced_ollama_trading(duration_minutes=30)
    
    print(f"\n✅ Enhanced Ollama trading completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
