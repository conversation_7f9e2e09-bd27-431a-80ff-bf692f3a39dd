#!/usr/bin/env python3
"""
Real NORYON V2 AI Trading System
Main entry point for the real AI trading system using your actual Ollama models.
"""

import asyncio
import logging
import signal
import sys
import argparse
from datetime import datetime
import json

# Add src to path
sys.path.append('src')

from agents.real_agent_manager import RealAgentManager
from api.main import app
import uvicorn


class RealNoryonSystem:
    """
    Real NORYON V2 AI Trading System
    
    Uses your actual available Ollama models:
    - marco-o1:7b (Market Watcher)
    - magistral:24b (Strategy Researcher)
    - command-r:35b (Risk Officer)
    - cogito:32b (Technical Analyst)
    - gemma3:27b (News Analyst)
    - mistral-small:24b (Trade Executor)
    - falcon3:10b (Compliance Auditor)
    - qwen3:32b (Chief Analyst)
    - deepseek-r1:latest (Portfolio Manager)
    
    Features:
    - Real market data processing
    - Actual AI model inference
    - Live risk management
    - Performance monitoring
    - API server with real endpoints
    """
    
    def __init__(self, config: dict = None):
        self.config = config or {
            "environment": "development",
            "log_level": "INFO",
            "api_host": "0.0.0.0",
            "api_port": 8000,
            "enable_agents": True,
            "enable_api": True
        }
        
        self.logger = self._setup_logging()
        self.agent_manager = RealAgentManager()
        self.running = False
        self.start_time = None
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _setup_logging(self) -> logging.Logger:
        """Setup system logging."""
        logging.basicConfig(
            level=getattr(logging, self.config["log_level"]),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'noryon_real_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        
        return logging.getLogger("RealNoryonSystem")

    async def initialize_system(self):
        """Initialize the complete real system."""
        self.logger.info("🚀 INITIALIZING REAL NORYON V2 AI TRADING SYSTEM")
        self.logger.info("=" * 80)
        self.logger.info("🎯 System: Real AI Trading with Actual Ollama Models")
        self.logger.info("🤖 Models: Using Your Available Models")
        self.logger.info("📊 Data: Real Market Data from Binance")
        self.logger.info("⚡ Features: Live Analysis, Risk Management, Trading")
        self.logger.info("=" * 80)
        
        try:
            # Step 1: Verify system prerequisites
            self.logger.info("🔧 Verifying system prerequisites...")
            await self._verify_prerequisites()
            
            # Step 2: Initialize AI agents
            if self.config["enable_agents"]:
                self.logger.info("🤖 Initializing AI agents...")
                init_results = await self.agent_manager.initialize_real_agents()
                
                if not init_results["summary"]["system_ready"]:
                    self.logger.warning("⚠️ System not fully ready, but continuing...")
                else:
                    self.logger.info("✅ All agents initialized successfully")
            
            self.logger.info("=" * 80)
            self.logger.info("🎉 REAL NORYON V2 SYSTEM INITIALIZATION COMPLETE!")
            self.logger.info("🚀 Ready for Real AI Trading Operations")
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            raise

    async def _verify_prerequisites(self):
        """Verify system prerequisites."""
        # Check Ollama
        model_status = await self.agent_manager.verify_ollama_models()
        available_models = sum(1 for status in model_status.values() if status)
        
        if available_models == 0:
            raise RuntimeError("No Ollama models available")
        
        self.logger.info(f"✅ {available_models} Ollama models available")
        
        # Test API connectivity
        api_tests = await self.agent_manager._test_api_connectivity()
        if not api_tests.get("binance", False):
            self.logger.warning("⚠️ Binance API not accessible - some features may be limited")

    async def start_system(self):
        """Start the complete system."""
        self.logger.info("🚀 STARTING REAL NORYON V2 SYSTEM")
        
        self.running = True
        self.start_time = datetime.utcnow()
        
        tasks = []
        
        # Start AI agents
        if self.config["enable_agents"]:
            self.logger.info("🤖 Starting AI agents...")
            await self.agent_manager.start_real_agents()
            tasks.append(asyncio.create_task(self._monitor_agents()))
        
        # Start API server
        if self.config["enable_api"]:
            self.logger.info("🌐 Starting API server...")
            tasks.append(asyncio.create_task(self._run_api_server()))
        
        # Start system monitoring
        tasks.append(asyncio.create_task(self._system_monitoring()))
        
        self.logger.info("🎯 REAL NORYON V2 SYSTEM FULLY OPERATIONAL!")
        self.logger.info("=" * 60)
        self.logger.info("🤖 AI Agents: ACTIVE")
        self.logger.info("📊 Market Analysis: RUNNING")
        self.logger.info("🛡️ Risk Management: MONITORING")
        self.logger.info("🌐 API Server: AVAILABLE")
        self.logger.info("📈 Performance Tracking: ACTIVE")
        self.logger.info("=" * 60)
        
        # Wait for all tasks
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            self.logger.info("System tasks cancelled")

    async def _run_api_server(self):
        """Run the API server."""
        config = uvicorn.Config(
            app,
            host=self.config["api_host"],
            port=self.config["api_port"],
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

    async def _monitor_agents(self):
        """Monitor agent health and performance."""
        while self.running:
            try:
                # Get system status
                status = await self.agent_manager.get_system_status()
                
                # Log periodic status
                if datetime.utcnow().minute % 5 == 0:  # Every 5 minutes
                    active_agents = status["system_health"]["active_agents"]
                    total_agents = status["system_health"]["total_agents"]
                    
                    self.logger.info(f"💓 System Health: {active_agents}/{total_agents} agents active")
                    
                    # Log agent details
                    for agent_name, agent_info in status["agents"].items():
                        if agent_info["status"] == "running":
                            self.logger.info(f"  ✅ {agent_name}: {agent_info['tasks_completed']} tasks, "
                                           f"{agent_info['success_rate']:.1%} success rate")
                        else:
                            self.logger.warning(f"  ❌ {agent_name}: {agent_info['status']}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Agent monitoring error: {e}")
                await asyncio.sleep(30)

    async def _system_monitoring(self):
        """Monitor overall system performance."""
        while self.running:
            try:
                # Log system uptime and performance
                uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
                
                if uptime > 0 and uptime % 1800 == 0:  # Every 30 minutes
                    self.logger.info(f"📊 System Uptime: {uptime/3600:.1f} hours")
                    
                    # Get detailed status
                    status = await self.agent_manager.get_system_status()
                    
                    # Log performance metrics
                    memory_mb = status["system_health"]["memory_usage_mb"]
                    cpu_percent = status["system_health"]["cpu_usage_percent"]
                    
                    self.logger.info(f"💾 Resource Usage: {memory_mb:.0f}MB memory, {cpu_percent:.1f}% CPU")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"System monitoring error: {e}")
                await asyncio.sleep(120)

    async def stop_system(self):
        """Stop the system gracefully."""
        self.logger.info("🛑 STOPPING REAL NORYON V2 SYSTEM...")
        
        self.running = False
        
        # Stop agents
        if self.config["enable_agents"]:
            await self.agent_manager.stop_all_agents()
        
        # Generate final report
        await self._generate_final_report()
        
        self.logger.info("✅ REAL NORYON V2 SYSTEM STOPPED SUCCESSFULLY")

    async def _generate_final_report(self):
        """Generate final system report."""
        uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
        
        # Get final status
        status = await self.agent_manager.get_system_status()
        
        self.logger.info("📊 FINAL SYSTEM REPORT")
        self.logger.info("=" * 60)
        self.logger.info(f"🕐 Total Runtime: {uptime:.1f} seconds ({uptime/3600:.1f} hours)")
        self.logger.info(f"🤖 Agents Managed: {status['system_health']['total_agents']}")
        self.logger.info(f"📈 Total Tasks: {sum(agent['tasks_completed'] for agent in status['agents'].values())}")
        self.logger.info(f"📊 Average Success Rate: {sum(agent['success_rate'] for agent in status['agents'].values()) / len(status['agents']):.1%}")
        self.logger.info("🏆 ACHIEVEMENTS:")
        self.logger.info("  ✅ Real AI Trading System Operational")
        self.logger.info("  ✅ Multiple Ollama Models Coordinated")
        self.logger.info("  ✅ Live Market Data Processing")
        self.logger.info("  ✅ Real-time Risk Management")
        self.logger.info("  ✅ Performance Monitoring Active")
        self.logger.info("=" * 60)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        
        # Create shutdown task
        loop = asyncio.get_event_loop()
        loop.create_task(self.stop_system())

    async def get_system_status(self):
        """Get current system status."""
        if self.config["enable_agents"]:
            return await self.agent_manager.get_system_status()
        else:
            return {
                "system_info": {
                    "running": self.running,
                    "uptime": (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0,
                    "agents_enabled": False
                }
            }

    async def run_forever(self):
        """Run the system indefinitely."""
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Keyboard interrupt received, shutting down...")
        finally:
            await self.stop_system()


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Real NORYON V2 AI Trading System")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--no-agents", action="store_true", help="Disable AI agents")
    parser.add_argument("--no-api", action="store_true", help="Disable API server")
    parser.add_argument("--port", type=int, default=8000, help="API server port")
    parser.add_argument("--log-level", default="INFO", help="Log level")
    args = parser.parse_args()
    
    # Create configuration
    config = {
        "environment": "production",
        "log_level": args.log_level,
        "api_host": "0.0.0.0",
        "api_port": args.port,
        "enable_agents": not args.no_agents,
        "enable_api": not args.no_api
    }
    
    # Load config file if provided
    if args.config:
        try:
            with open(args.config, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"Error loading config file: {e}")
            sys.exit(1)
    
    # Create and run system
    system = RealNoryonSystem(config)
    
    try:
        # Initialize system
        await system.initialize_system()
        
        # Start system
        await system.start_system()
        
        # Run forever
        await system.run_forever()
        
    except Exception as e:
        logging.error(f"System error: {e}")
        await system.stop_system()
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 NORYON V2 REAL AI TRADING SYSTEM")
    print("=" * 50)
    print("Using your actual Ollama models for real trading")
    print("=" * 50)
    
    asyncio.run(main())
