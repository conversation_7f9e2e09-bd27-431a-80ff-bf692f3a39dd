#!/usr/bin/env python3
"""
🤖 AI SYSTEM FULL ACTIVATION
Complete activation of all AI systems with long-term simulations
"""

import asyncio
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import signal
import sys

# Import all our systems
from comprehensive_long_term_system import ComprehensiveLongTermSystem
from working_realistic_system import WorkingRealisticSystem
from advanced_model_architectures import AdvancedModelArchitectures

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AISystemFullActivation")

class AISystemFullActivation:
    """Complete AI system activation with all components"""
    
    def __init__(self):
        self.systems = {}
        self.system_threads = {}
        self.system_active = True
        self.start_time = datetime.now()
        
        # Performance tracking
        self.activation_metrics = {
            'systems_activated': 0,
            'total_trades': 0,
            'total_portfolio_value': 0,
            'system_uptime': 0,
            'errors_encountered': 0
        }
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"🛑 Received shutdown signal {signum}")
        self.system_active = False
        self._shutdown_all_systems()
        sys.exit(0)

    def _shutdown_all_systems(self):
        """Shutdown all systems gracefully"""
        logger.info("🔄 Shutting down all AI systems...")
        
        for system_name, system in self.systems.items():
            try:
                if hasattr(system, 'system_active'):
                    system.system_active = False
                logger.info(f"✅ {system_name} shutdown initiated")
            except Exception as e:
                logger.error(f"❌ Error shutting down {system_name}: {e}")
        
        # Wait for threads to complete
        for thread_name, thread in self.system_threads.items():
            if thread.is_alive():
                logger.info(f"⏳ Waiting for {thread_name} to complete...")
                thread.join(timeout=5)

    async def activate_comprehensive_system(self):
        """Activate comprehensive long-term system"""
        try:
            logger.info("🚀 Activating Comprehensive Long-Term AI System...")
            
            system = ComprehensiveLongTermSystem()
            self.systems['comprehensive'] = system
            
            # Run comprehensive simulation
            final_metrics = await system.run_comprehensive_long_term_simulation(
                duration_hours=24.0,  # 24 hour simulation
                update_interval_seconds=60  # 1 minute intervals
            )
            
            # Update activation metrics
            self.activation_metrics['total_portfolio_value'] += final_metrics.total_portfolio_value
            self.activation_metrics['total_trades'] += final_metrics.total_trades
            self.activation_metrics['systems_activated'] += 1
            
            logger.info("✅ Comprehensive system completed successfully")
            return final_metrics
            
        except Exception as e:
            logger.error(f"❌ Comprehensive system error: {e}")
            self.activation_metrics['errors_encountered'] += 1
            return None

    async def activate_realistic_system(self):
        """Activate realistic working system"""
        try:
            logger.info("🔧 Activating Realistic Working System...")
            
            system = WorkingRealisticSystem()
            self.systems['realistic'] = system
            
            # Run realistic system
            results = await system.run_working_system(duration_minutes=120)  # 2 hours
            
            # Update activation metrics
            if results:
                self.activation_metrics['total_portfolio_value'] += results.get('portfolio_value', 0)
                self.activation_metrics['total_trades'] += results.get('executed_trades', 0)
                self.activation_metrics['systems_activated'] += 1
            
            logger.info("✅ Realistic system completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"❌ Realistic system error: {e}")
            self.activation_metrics['errors_encountered'] += 1
            return None

    def activate_advanced_models(self):
        """Activate advanced model architectures"""
        try:
            logger.info("🧠 Activating Advanced Model Architectures...")
            
            model_architect = AdvancedModelArchitectures()
            self.systems['advanced_models'] = model_architect
            
            # Create synthetic data for demonstration
            import numpy as np
            n_samples = 1000
            n_features = 30
            
            X = np.random.randn(n_samples, n_features)
            y = (X[:, 0] * X[:, 1] + 
                 np.sin(X[:, 2]) + 
                 X[:, 3]**2 + 
                 np.random.randn(n_samples) * 0.1)
            
            feature_names = [f'feature_{i}' for i in range(n_features)]
            
            # Split data
            split_idx = int(0.8 * n_samples)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Train advanced ensemble
            results = model_architect.train_advanced_ensemble(
                X_train, X_test, y_train, y_test, feature_names
            )
            
            if results:
                best_model_name = results['best_model_name']
                best_performance = results['model_results'][best_model_name]
                
                logger.info(f"✅ Advanced models activated successfully")
                logger.info(f"   Best model: {best_model_name}")
                logger.info(f"   Test R²: {best_performance['test_r2']:.6f}")
                
                self.activation_metrics['systems_activated'] += 1
                return results
            else:
                logger.error("❌ Advanced model training failed")
                self.activation_metrics['errors_encountered'] += 1
                return None
                
        except Exception as e:
            logger.error(f"❌ Advanced models error: {e}")
            self.activation_metrics['errors_encountered'] += 1
            return None

    def run_continuous_monitoring(self):
        """Run continuous system monitoring"""
        logger.info("📊 Starting continuous system monitoring...")
        
        while self.system_active:
            try:
                # Update system uptime
                uptime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
                self.activation_metrics['system_uptime'] = uptime_hours
                
                # Display status every 5 minutes
                if int(uptime_hours * 12) % 1 == 0:  # Every 5 minutes
                    self._display_activation_status()
                
                time.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"❌ Monitoring error: {e}")
                self.activation_metrics['errors_encountered'] += 1
                time.sleep(60)

    def _display_activation_status(self):
        """Display comprehensive activation status"""
        print(f"\n🤖 AI SYSTEM FULL ACTIVATION STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        print(f"📊 ACTIVATION METRICS:")
        print(f"   Systems Activated: {self.activation_metrics['systems_activated']}")
        print(f"   Total Portfolio Value: ${self.activation_metrics['total_portfolio_value']:,.2f}")
        print(f"   Total Trades Executed: {self.activation_metrics['total_trades']}")
        print(f"   System Uptime: {self.activation_metrics['system_uptime']:.2f} hours")
        print(f"   Errors Encountered: {self.activation_metrics['errors_encountered']}")
        
        print(f"\n🔧 ACTIVE SYSTEMS:")
        for system_name, system in self.systems.items():
            status = "ACTIVE" if hasattr(system, 'system_active') and getattr(system, 'system_active', True) else "INACTIVE"
            print(f"   {system_name:20} | {status}")
        
        print(f"\n🧵 SYSTEM THREADS:")
        for thread_name, thread in self.system_threads.items():
            status = "RUNNING" if thread.is_alive() else "STOPPED"
            print(f"   {thread_name:20} | {status}")
        
        print("=" * 80)

    async def run_parallel_activation(self):
        """Run all systems in parallel for maximum activation"""
        logger.info("🚀 Starting parallel AI system activation...")
        
        # Create tasks for parallel execution
        tasks = []
        
        # Task 1: Comprehensive long-term system
        comprehensive_task = asyncio.create_task(
            self.activate_comprehensive_system(),
            name="comprehensive_system"
        )
        tasks.append(comprehensive_task)
        
        # Task 2: Realistic working system
        realistic_task = asyncio.create_task(
            self.activate_realistic_system(),
            name="realistic_system"
        )
        tasks.append(realistic_task)
        
        # Task 3: Advanced models (run in thread since it's CPU intensive)
        def run_advanced_models():
            return self.activate_advanced_models()
        
        advanced_models_thread = threading.Thread(
            target=run_advanced_models,
            name="advanced_models_thread"
        )
        self.system_threads['advanced_models'] = advanced_models_thread
        advanced_models_thread.start()
        
        # Task 4: Continuous monitoring (run in thread)
        monitoring_thread = threading.Thread(
            target=self.run_continuous_monitoring,
            name="monitoring_thread"
        )
        self.system_threads['monitoring'] = monitoring_thread
        monitoring_thread.start()
        
        # Wait for async tasks to complete
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.info("✅ All async systems completed")
            
            # Wait for threads to complete
            for thread_name, thread in self.system_threads.items():
                if thread.is_alive():
                    logger.info(f"⏳ Waiting for {thread_name} to complete...")
                    thread.join()
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Parallel activation error: {e}")
            self.activation_metrics['errors_encountered'] += 1
            return None

    async def run_sequential_activation(self):
        """Run systems sequentially for stability"""
        logger.info("🔄 Starting sequential AI system activation...")
        
        results = {}
        
        # Step 1: Activate advanced models first
        logger.info("Step 1/3: Activating advanced models...")
        advanced_results = self.activate_advanced_models()
        results['advanced_models'] = advanced_results
        
        if advanced_results:
            logger.info("✅ Advanced models activated successfully")
        else:
            logger.warning("⚠️ Advanced models activation failed, continuing...")
        
        # Step 2: Activate realistic system
        logger.info("Step 2/3: Activating realistic system...")
        realistic_results = await self.activate_realistic_system()
        results['realistic_system'] = realistic_results
        
        if realistic_results:
            logger.info("✅ Realistic system activated successfully")
        else:
            logger.warning("⚠️ Realistic system activation failed, continuing...")
        
        # Step 3: Activate comprehensive system
        logger.info("Step 3/3: Activating comprehensive system...")
        comprehensive_results = await self.activate_comprehensive_system()
        results['comprehensive_system'] = comprehensive_results
        
        if comprehensive_results:
            logger.info("✅ Comprehensive system activated successfully")
        else:
            logger.warning("⚠️ Comprehensive system activation failed")
        
        return results

    async def run_full_activation(self, mode: str = "parallel"):
        """Run full AI system activation"""
        logger.info(f"🚀 STARTING FULL AI SYSTEM ACTIVATION - {mode.upper()} MODE")
        logger.info(f"Start time: {self.start_time}")
        
        try:
            if mode == "parallel":
                results = await self.run_parallel_activation()
            else:
                results = await self.run_sequential_activation()
            
            # Final status display
            final_uptime = (datetime.now() - self.start_time).total_seconds() / 3600
            self.activation_metrics['system_uptime'] = final_uptime
            
            print(f"\n🏁 FULL AI SYSTEM ACTIVATION COMPLETED")
            print(f"Total runtime: {final_uptime:.2f} hours")
            self._display_activation_status()
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Full activation error: {e}")
            self.activation_metrics['errors_encountered'] += 1
            return None
        finally:
            self.system_active = False
            self._shutdown_all_systems()

async def main():
    """Main AI system full activation"""
    print("🤖 AI SYSTEM FULL ACTIVATION")
    print("=" * 80)
    print("Comprehensive activation of all AI trading systems:")
    print("• Comprehensive Long-Term System (24h simulation)")
    print("• Realistic Working System (2h operation)")
    print("• Advanced Model Architectures (ensemble training)")
    print("• Continuous System Monitoring")
    print("• Parallel/Sequential execution modes")
    print("=" * 80)
    
    # Create activation system
    activation_system = AISystemFullActivation()
    
    # Choose activation mode
    print(f"\n🎯 Choose activation mode:")
    print(f"1. Parallel (all systems simultaneously)")
    print(f"2. Sequential (systems one after another)")
    
    try:
        choice = input("Enter choice (1 or 2, default=1): ").strip()
        mode = "parallel" if choice != "2" else "sequential"
    except:
        mode = "parallel"
    
    print(f"\n🚀 Starting {mode} activation...")
    print(f"Press Ctrl+C to stop activation early")
    
    try:
        # Run full activation
        results = await activation_system.run_full_activation(mode=mode)
        
        if results:
            print(f"\n🏆 ACTIVATION COMPLETED SUCCESSFULLY!")
            print(f"Systems activated: {activation_system.activation_metrics['systems_activated']}")
            print(f"Total portfolio value: ${activation_system.activation_metrics['total_portfolio_value']:,.2f}")
            print(f"Total trades: {activation_system.activation_metrics['total_trades']}")
        else:
            print(f"\n⚠️ Activation completed with issues")
        
    except KeyboardInterrupt:
        print(f"\n🛑 Activation stopped by user")
    except Exception as e:
        print(f"\n❌ Activation failed: {e}")
    
    print(f"\n✅ AI system full activation demonstration completed!")

if __name__ == "__main__":
    asyncio.run(main())
