#!/usr/bin/env python3
"""
🚨 Enhanced Monitoring and Alerting System
Production-ready monitoring with real-time alerts and error tracking
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import sqlite3
import pandas as pd
import numpy as np
import psutil
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("EnhancedMonitoring")

@dataclass
class SystemAlert:
    """System alert structure"""
    alert_id: str
    alert_type: str
    severity: str
    message: str
    timestamp: str
    component: str
    metric_value: float
    threshold_value: float
    resolved: bool = False

@dataclass
class SystemMetrics:
    """System metrics structure"""
    timestamp: str
    cpu_usage: float
    memory_usage: float
    error_rate: float
    api_success_rate: float
    portfolio_value: float
    total_trades: int
    win_rate: float
    max_drawdown: float

class EnhancedMonitoringSystem:
    """Enhanced monitoring system with real-time alerts"""
    
    def __init__(self, trading_system=None):
        self.trading_system = trading_system
        self.db_path = "monitoring_system.db"
        
        # Monitoring state
        self.alerts = []
        self.metrics_history = []
        self.monitoring_active = False
        self.system_start_time = time.time()
        
        # Thresholds
        self.thresholds = {
            "max_drawdown": 0.10,
            "min_win_rate": 0.35,
            "max_error_rate": 0.05,
            "max_memory_usage": 0.80,
            "min_api_success_rate": 0.95
        }
        
        self._initialize_database()
        logger.info("✅ Enhanced Monitoring System initialized")
    
    def _initialize_database(self):
        """Initialize monitoring database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                cpu_usage REAL,
                memory_usage REAL,
                error_rate REAL,
                api_success_rate REAL,
                portfolio_value REAL,
                total_trades INTEGER,
                win_rate REAL,
                max_drawdown REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_id TEXT UNIQUE,
                alert_type TEXT,
                severity TEXT,
                message TEXT,
                timestamp TEXT,
                component TEXT,
                metric_value REAL,
                threshold_value REAL,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Monitoring database initialized: {self.db_path}")
    
    async def start_monitoring(self, interval_seconds: int = 30):
        """Start continuous monitoring"""
        self.monitoring_active = True
        logger.info(f"🚀 Starting enhanced monitoring (interval: {interval_seconds}s)")
        
        try:
            while self.monitoring_active:
                start_time = time.time()
                
                # Collect metrics
                metrics = await self.collect_system_metrics()
                
                # Check for alerts
                alerts = await self.check_alert_conditions(metrics)
                
                # Process alerts
                if alerts:
                    await self.process_alerts(alerts)
                
                # Store metrics
                await self.store_metrics(metrics)
                
                # Wait for next iteration
                duration = time.time() - start_time
                wait_time = max(0, interval_seconds - duration)
                await asyncio.sleep(wait_time)
                
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")
        finally:
            self.monitoring_active = False
            logger.info("🛑 Monitoring stopped")
    
    async def collect_system_metrics(self) -> SystemMetrics:
        """Collect comprehensive system metrics"""
        try:
            # System metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Trading system metrics
            portfolio_value = 0
            total_trades = 0
            win_rate = 0
            max_drawdown = 0
            
            if self.trading_system:
                portfolio_value = self.trading_system.portfolio.total_value
                total_trades = self.trading_system.portfolio.trade_count
                win_rate = self.trading_system.portfolio.win_rate
                max_drawdown = self.trading_system.portfolio.max_drawdown
            
            # Calculate error rate
            error_rate = self._calculate_error_rate()
            api_success_rate = self._calculate_api_success_rate()
            
            metrics = SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                error_rate=error_rate,
                api_success_rate=api_success_rate,
                portfolio_value=portfolio_value,
                total_trades=total_trades,
                win_rate=win_rate,
                max_drawdown=max_drawdown
            )
            
            self.metrics_history.append(metrics)
            
            # Keep only last 1000 metrics
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Error collecting metrics: {e}")
            return SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_usage=0, memory_usage=0, error_rate=0, api_success_rate=1.0,
                portfolio_value=0, total_trades=0, win_rate=0, max_drawdown=0
            )
    
    async def check_alert_conditions(self, metrics: SystemMetrics) -> List[SystemAlert]:
        """Check for alert conditions"""
        alerts = []
        current_time = datetime.now()
        
        # Portfolio alerts
        if metrics.max_drawdown > self.thresholds["max_drawdown"]:
            alert = SystemAlert(
                alert_id=f"drawdown_{int(time.time())}",
                alert_type="PORTFOLIO_RISK",
                severity="HIGH",
                message=f"Maximum drawdown exceeded: {metrics.max_drawdown:.2%}",
                timestamp=current_time.isoformat(),
                component="portfolio",
                metric_value=metrics.max_drawdown,
                threshold_value=self.thresholds["max_drawdown"]
            )
            alerts.append(alert)
        
        if metrics.total_trades > 10 and metrics.win_rate < self.thresholds["min_win_rate"]:
            alert = SystemAlert(
                alert_id=f"winrate_{int(time.time())}",
                alert_type="PERFORMANCE",
                severity="MEDIUM",
                message=f"Low win rate: {metrics.win_rate:.2%}",
                timestamp=current_time.isoformat(),
                component="trading",
                metric_value=metrics.win_rate,
                threshold_value=self.thresholds["min_win_rate"]
            )
            alerts.append(alert)
        
        # System resource alerts
        if metrics.memory_usage > self.thresholds["max_memory_usage"] * 100:
            alert = SystemAlert(
                alert_id=f"memory_{int(time.time())}",
                alert_type="SYSTEM_RESOURCE",
                severity="HIGH",
                message=f"High memory usage: {metrics.memory_usage:.1f}%",
                timestamp=current_time.isoformat(),
                component="system",
                metric_value=metrics.memory_usage / 100,
                threshold_value=self.thresholds["max_memory_usage"]
            )
            alerts.append(alert)
        
        # Error rate alerts
        if metrics.error_rate > self.thresholds["max_error_rate"]:
            alert = SystemAlert(
                alert_id=f"error_rate_{int(time.time())}",
                alert_type="ERROR_RATE",
                severity="HIGH",
                message=f"High error rate: {metrics.error_rate:.2%}",
                timestamp=current_time.isoformat(),
                component="system",
                metric_value=metrics.error_rate,
                threshold_value=self.thresholds["max_error_rate"]
            )
            alerts.append(alert)
        
        # API performance alerts
        if metrics.api_success_rate < self.thresholds["min_api_success_rate"]:
            alert = SystemAlert(
                alert_id=f"api_success_{int(time.time())}",
                alert_type="API_PERFORMANCE",
                severity="MEDIUM",
                message=f"Low API success rate: {metrics.api_success_rate:.2%}",
                timestamp=current_time.isoformat(),
                component="api",
                metric_value=metrics.api_success_rate,
                threshold_value=self.thresholds["min_api_success_rate"]
            )
            alerts.append(alert)
        
        return alerts
    
    async def process_alerts(self, alerts: List[SystemAlert]):
        """Process and send alerts"""
        for alert in alerts:
            self.alerts.append(alert)
            await self._send_alert(alert)
        
        logger.info(f"📢 Processed {len(alerts)} alerts")
    
    async def _send_alert(self, alert: SystemAlert):
        """Send alert (console + database)"""
        # Console alert
        severity_emoji = {"LOW": "🟡", "MEDIUM": "🟠", "HIGH": "🔴", "CRITICAL": "🚨"}
        emoji = severity_emoji.get(alert.severity, "⚠️")
        logger.warning(f"{emoji} {alert.severity} ALERT: {alert.message} [{alert.component}]")
        
        # Database alert
        await self._store_alert(alert)
    
    async def _store_alert(self, alert: SystemAlert):
        """Store alert in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO alerts 
                (alert_id, alert_type, severity, message, timestamp, component, 
                 metric_value, threshold_value, resolved)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.alert_id, alert.alert_type, alert.severity, alert.message,
                alert.timestamp, alert.component, alert.metric_value,
                alert.threshold_value, alert.resolved
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Alert storage failed: {e}")
    
    async def store_metrics(self, metrics: SystemMetrics):
        """Store metrics in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO system_metrics 
                (timestamp, cpu_usage, memory_usage, error_rate, api_success_rate,
                 portfolio_value, total_trades, win_rate, max_drawdown)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metrics.timestamp, metrics.cpu_usage, metrics.memory_usage,
                metrics.error_rate, metrics.api_success_rate, metrics.portfolio_value,
                metrics.total_trades, metrics.win_rate, metrics.max_drawdown
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Metrics storage failed: {e}")
    
    def _calculate_error_rate(self) -> float:
        """Calculate current error rate"""
        if not self.trading_system or not hasattr(self.trading_system, 'error_log'):
            return 0.0
        
        # Calculate error rate over last hour
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_errors = [
            error for error in self.trading_system.error_log
            if datetime.fromisoformat(error.get("timestamp", "2020-01-01")) > one_hour_ago
        ]
        
        # Simple error rate calculation
        total_operations = max(len(getattr(self.trading_system, 'trades_history', [])), 1)
        return min(len(recent_errors) / total_operations, 1.0)
    
    def _calculate_api_success_rate(self) -> float:
        """Calculate API success rate"""
        if not self.trading_system or not hasattr(self.trading_system, 'market_data'):
            return 1.0
        
        # Simple success rate based on market data availability
        return 1.0 if self.trading_system.market_data else 0.5
    
    def get_health_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive health dashboard"""
        if not self.metrics_history:
            return {"status": "NO_DATA"}
        
        latest_metrics = self.metrics_history[-1]
        health_score = self._calculate_health_score(latest_metrics)
        
        # Recent alerts
        recent_alerts = [alert for alert in self.alerts[-10:] if not alert.resolved]
        
        # System status
        system_status = "HEALTHY"
        if health_score < 0.7:
            system_status = "DEGRADED"
        if health_score < 0.5:
            system_status = "CRITICAL"
        
        return {
            "system_status": system_status,
            "health_score": health_score,
            "timestamp": latest_metrics.timestamp,
            "system_metrics": {
                "cpu_usage": latest_metrics.cpu_usage,
                "memory_usage": latest_metrics.memory_usage,
                "error_rate": latest_metrics.error_rate,
                "api_success_rate": latest_metrics.api_success_rate
            },
            "trading_metrics": {
                "portfolio_value": latest_metrics.portfolio_value,
                "total_trades": latest_metrics.total_trades,
                "win_rate": latest_metrics.win_rate,
                "max_drawdown": latest_metrics.max_drawdown
            },
            "active_alerts": len(recent_alerts),
            "recent_alerts": [asdict(alert) for alert in recent_alerts],
            "monitoring_active": self.monitoring_active
        }
    
    def _calculate_health_score(self, metrics: SystemMetrics) -> float:
        """Calculate overall system health score"""
        score = 1.0
        
        # System resource penalties
        if metrics.cpu_usage > 80:
            score -= 0.1
        if metrics.memory_usage > 80:
            score -= 0.1
        
        # Performance penalties
        if metrics.error_rate > 0.05:
            score -= 0.2
        if metrics.api_success_rate < 0.95:
            score -= 0.1
        
        # Trading performance penalties
        if metrics.max_drawdown > 0.1:
            score -= 0.2
        if metrics.total_trades > 10 and metrics.win_rate < 0.4:
            score -= 0.1
        
        return max(0, score)
    
    def stop_monitoring(self):
        """Stop monitoring system"""
        self.monitoring_active = False
        logger.info("🛑 Stopping monitoring system...")


# =============================================================================
# DASHBOARD GENERATOR
# =============================================================================

class MonitoringDashboard:
    """Generate HTML dashboard for monitoring"""
    
    def __init__(self, monitoring_system: EnhancedMonitoringSystem):
        self.monitoring_system = monitoring_system
    
    def generate_html_dashboard(self) -> str:
        """Generate HTML dashboard"""
        dashboard = self.monitoring_system.get_health_dashboard()
        
        # Status color
        status_color = {
            "HEALTHY": "#28a745",
            "DEGRADED": "#ffc107", 
            "CRITICAL": "#dc3545"
        }.get(dashboard.get("system_status", "UNKNOWN"), "#6c757d")
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading System Monitoring Dashboard</title>
            <meta http-equiv="refresh" content="30">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                .header {{ background-color: {status_color}; color: white; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-card {{ background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
                .metric-value {{ font-size: 2em; font-weight: bold; color: #333; }}
                .metric-label {{ color: #666; font-size: 0.9em; }}
                .alert {{ background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                .timestamp {{ color: #666; font-size: 0.8em; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 Trading System Monitoring Dashboard</h1>
                    <h2>Status: {dashboard.get('system_status', 'UNKNOWN')}</h2>
                    <p>Health Score: {dashboard.get('health_score', 0):.1%}</p>
                    <p class="timestamp">Last Updated: {dashboard.get('timestamp', 'N/A')}</p>
                </div>
                
                <div class="metrics">
                    <div class="metric-card">
                        <div class="metric-value">{dashboard.get('system_metrics', {}).get('cpu_usage', 0):.1f}%</div>
                        <div class="metric-label">CPU Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{dashboard.get('system_metrics', {}).get('memory_usage', 0):.1f}%</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{dashboard.get('system_metrics', {}).get('error_rate', 0):.2%}</div>
                        <div class="metric-label">Error Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${dashboard.get('trading_metrics', {}).get('portfolio_value', 0):,.2f}</div>
                        <div class="metric-label">Portfolio Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{dashboard.get('trading_metrics', {}).get('win_rate', 0):.1%}</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                </div>
                
                <div class="alerts-section">
                    <h3>🚨 Active Alerts ({dashboard.get('active_alerts', 0)})</h3>
        """
        
        # Add alerts
        for alert in dashboard.get('recent_alerts', []):
            html += f"""
                    <div class="alert">
                        <strong>{alert.get('severity', 'N/A')}</strong>: {alert.get('message', 'N/A')}
                        <br><small>Component: {alert.get('component', 'N/A')} | {alert.get('timestamp', 'N/A')}</small>
                    </div>
            """
        
        html += """
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def save_dashboard(self, filename: str = "monitoring_dashboard.html"):
        """Save dashboard to HTML file"""
        try:
            html = self.generate_html_dashboard()
            with open(filename, 'w') as f:
                f.write(html)
            logger.info(f"✅ Dashboard saved: {filename}")
        except Exception as e:
            logger.error(f"❌ Error saving dashboard: {e}")


# =============================================================================
# MAIN EXECUTION
# =============================================================================

if __name__ == "__main__":
    # Initialize monitoring system
    monitoring = EnhancedMonitoringSystem()
    dashboard = MonitoringDashboard(monitoring)
    
    async def main():
        try:
            # Start monitoring
            await monitoring.start_monitoring(interval_seconds=30)
        except KeyboardInterrupt:
            logger.info("Monitoring interrupted by user")
        finally:
            monitoring.stop_monitoring()
    
    # Run monitoring
    asyncio.run(main()) 