"""
Advanced Database Pool Manager for Noryon V2
High-performance database connection pooling with automatic scaling, query caching, and health monitoring
"""

import asyncio
import logging
import time
import json
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from contextlib import asynccontextmanager
import weakref
import threading
from concurrent.futures import ThreadPoolExecutor

import asyncpg
import aioredis
import aioclickhouse
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text, pool
from sqlalchemy.pool import QueuePool

logger = logging.getLogger(__name__)

class DatabaseType(Enum):
    POSTGRES = "postgres"
    REDIS = "redis"
    CLICKHOUSE = "clickhouse"

class ConnectionStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILING = "failing"
    OFFLINE = "offline"

@dataclass
class ConnectionPoolConfig:
    """Configuration for database connection pools"""
    min_connections: int = 5
    max_connections: int = 50
    max_overflow: int = 20
    pool_recycle: int = 3600  # 1 hour
    pool_timeout: int = 30
    retry_on_disconnect: bool = True
    echo_queries: bool = False
    health_check_interval: int = 60  # seconds

@dataclass
class ConnectionMetrics:
    """Metrics for database connections"""
    pool_size: int
    checked_out: int
    overflow: int
    invalid: int
    total_queries: int
    avg_query_time: float
    error_rate: float
    last_health_check: datetime
    status: ConnectionStatus

@dataclass
class QueryCacheConfig:
    """Configuration for query caching"""
    enabled: bool = True
    default_ttl: int = 300  # 5 minutes
    max_cache_size: int = 10000
    cache_key_prefix: str = "query_cache"

class AdvancedDatabasePoolManager:
    """Advanced database connection pool manager with caching and monitoring"""
    
    def __init__(self, configs: Dict[DatabaseType, Dict[str, Any]]):
        self.configs = configs
        self.pools: Dict[DatabaseType, Any] = {}
        self.pool_configs: Dict[DatabaseType, ConnectionPoolConfig] = {}
        self.metrics: Dict[DatabaseType, ConnectionMetrics] = {}
        
        # Query caching
        self.cache_config = QueryCacheConfig()
        self.query_cache: Dict[str, Any] = {}
        self.cache_access_times: Dict[str, datetime] = {}
        
        # Redis client for distributed caching
        self.redis_cache: Optional[aioredis.Redis] = None
        
        # Health monitoring
        self.health_check_tasks: Dict[DatabaseType, asyncio.Task] = {}
        self.running = False
        
        # Performance tracking
        self.query_stats: Dict[str, List[float]] = {}
        self.connection_stats: Dict[DatabaseType, Dict[str, int]] = {}
        
        # Thread pool for blocking operations
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        
        logger.info("[INIT] Advanced Database Pool Manager initialized")
    
    async def initialize(self):
        """Initialize all database connection pools"""
        try:
            for db_type, config in self.configs.items():
                await self._initialize_pool(db_type, config)
            
            # Initialize Redis cache if available
            if DatabaseType.REDIS in self.configs:
                self.redis_cache = self.pools[DatabaseType.REDIS]
            
            # Start health monitoring
            await self._start_health_monitoring()
            
            self.running = True
            logger.info("✅ All database pools initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database pools: {e}")
            raise
    
    async def _initialize_pool(self, db_type: DatabaseType, config: Dict[str, Any]):
        """Initialize a specific database pool"""
        try:
            pool_config = ConnectionPoolConfig(**config.get('pool_config', {}))
            self.pool_configs[db_type] = pool_config
            
            if db_type == DatabaseType.POSTGRES:
                await self._initialize_postgres_pool(config, pool_config)
            elif db_type == DatabaseType.REDIS:
                await self._initialize_redis_pool(config, pool_config)
            elif db_type == DatabaseType.CLICKHOUSE:
                await self._initialize_clickhouse_pool(config, pool_config)
            
            # Initialize metrics
            self.metrics[db_type] = ConnectionMetrics(
                pool_size=0,
                checked_out=0,
                overflow=0,
                invalid=0,
                total_queries=0,
                avg_query_time=0.0,
                error_rate=0.0,
                last_health_check=datetime.now(timezone.utc),
                status=ConnectionStatus.HEALTHY
            )
            
            # Initialize connection stats
            self.connection_stats[db_type] = {
                "successful_connections": 0,
                "failed_connections": 0,
                "reconnections": 0,
                "timeouts": 0
            }
            
            logger.info(f"✅ {db_type.value} pool initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize {db_type.value} pool: {e}")
            raise
    
    async def _initialize_postgres_pool(self, config: Dict[str, Any], pool_config: ConnectionPoolConfig):
        """Initialize PostgreSQL connection pool"""
        database_url = config['url']
        
        # Create SQLAlchemy async engine with custom pool
        engine = create_async_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=pool_config.min_connections,
            max_overflow=pool_config.max_overflow,
            pool_recycle=pool_config.pool_recycle,
            pool_timeout=pool_config.pool_timeout,
            pool_pre_ping=True,  # Validate connections
            echo=pool_config.echo_queries,
            future=True
        )
        
        # Create session factory
        session_factory = sessionmaker(
            engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )
        
        self.pools[DatabaseType.POSTGRES] = {
            'engine': engine,
            'session_factory': session_factory
        }
        
        # Test connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
    
    async def _initialize_redis_pool(self, config: Dict[str, Any], pool_config: ConnectionPoolConfig):
        """Initialize Redis connection pool"""
        redis_url = config['url']
        
        # Create connection pool
        connection_pool = aioredis.ConnectionPool.from_url(
            redis_url,
            max_connections=pool_config.max_connections,
            retry_on_timeout=True,
            socket_timeout=pool_config.pool_timeout,
            socket_connect_timeout=10,
            health_check_interval=30
        )
        
        redis_client = aioredis.Redis(
            connection_pool=connection_pool,
            decode_responses=True
        )
        
        self.pools[DatabaseType.REDIS] = redis_client
        
        # Test connection
        await redis_client.ping()
    
    async def _initialize_clickhouse_pool(self, config: Dict[str, Any], pool_config: ConnectionPoolConfig):
        """Initialize ClickHouse connection pool"""
        clickhouse_url = config['url']
        
        # Create ClickHouse client with connection pooling
        client = aioclickhouse.create_client(
            clickhouse_url,
            pool_size=pool_config.max_connections,
            timeout=pool_config.pool_timeout
        )
        
        self.pools[DatabaseType.CLICKHOUSE] = client
        
        # Test connection
        await client.execute("SELECT 1")
    
    @asynccontextmanager
    async def get_connection(self, db_type: DatabaseType):
        """Get a database connection with automatic cleanup"""
        start_time = time.time()
        connection = None
        
        try:
            if db_type == DatabaseType.POSTGRES:
                async with self.pools[db_type]['session_factory']() as session:
                    connection = session
                    yield session
            elif db_type == DatabaseType.REDIS:
                connection = self.pools[db_type]
                yield connection
            elif db_type == DatabaseType.CLICKHOUSE:
                connection = self.pools[db_type]
                yield connection
            else:
                raise ValueError(f"Unsupported database type: {db_type}")
            
            # Update success metrics
            self.connection_stats[db_type]["successful_connections"] += 1
            
        except asyncio.TimeoutError:
            self.connection_stats[db_type]["timeouts"] += 1
            logger.error(f"❌ Connection timeout for {db_type.value}")
            raise
        except Exception as e:
            self.connection_stats[db_type]["failed_connections"] += 1
            logger.error(f"❌ Connection error for {db_type.value}: {e}")
            raise
        finally:
            # Update connection metrics
            connection_time = time.time() - start_time
            await self._update_connection_metrics(db_type, connection_time)
    
    async def execute_query(self, db_type: DatabaseType, query: str, 
                          params: Optional[Dict[str, Any]] = None,
                          use_cache: bool = True,
                          cache_ttl: Optional[int] = None) -> Any:
        """Execute a database query with caching"""
        start_time = time.time()
        cache_key = None
        
        try:
            # Generate cache key for SELECT queries
            if use_cache and self.cache_config.enabled and query.strip().upper().startswith('SELECT'):
                cache_key = self._generate_cache_key(db_type, query, params)
                
                # Check cache first
                cached_result = await self._get_cached_result(cache_key)
                if cached_result is not None:
                    logger.debug(f"🎯 Cache hit for query: {query[:50]}...")
                    return cached_result
            
            # Execute query
            async with self.get_connection(db_type) as conn:
                if db_type == DatabaseType.POSTGRES:
                    if params:
                        result = await conn.execute(text(query), params)
                    else:
                        result = await conn.execute(text(query))
                    
                    # Fetch results for SELECT queries
                    if query.strip().upper().startswith('SELECT'):
                        rows = result.fetchall()
                        result_data = [dict(row._mapping) for row in rows]
                    else:
                        result_data = result.rowcount
                        
                elif db_type == DatabaseType.CLICKHOUSE:
                    if params:
                        result_data = await conn.execute(query, params)
                    else:
                        result_data = await conn.execute(query)
                        
                elif db_type == DatabaseType.REDIS:
                    # Redis operations are different - this is a simplified example
                    if query.upper().startswith('GET'):
                        key = params.get('key') if params else query.split()[1]
                        result_data = await conn.get(key)
                    elif query.upper().startswith('SET'):
                        key = params.get('key') if params else query.split()[1]
                        value = params.get('value') if params else query.split()[2]
                        result_data = await conn.set(key, value)
                    else:
                        raise ValueError(f"Unsupported Redis operation: {query}")
                
                # Cache result if applicable
                if cache_key and query.strip().upper().startswith('SELECT'):
                    ttl = cache_ttl or self.cache_config.default_ttl
                    await self._cache_result(cache_key, result_data, ttl)
                
                return result_data
            
        except Exception as e:
            logger.error(f"❌ Query execution failed for {db_type.value}: {e}")
            raise
        finally:
            # Update query metrics
            query_time = time.time() - start_time
            await self._update_query_metrics(db_type, query, query_time)
    
    async def execute_batch(self, db_type: DatabaseType, queries: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple queries in a batch for better performance"""
        start_time = time.time()
        results = []
        
        try:
            async with self.get_connection(db_type) as conn:
                if db_type == DatabaseType.POSTGRES:
                    async with conn.begin():
                        for query_data in queries:
                            query = query_data['query']
                            params = query_data.get('params')
                            
                            if params:
                                result = await conn.execute(text(query), params)
                            else:
                                result = await conn.execute(text(query))
                            
                            if query.strip().upper().startswith('SELECT'):
                                rows = result.fetchall()
                                result_data = [dict(row._mapping) for row in rows]
                            else:
                                result_data = result.rowcount
                            
                            results.append(result_data)
                
                elif db_type == DatabaseType.CLICKHOUSE:
                    # ClickHouse batch processing
                    for query_data in queries:
                        query = query_data['query']
                        params = query_data.get('params')
                        
                        if params:
                            result = await conn.execute(query, params)
                        else:
                            result = await conn.execute(query)
                        
                        results.append(result)
                
                elif db_type == DatabaseType.REDIS:
                    # Redis pipeline for batch operations
                    pipe = conn.pipeline()
                    
                    for query_data in queries:
                        query = query_data['query']
                        params = query_data.get('params', {})
                        
                        if query.upper().startswith('GET'):
                            key = params.get('key', query.split()[1])
                            pipe.get(key)
                        elif query.upper().startswith('SET'):
                            key = params.get('key', query.split()[1])
                            value = params.get('value', query.split()[2])
                            pipe.set(key, value)
                    
                    results = await pipe.execute()
            
            batch_time = time.time() - start_time
            logger.info(f"📦 Executed batch of {len(queries)} queries for {db_type.value} in {batch_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Batch execution failed for {db_type.value}: {e}")
            raise
    
    def _generate_cache_key(self, db_type: DatabaseType, query: str, params: Optional[Dict[str, Any]]) -> str:
        """Generate a cache key for the query"""
        key_data = f"{db_type.value}:{query}"
        if params:
            key_data += f":{json.dumps(params, sort_keys=True)}"
        
        return f"{self.cache_config.cache_key_prefix}:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    async def _get_cached_result(self, cache_key: str) -> Any:
        """Get result from cache"""
        try:
            # Try local cache first
            if cache_key in self.query_cache:
                self.cache_access_times[cache_key] = datetime.now(timezone.utc)
                return self.query_cache[cache_key]
            
            # Try Redis cache
            if self.redis_cache:
                cached_data = await self.redis_cache.get(cache_key)
                if cached_data:
                    result = json.loads(cached_data)
                    # Store in local cache for faster access
                    self.query_cache[cache_key] = result
                    self.cache_access_times[cache_key] = datetime.now(timezone.utc)
                    return result
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Cache retrieval error: {e}")
            return None
    
    async def _cache_result(self, cache_key: str, result: Any, ttl: int):
        """Cache query result"""
        try:
            # Local cache
            self.query_cache[cache_key] = result
            self.cache_access_times[cache_key] = datetime.now(timezone.utc)
            
            # Manage local cache size
            if len(self.query_cache) > self.cache_config.max_cache_size:
                await self._cleanup_local_cache()
            
            # Redis cache
            if self.redis_cache:
                await self.redis_cache.setex(
                    cache_key,
                    ttl,
                    json.dumps(result, default=str)
                )
            
        except Exception as e:
            logger.error(f"❌ Cache storage error: {e}")
    
    async def _cleanup_local_cache(self):
        """Clean up local cache by removing least recently used items"""
        try:
            # Remove 25% of least recently used items
            items_to_remove = len(self.query_cache) // 4
            
            # Sort by access time
            sorted_items = sorted(
                self.cache_access_times.items(),
                key=lambda x: x[1]
            )
            
            for cache_key, _ in sorted_items[:items_to_remove]:
                self.query_cache.pop(cache_key, None)
                self.cache_access_times.pop(cache_key, None)
            
            logger.debug(f"🧹 Cleaned up {items_to_remove} cache items")
            
        except Exception as e:
            logger.error(f"❌ Cache cleanup error: {e}")
    
    async def _update_connection_metrics(self, db_type: DatabaseType, connection_time: float):
        """Update connection performance metrics"""
        try:
            if db_type not in self.metrics:
                return
            
            metrics = self.metrics[db_type]
            
            # Update pool status if available
            if db_type == DatabaseType.POSTGRES:
                pool = self.pools[db_type]['engine'].pool
                metrics.pool_size = pool.size()
                metrics.checked_out = pool.checkedout()
                metrics.overflow = pool.overflow()
                metrics.invalid = pool.invalid()
            
        except Exception as e:
            logger.error(f"❌ Failed to update connection metrics: {e}")
    
    async def _update_query_metrics(self, db_type: DatabaseType, query: str, query_time: float):
        """Update query performance metrics"""
        try:
            if db_type not in self.metrics:
                return
            
            metrics = self.metrics[db_type]
            metrics.total_queries += 1
            
            # Update running average of query time
            if metrics.avg_query_time == 0:
                metrics.avg_query_time = query_time
            else:
                metrics.avg_query_time = (metrics.avg_query_time * 0.9) + (query_time * 0.1)
            
            # Track query performance by type
            query_type = query.strip().split()[0].upper()
            if query_type not in self.query_stats:
                self.query_stats[query_type] = []
            
            self.query_stats[query_type].append(query_time)
            
            # Keep only last 100 measurements
            if len(self.query_stats[query_type]) > 100:
                self.query_stats[query_type] = self.query_stats[query_type][-100:]
            
        except Exception as e:
            logger.error(f"❌ Failed to update query metrics: {e}")
    
    async def _start_health_monitoring(self):
        """Start health monitoring for all database pools"""
        for db_type in self.pools.keys():
            task = asyncio.create_task(self._health_monitor(db_type))
            self.health_check_tasks[db_type] = task
        
        logger.info("🏥 Health monitoring started for all database pools")
    
    async def _health_monitor(self, db_type: DatabaseType):
        """Monitor health of a specific database pool"""
        while self.running:
            try:
                start_time = time.time()
                
                # Perform health check
                if db_type == DatabaseType.POSTGRES:
                    async with self.get_connection(db_type) as conn:
                        await conn.execute(text("SELECT 1"))
                elif db_type == DatabaseType.REDIS:
                    await self.pools[db_type].ping()
                elif db_type == DatabaseType.CLICKHOUSE:
                    await self.pools[db_type].execute("SELECT 1")
                
                health_check_time = time.time() - start_time
                
                # Update health status
                if health_check_time < 1.0:
                    self.metrics[db_type].status = ConnectionStatus.HEALTHY
                elif health_check_time < 5.0:
                    self.metrics[db_type].status = ConnectionStatus.DEGRADED
                else:
                    self.metrics[db_type].status = ConnectionStatus.FAILING
                
                self.metrics[db_type].last_health_check = datetime.now(timezone.utc)
                
                # Log degraded performance
                if health_check_time > 1.0:
                    logger.warning(f"⚠️ {db_type.value} health check took {health_check_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Health check failed for {db_type.value}: {e}")
                self.metrics[db_type].status = ConnectionStatus.OFFLINE
            
            # Wait for next health check
            interval = self.pool_configs[db_type].health_check_interval
            await asyncio.sleep(interval)
    
    async def get_pool_metrics(self) -> Dict[str, Any]:
        """Get comprehensive metrics for all database pools"""
        try:
            pool_metrics = {}
            
            for db_type, metrics in self.metrics.items():
                pool_metrics[db_type.value] = {
                    "connection_metrics": asdict(metrics),
                    "connection_stats": self.connection_stats.get(db_type, {}),
                    "query_stats": {}
                }
                
                # Add query statistics
                for query_type, times in self.query_stats.items():
                    if times:
                        pool_metrics[db_type.value]["query_stats"][query_type] = {
                            "count": len(times),
                            "avg_time": sum(times) / len(times),
                            "min_time": min(times),
                            "max_time": max(times)
                        }
            
            # Add cache metrics
            pool_metrics["cache"] = {
                "local_cache_size": len(self.query_cache),
                "cache_hit_rate": 0,  # Would need to track hits/misses
                "max_cache_size": self.cache_config.max_cache_size,
                "cache_enabled": self.cache_config.enabled
            }
            
            return pool_metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to get pool metrics: {e}")
            return {}
    
    async def clear_cache(self, pattern: Optional[str] = None):
        """Clear query cache"""
        try:
            if pattern:
                # Clear specific pattern
                keys_to_remove = [k for k in self.query_cache.keys() if pattern in k]
                for key in keys_to_remove:
                    self.query_cache.pop(key, None)
                    self.cache_access_times.pop(key, None)
                
                # Clear from Redis cache
                if self.redis_cache:
                    redis_keys = await self.redis_cache.keys(f"*{pattern}*")
                    if redis_keys:
                        await self.redis_cache.delete(*redis_keys)
                
                logger.info(f"🧹 Cleared {len(keys_to_remove)} cache entries matching pattern: {pattern}")
            else:
                # Clear all cache
                self.query_cache.clear()
                self.cache_access_times.clear()
                
                if self.redis_cache:
                    await self.redis_cache.flushall()
                
                logger.info("🧹 Cleared all query cache")
            
        except Exception as e:
            logger.error(f"❌ Failed to clear cache: {e}")
    
    async def shutdown(self):
        """Shutdown all database pools and cleanup resources"""
        try:
            self.running = False
            
            # Cancel health monitoring tasks
            for task in self.health_check_tasks.values():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Close database connections
            for db_type, pool in self.pools.items():
                if db_type == DatabaseType.POSTGRES:
                    await pool['engine'].dispose()
                elif db_type == DatabaseType.REDIS:
                    await pool.close()
                elif db_type == DatabaseType.CLICKHOUSE:
                    await pool.close()
            
            # Shutdown thread pool
            self.thread_pool.shutdown(wait=True)
            
            logger.info("✅ All database pools shutdown successfully")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")

# Factory function
async def create_database_pool_manager(configs: Dict[DatabaseType, Dict[str, Any]]) -> AdvancedDatabasePoolManager:
    """Factory function to create and initialize database pool manager"""
    manager = AdvancedDatabasePoolManager(configs)
    await manager.initialize()
    return manager

# Example usage
if __name__ == "__main__":
    async def main():
        # Example configuration
        configs = {
            DatabaseType.POSTGRES: {
                'url': 'postgresql+asyncpg://user:pass@localhost/db',
                'pool_config': {
                    'min_connections': 10,
                    'max_connections': 100,
                    'max_overflow': 50
                }
            },
            DatabaseType.REDIS: {
                'url': 'redis://localhost:6379',
                'pool_config': {
                    'max_connections': 50
                }
            }
        }
        
        # Create pool manager
        pool_manager = await create_database_pool_manager(configs)
        
        # Execute some queries
        result = await pool_manager.execute_query(
            DatabaseType.POSTGRES,
            "SELECT COUNT(*) as count FROM users",
            use_cache=True
        )
        
        print(f"Query result: {result}")
        
        # Get metrics
        metrics = await pool_manager.get_pool_metrics()
        print(f"Pool metrics: {metrics}")
        
        # Shutdown
        await pool_manager.shutdown()
    
    asyncio.run(main()) 