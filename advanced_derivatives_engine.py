#!/usr/bin/env python3
"""
Advanced Options & Derivatives Engine
Professional-grade options pricing, Greeks calculation, and derivatives risk management
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from scipy.stats import norm
import math

logger = logging.getLogger("AdvancedDerivativesEngine")


class OptionType(Enum):
    """Option types."""
    CALL = "call"
    PUT = "put"


class DerivativeType(Enum):
    """Derivative types."""
    VANILLA_OPTION = "vanilla_option"
    BINARY_OPTION = "binary_option"
    BARRIER_OPTION = "barrier_option"
    ASIAN_OPTION = "asian_option"
    FUTURES = "futures"
    PERPETUAL_SWAP = "perpetual_swap"


@dataclass
class OptionContract:
    """Option contract specification."""
    symbol: str
    option_type: OptionType
    strike: float
    expiry: datetime
    underlying_price: float
    risk_free_rate: float
    volatility: float
    dividend_yield: float = 0.0


@dataclass
class OptionGreeks:
    """Option Greeks."""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    lambda_: float  # Option elasticity


@dataclass
class OptionPricing:
    """Option pricing results."""
    theoretical_price: float
    intrinsic_value: float
    time_value: float
    implied_volatility: float
    greeks: OptionGreeks
    moneyness: float
    time_to_expiry: float


class AdvancedDerivativesEngine:
    """Advanced options and derivatives pricing engine."""
    
    def __init__(self):
        self.option_chains = {}
        self.volatility_surface = {}
        self.pricing_history = {}
        
        # Risk-free rates by currency
        self.risk_free_rates = {
            "USD": 0.05,  # 5% USD risk-free rate
            "EUR": 0.03,  # 3% EUR risk-free rate
            "BTC": 0.0,   # 0% for crypto
            "ETH": 0.0    # 0% for crypto
        }
        
        # Volatility models
        self.volatility_models = {
            "historical": self._calculate_historical_volatility,
            "garch": self._calculate_garch_volatility,
            "ewma": self._calculate_ewma_volatility
        }
    
    def price_vanilla_option(self, contract: OptionContract, 
                           model: str = "black_scholes") -> OptionPricing:
        """Price vanilla option using specified model."""
        try:
            if model == "black_scholes":
                return self._black_scholes_pricing(contract)
            elif model == "binomial":
                return self._binomial_pricing(contract)
            elif model == "monte_carlo":
                return self._monte_carlo_pricing(contract)
            else:
                return self._black_scholes_pricing(contract)  # Default
                
        except Exception as e:
            logger.error(f"Option pricing error: {e}")
            return self._default_option_pricing(contract)
    
    def _black_scholes_pricing(self, contract: OptionContract) -> OptionPricing:
        """Black-Scholes option pricing."""
        S = contract.underlying_price
        K = contract.strike
        T = self._time_to_expiry(contract.expiry)
        r = contract.risk_free_rate
        sigma = contract.volatility
        q = contract.dividend_yield
        
        # Handle edge cases
        if T <= 0:
            return self._expired_option_pricing(contract)
        
        if sigma <= 0:
            sigma = 0.01  # Minimum volatility
        
        # Black-Scholes formula
        d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        if contract.option_type == OptionType.CALL:
            theoretical_price = (S * np.exp(-q * T) * norm.cdf(d1) - 
                               K * np.exp(-r * T) * norm.cdf(d2))
            intrinsic_value = max(S - K, 0)
        else:  # PUT
            theoretical_price = (K * np.exp(-r * T) * norm.cdf(-d2) - 
                               S * np.exp(-q * T) * norm.cdf(-d1))
            intrinsic_value = max(K - S, 0)
        
        time_value = theoretical_price - intrinsic_value
        
        # Calculate Greeks
        greeks = self._calculate_greeks(contract, d1, d2)
        
        # Calculate additional metrics
        moneyness = S / K
        implied_vol = sigma  # For theoretical pricing, IV = input volatility
        
        return OptionPricing(
            theoretical_price=round(theoretical_price, 6),
            intrinsic_value=round(intrinsic_value, 6),
            time_value=round(time_value, 6),
            implied_volatility=round(implied_vol, 4),
            greeks=greeks,
            moneyness=round(moneyness, 4),
            time_to_expiry=round(T, 6)
        )
    
    def _calculate_greeks(self, contract: OptionContract, d1: float, d2: float) -> OptionGreeks:
        """Calculate option Greeks."""
        S = contract.underlying_price
        K = contract.strike
        T = self._time_to_expiry(contract.expiry)
        r = contract.risk_free_rate
        sigma = contract.volatility
        q = contract.dividend_yield
        
        # Delta
        if contract.option_type == OptionType.CALL:
            delta = np.exp(-q * T) * norm.cdf(d1)
        else:
            delta = -np.exp(-q * T) * norm.cdf(-d1)
        
        # Gamma
        gamma = (np.exp(-q * T) * norm.pdf(d1)) / (S * sigma * np.sqrt(T))
        
        # Theta
        theta_term1 = -(S * norm.pdf(d1) * sigma * np.exp(-q * T)) / (2 * np.sqrt(T))
        if contract.option_type == OptionType.CALL:
            theta_term2 = r * K * np.exp(-r * T) * norm.cdf(d2)
            theta_term3 = -q * S * np.exp(-q * T) * norm.cdf(d1)
            theta = theta_term1 - theta_term2 + theta_term3
        else:
            theta_term2 = -r * K * np.exp(-r * T) * norm.cdf(-d2)
            theta_term3 = q * S * np.exp(-q * T) * norm.cdf(-d1)
            theta = theta_term1 + theta_term2 + theta_term3
        
        theta = theta / 365  # Convert to daily theta
        
        # Vega
        vega = S * np.exp(-q * T) * norm.pdf(d1) * np.sqrt(T) / 100  # Per 1% vol change
        
        # Rho
        if contract.option_type == OptionType.CALL:
            rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100  # Per 1% rate change
        else:
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        # Lambda (elasticity)
        option_price = self._black_scholes_pricing(contract).theoretical_price
        if option_price > 0:
            lambda_ = delta * S / option_price
        else:
            lambda_ = 0
        
        return OptionGreeks(
            delta=round(delta, 6),
            gamma=round(gamma, 6),
            theta=round(theta, 6),
            vega=round(vega, 6),
            rho=round(rho, 6),
            lambda_=round(lambda_, 6)
        )
    
    def _binomial_pricing(self, contract: OptionContract, steps: int = 100) -> OptionPricing:
        """Binomial tree option pricing."""
        S = contract.underlying_price
        K = contract.strike
        T = self._time_to_expiry(contract.expiry)
        r = contract.risk_free_rate
        sigma = contract.volatility
        q = contract.dividend_yield
        
        if T <= 0:
            return self._expired_option_pricing(contract)
        
        # Binomial parameters
        dt = T / steps
        u = np.exp(sigma * np.sqrt(dt))  # Up factor
        d = 1 / u  # Down factor
        p = (np.exp((r - q) * dt) - d) / (u - d)  # Risk-neutral probability
        
        # Initialize asset prices at maturity
        asset_prices = np.zeros(steps + 1)
        for i in range(steps + 1):
            asset_prices[i] = S * (u ** (steps - i)) * (d ** i)
        
        # Initialize option values at maturity
        option_values = np.zeros(steps + 1)
        for i in range(steps + 1):
            if contract.option_type == OptionType.CALL:
                option_values[i] = max(asset_prices[i] - K, 0)
            else:
                option_values[i] = max(K - asset_prices[i], 0)
        
        # Backward induction
        for j in range(steps - 1, -1, -1):
            for i in range(j + 1):
                option_values[i] = np.exp(-r * dt) * (p * option_values[i] + (1 - p) * option_values[i + 1])
        
        theoretical_price = option_values[0]
        
        # Calculate intrinsic value and time value
        if contract.option_type == OptionType.CALL:
            intrinsic_value = max(S - K, 0)
        else:
            intrinsic_value = max(K - S, 0)
        
        time_value = theoretical_price - intrinsic_value
        
        # For binomial, we'll use Black-Scholes Greeks as approximation
        d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        greeks = self._calculate_greeks(contract, d1, d2)
        
        return OptionPricing(
            theoretical_price=round(theoretical_price, 6),
            intrinsic_value=round(intrinsic_value, 6),
            time_value=round(time_value, 6),
            implied_volatility=round(sigma, 4),
            greeks=greeks,
            moneyness=round(S / K, 4),
            time_to_expiry=round(T, 6)
        )
    
    def _monte_carlo_pricing(self, contract: OptionContract, simulations: int = 100000) -> OptionPricing:
        """Monte Carlo option pricing."""
        S = contract.underlying_price
        K = contract.strike
        T = self._time_to_expiry(contract.expiry)
        r = contract.risk_free_rate
        sigma = contract.volatility
        q = contract.dividend_yield
        
        if T <= 0:
            return self._expired_option_pricing(contract)
        
        # Generate random paths
        np.random.seed(42)  # For reproducibility
        Z = np.random.standard_normal(simulations)
        
        # Final stock prices
        ST = S * np.exp((r - q - 0.5 * sigma**2) * T + sigma * np.sqrt(T) * Z)
        
        # Option payoffs
        if contract.option_type == OptionType.CALL:
            payoffs = np.maximum(ST - K, 0)
        else:
            payoffs = np.maximum(K - ST, 0)
        
        # Discounted expected payoff
        theoretical_price = np.exp(-r * T) * np.mean(payoffs)
        
        # Calculate intrinsic value and time value
        if contract.option_type == OptionType.CALL:
            intrinsic_value = max(S - K, 0)
        else:
            intrinsic_value = max(K - S, 0)
        
        time_value = theoretical_price - intrinsic_value
        
        # Use Black-Scholes Greeks as approximation
        d1 = (np.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        greeks = self._calculate_greeks(contract, d1, d2)
        
        return OptionPricing(
            theoretical_price=round(theoretical_price, 6),
            intrinsic_value=round(intrinsic_value, 6),
            time_value=round(time_value, 6),
            implied_volatility=round(sigma, 4),
            greeks=greeks,
            moneyness=round(S / K, 4),
            time_to_expiry=round(T, 6)
        )
    
    def calculate_implied_volatility(self, market_price: float, contract: OptionContract, 
                                   tolerance: float = 1e-6, max_iterations: int = 100) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        try:
            # Initial guess
            vol = 0.2  # 20% initial volatility guess
            
            for i in range(max_iterations):
                # Create contract with current volatility guess
                test_contract = OptionContract(
                    symbol=contract.symbol,
                    option_type=contract.option_type,
                    strike=contract.strike,
                    expiry=contract.expiry,
                    underlying_price=contract.underlying_price,
                    risk_free_rate=contract.risk_free_rate,
                    volatility=vol,
                    dividend_yield=contract.dividend_yield
                )
                
                # Calculate theoretical price and vega
                pricing = self._black_scholes_pricing(test_contract)
                theoretical_price = pricing.theoretical_price
                vega = pricing.greeks.vega * 100  # Convert back to per unit vol change
                
                # Newton-Raphson update
                price_diff = theoretical_price - market_price
                
                if abs(price_diff) < tolerance:
                    return vol
                
                if vega == 0:
                    break
                
                vol = vol - price_diff / vega
                
                # Keep volatility positive
                vol = max(vol, 0.001)
                vol = min(vol, 5.0)  # Cap at 500%
            
            return vol
            
        except Exception as e:
            logger.error(f"Implied volatility calculation error: {e}")
            return 0.2  # Default 20% volatility
    
    def _time_to_expiry(self, expiry: datetime) -> float:
        """Calculate time to expiry in years."""
        now = datetime.now(timezone.utc)
        if expiry.tzinfo is None:
            expiry = expiry.replace(tzinfo=timezone.utc)
        
        time_diff = expiry - now
        return max(time_diff.total_seconds() / (365.25 * 24 * 3600), 0)
    
    def _expired_option_pricing(self, contract: OptionContract) -> OptionPricing:
        """Pricing for expired options."""
        S = contract.underlying_price
        K = contract.strike
        
        if contract.option_type == OptionType.CALL:
            intrinsic_value = max(S - K, 0)
        else:
            intrinsic_value = max(K - S, 0)
        
        # Expired options have no time value and zero Greeks
        zero_greeks = OptionGreeks(0, 0, 0, 0, 0, 0)
        
        return OptionPricing(
            theoretical_price=intrinsic_value,
            intrinsic_value=intrinsic_value,
            time_value=0.0,
            implied_volatility=0.0,
            greeks=zero_greeks,
            moneyness=round(S / K, 4),
            time_to_expiry=0.0
        )
    
    def _default_option_pricing(self, contract: OptionContract) -> OptionPricing:
        """Default option pricing for error cases."""
        zero_greeks = OptionGreeks(0, 0, 0, 0, 0, 0)
        
        return OptionPricing(
            theoretical_price=0.0,
            intrinsic_value=0.0,
            time_value=0.0,
            implied_volatility=0.0,
            greeks=zero_greeks,
            moneyness=1.0,
            time_to_expiry=0.0
        )
    
    def _calculate_historical_volatility(self, price_data: List[float], window: int = 30) -> float:
        """Calculate historical volatility."""
        if len(price_data) < 2:
            return 0.2  # Default 20%
        
        returns = []
        for i in range(1, len(price_data)):
            ret = np.log(price_data[i] / price_data[i-1])
            returns.append(ret)
        
        if len(returns) < window:
            window = len(returns)
        
        recent_returns = returns[-window:]
        volatility = np.std(recent_returns) * np.sqrt(365)  # Annualized
        
        return volatility
    
    def _calculate_garch_volatility(self, price_data: List[float]) -> float:
        """Simplified GARCH volatility calculation."""
        # Simplified GARCH(1,1) - would need more sophisticated implementation
        historical_vol = self._calculate_historical_volatility(price_data)
        return historical_vol * 1.1  # Slightly higher than historical
    
    def _calculate_ewma_volatility(self, price_data: List[float], lambda_param: float = 0.94) -> float:
        """EWMA volatility calculation."""
        if len(price_data) < 2:
            return 0.2
        
        returns = []
        for i in range(1, len(price_data)):
            ret = np.log(price_data[i] / price_data[i-1])
            returns.append(ret)
        
        if not returns:
            return 0.2
        
        # EWMA calculation
        variance = returns[0] ** 2  # Initial variance
        
        for ret in returns[1:]:
            variance = lambda_param * variance + (1 - lambda_param) * ret ** 2
        
        volatility = np.sqrt(variance * 365)  # Annualized
        return volatility
    
    def create_option_chain(self, symbol: str, underlying_price: float, 
                          expiry_dates: List[datetime], strike_range: Tuple[float, float],
                          strike_step: float = None) -> Dict[str, Any]:
        """Create option chain for given parameters."""
        try:
            if strike_step is None:
                strike_step = underlying_price * 0.05  # 5% steps
            
            # Generate strike prices
            min_strike, max_strike = strike_range
            strikes = []
            current_strike = min_strike
            while current_strike <= max_strike:
                strikes.append(current_strike)
                current_strike += strike_step
            
            option_chain = {
                "symbol": symbol,
                "underlying_price": underlying_price,
                "generated_at": datetime.now(timezone.utc),
                "expiries": {}
            }
            
            # Generate options for each expiry
            for expiry in expiry_dates:
                expiry_key = expiry.strftime("%Y-%m-%d")
                option_chain["expiries"][expiry_key] = {
                    "calls": {},
                    "puts": {}
                }
                
                for strike in strikes:
                    # Create call option
                    call_contract = OptionContract(
                        symbol=f"{symbol}-C-{strike}-{expiry_key}",
                        option_type=OptionType.CALL,
                        strike=strike,
                        expiry=expiry,
                        underlying_price=underlying_price,
                        risk_free_rate=self.risk_free_rates.get("USD", 0.05),
                        volatility=0.25  # Default 25% volatility
                    )
                    
                    call_pricing = self.price_vanilla_option(call_contract)
                    option_chain["expiries"][expiry_key]["calls"][strike] = {
                        "contract": call_contract,
                        "pricing": call_pricing
                    }
                    
                    # Create put option
                    put_contract = OptionContract(
                        symbol=f"{symbol}-P-{strike}-{expiry_key}",
                        option_type=OptionType.PUT,
                        strike=strike,
                        expiry=expiry,
                        underlying_price=underlying_price,
                        risk_free_rate=self.risk_free_rates.get("USD", 0.05),
                        volatility=0.25  # Default 25% volatility
                    )
                    
                    put_pricing = self.price_vanilla_option(put_contract)
                    option_chain["expiries"][expiry_key]["puts"][strike] = {
                        "contract": put_contract,
                        "pricing": put_pricing
                    }
            
            # Store option chain
            self.option_chains[symbol] = option_chain
            
            logger.info(f"✅ Option chain created for {symbol}: {len(expiry_dates)} expiries, {len(strikes)} strikes")
            
            return option_chain
            
        except Exception as e:
            logger.error(f"Option chain creation error: {e}")
            return {"error": str(e)}
    
    def get_option_analytics(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive option analytics."""
        if symbol not in self.option_chains:
            return {"error": "No option chain available"}
        
        chain = self.option_chains[symbol]
        analytics = {
            "symbol": symbol,
            "underlying_price": chain["underlying_price"],
            "total_expiries": len(chain["expiries"]),
            "analytics_by_expiry": {}
        }
        
        for expiry_key, expiry_data in chain["expiries"].items():
            calls = expiry_data["calls"]
            puts = expiry_data["puts"]
            
            # Calculate analytics for this expiry
            call_volumes = [pricing["pricing"].theoretical_price for pricing in calls.values()]
            put_volumes = [pricing["pricing"].theoretical_price for pricing in puts.values()]
            
            total_call_oi = sum(call_volumes)
            total_put_oi = sum(put_volumes)
            put_call_ratio = total_put_oi / total_call_oi if total_call_oi > 0 else 0
            
            analytics["analytics_by_expiry"][expiry_key] = {
                "total_call_volume": round(total_call_oi, 2),
                "total_put_volume": round(total_put_oi, 2),
                "put_call_ratio": round(put_call_ratio, 4),
                "strikes_count": len(calls)
            }
        
        return analytics


# Test the Advanced Derivatives Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("📊 Testing Advanced Derivatives Engine")
    print("=" * 70)
    
    # Initialize derivatives engine
    derivatives_engine = AdvancedDerivativesEngine()
    
    # Create sample option contract
    expiry = datetime.now(timezone.utc) + timedelta(days=30)  # 30 days to expiry
    
    option_contract = OptionContract(
        symbol="BTC-45000-C-30D",
        option_type=OptionType.CALL,
        strike=45000,
        expiry=expiry,
        underlying_price=44000,  # $1000 out of the money
        risk_free_rate=0.05,
        volatility=0.80  # 80% volatility (typical for crypto)
    )
    
    # Test different pricing models
    print("💰 Testing option pricing models...")
    
    models = ["black_scholes", "binomial", "monte_carlo"]
    for model in models:
        pricing = derivatives_engine.price_vanilla_option(option_contract, model)
        print(f"✅ {model.title()}: ${pricing.theoretical_price:.2f}")
        print(f"   Delta: {pricing.greeks.delta:.4f}, Gamma: {pricing.greeks.gamma:.6f}")
        print(f"   Theta: ${pricing.greeks.theta:.2f}/day, Vega: ${pricing.greeks.vega:.2f}")
    
    # Test implied volatility calculation
    print("\n📈 Testing implied volatility...")
    market_price = 2500  # Assume market price
    implied_vol = derivatives_engine.calculate_implied_volatility(market_price, option_contract)
    print(f"✅ Implied Volatility: {implied_vol:.1%}")
    
    # Test option chain creation
    print("\n📋 Testing option chain creation...")
    expiry_dates = [
        datetime.now(timezone.utc) + timedelta(days=7),   # 1 week
        datetime.now(timezone.utc) + timedelta(days=30),  # 1 month
        datetime.now(timezone.utc) + timedelta(days=90)   # 3 months
    ]
    
    option_chain = derivatives_engine.create_option_chain(
        symbol="BTCUSDT",
        underlying_price=44000,
        expiry_dates=expiry_dates,
        strike_range=(40000, 48000),
        strike_step=1000
    )
    
    if "error" not in option_chain:
        print(f"✅ Option chain created: {option_chain['total_expiries']} expiries")
        
        # Get option analytics
        analytics = derivatives_engine.get_option_analytics("BTCUSDT")
        print(f"✅ Option analytics generated for {analytics.get('total_expiries', 0)} expiries")
    
    print("\n✅ Advanced Derivatives Engine test completed!")
