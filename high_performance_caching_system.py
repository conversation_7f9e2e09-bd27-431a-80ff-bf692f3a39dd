"""
High-Performance Multi-Level Caching System for Noryon V2
Advanced caching with Redis, in-memory cache, cache warming, and intelligent eviction
"""

import asyncio
import logging
import time
import json
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import pickle
import zlib
from concurrent.futures import ThreadPoolExecutor
from collections import OrderedDict
import weakref

import aioredis

logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    L1_MEMORY = "l1_memory"      # Fastest - In-memory cache
    L2_REDIS = "l2_redis"        # Fast - Redis cache
    L3_PERSISTENT = "l3_persistent"  # Slower - Database/file cache

class CacheStrategy(Enum):
    LRU = "lru"                  # Least Recently Used
    LFU = "lfu"                  # Least Frequently Used
    TTL = "ttl"                  # Time To Live
    ADAPTIVE = "adaptive"         # Adaptive based on access patterns

@dataclass
class CacheConfig:
    """Configuration for caching system"""
    l1_max_size: int = 10000
    l1_max_memory_mb: int = 512
    l2_max_size: int = 100000
    l2_default_ttl: int = 3600  # 1 hour
    l3_default_ttl: int = 86400  # 24 hours
    compression_threshold: int = 1024  # Compress items > 1KB
    cache_warming_enabled: bool = True
    cache_warming_interval: int = 300  # 5 minutes
    eviction_strategy: CacheStrategy = CacheStrategy.ADAPTIVE

@dataclass
class CacheItem:
    """Cache item with metadata"""
    key: str
    value: Any
    created_at: datetime
    accessed_at: datetime
    access_count: int = 0
    ttl: Optional[int] = None
    size_bytes: int = 0
    compression_used: bool = False
    
    def is_expired(self) -> bool:
        if self.ttl is None:
            return False
        return (datetime.now(timezone.utc) - self.created_at).seconds > self.ttl

@dataclass
class CacheMetrics:
    """Cache performance metrics"""
    l1_hits: int = 0
    l1_misses: int = 0
    l2_hits: int = 0
    l2_misses: int = 0
    l3_hits: int = 0
    l3_misses: int = 0
    evictions: int = 0
    cache_size_bytes: int = 0
    avg_access_time: float = 0.0

class HighPerformanceCache:
    """High-performance multi-level caching system"""
    
    def __init__(self, config: CacheConfig, redis_url: str = "redis://localhost:6379"):
        self.config = config
        self.redis_url = redis_url
        self.redis_client: Optional[aioredis.Redis] = None
        
        # L1 Cache - In-memory with ordered dict for LRU
        self.l1_cache: OrderedDict[str, CacheItem] = OrderedDict()
        self.l1_access_counts: Dict[str, int] = {}
        self.l1_lock = asyncio.Lock()
        
        # L2 Cache - Redis
        self.l2_prefix = "noryon:cache:l2"
        
        # L3 Cache - Persistent storage
        self.l3_prefix = "noryon:cache:l3"
        
        # Cache warming
        self.cache_warming_patterns: List[Dict[str, Any]] = []
        self.warming_task: Optional[asyncio.Task] = None
        
        # Performance metrics
        self.metrics = CacheMetrics()
        self.access_times: List[float] = []
        
        # Thread pool for CPU-intensive operations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # Cache prediction (AI-powered cache warming)
        self.access_pattern_history: Dict[str, List[datetime]] = {}
        self.predicted_keys: List[str] = []
        
        logger.info("[INIT] High-Performance Caching System initialized")
    
    async def initialize(self):
        """Initialize caching system"""
        try:
            # Initialize Redis connection
            self.redis_client = aioredis.from_url(
                self.redis_url,
                decode_responses=False,  # Keep binary for compression
                max_connections=20
            )
            await self.redis_client.ping()
            
            # Start cache warming if enabled
            if self.config.cache_warming_enabled:
                await self._start_cache_warming()
            
            logger.info("✅ High-Performance Cache initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize cache: {e}")
            raise
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with multi-level lookup"""
        start_time = time.time()
        
        try:
            # L1 Cache (Memory) - Fastest
            l1_value = await self._get_l1(key)
            if l1_value is not None:
                self.metrics.l1_hits += 1
                self._record_access_time(start_time)
                await self._update_access_pattern(key)
                return l1_value
            
            self.metrics.l1_misses += 1
            
            # L2 Cache (Redis) - Fast
            l2_value = await self._get_l2(key)
            if l2_value is not None:
                self.metrics.l2_hits += 1
                # Promote to L1 cache
                await self._set_l1(key, l2_value)
                self._record_access_time(start_time)
                await self._update_access_pattern(key)
                return l2_value
            
            self.metrics.l2_misses += 1
            
            # L3 Cache (Persistent) - Slower
            l3_value = await self._get_l3(key)
            if l3_value is not None:
                self.metrics.l3_hits += 1
                # Promote to L2 and L1 caches
                await self._set_l2(key, l3_value)
                await self._set_l1(key, l3_value)
                self._record_access_time(start_time)
                await self._update_access_pattern(key)
                return l3_value
            
            self.metrics.l3_misses += 1
            self._record_access_time(start_time)
            return default
            
        except Exception as e:
            logger.error(f"❌ Cache get error for key {key}: {e}")
            return default
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, 
                 levels: List[CacheLevel] = None) -> bool:
        """Set value in cache with multi-level storage"""
        try:
            if levels is None:
                levels = [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS]
            
            # Set in specified cache levels
            tasks = []
            if CacheLevel.L1_MEMORY in levels:
                tasks.append(self._set_l1(key, value, ttl))
            if CacheLevel.L2_REDIS in levels:
                tasks.append(self._set_l2(key, value, ttl))
            if CacheLevel.L3_PERSISTENT in levels:
                tasks.append(self._set_l3(key, value, ttl))
            
            await asyncio.gather(*tasks)
            await self._update_access_pattern(key)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from all cache levels"""
        try:
            tasks = [
                self._delete_l1(key),
                self._delete_l2(key),
                self._delete_l3(key)
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)
            return True
            
        except Exception as e:
            logger.error(f"❌ Cache delete error for key {key}: {e}")
            return False
    
    async def clear(self, pattern: Optional[str] = None) -> int:
        """Clear cache entries matching pattern"""
        try:
            cleared_count = 0
            
            # Clear L1 cache
            if pattern:
                keys_to_remove = [k for k in self.l1_cache.keys() if pattern in k]
                for key in keys_to_remove:
                    await self._delete_l1(key)
                    cleared_count += 1
            else:
                cleared_count += len(self.l1_cache)
                self.l1_cache.clear()
                self.l1_access_counts.clear()
            
            # Clear L2 cache (Redis)
            if self.redis_client:
                if pattern:
                    redis_pattern = f"{self.l2_prefix}:*{pattern}*"
                    keys = await self.redis_client.keys(redis_pattern)
                    if keys:
                        await self.redis_client.delete(*keys)
                        cleared_count += len(keys)
                else:
                    redis_pattern = f"{self.l2_prefix}:*"
                    keys = await self.redis_client.keys(redis_pattern)
                    if keys:
                        await self.redis_client.delete(*keys)
                        cleared_count += len(keys)
            
            logger.info(f"🧹 Cleared {cleared_count} cache entries")
            return cleared_count
            
        except Exception as e:
            logger.error(f"❌ Cache clear error: {e}")
            return 0
    
    async def _get_l1(self, key: str) -> Any:
        """Get from L1 (memory) cache"""
        async with self.l1_lock:
            if key in self.l1_cache:
                item = self.l1_cache[key]
                
                # Check expiration
                if item.is_expired():
                    del self.l1_cache[key]
                    self.l1_access_counts.pop(key, None)
                    return None
                
                # Update access info
                item.accessed_at = datetime.now(timezone.utc)
                item.access_count += 1
                self.l1_access_counts[key] = self.l1_access_counts.get(key, 0) + 1
                
                # Move to end for LRU
                self.l1_cache.move_to_end(key)
                
                return item.value
            
            return None
    
    async def _set_l1(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set in L1 (memory) cache"""
        async with self.l1_lock:
            # Calculate size
            size_bytes = self._calculate_size(value)
            
            # Create cache item
            item = CacheItem(
                key=key,
                value=value,
                created_at=datetime.now(timezone.utc),
                accessed_at=datetime.now(timezone.utc),
                ttl=ttl,
                size_bytes=size_bytes
            )
            
            # Check if we need to evict items
            await self._evict_l1_if_needed(size_bytes)
            
            # Store item
            self.l1_cache[key] = item
            self.l1_access_counts[key] = 1
            
            # Update metrics
            self.metrics.cache_size_bytes += size_bytes
    
    async def _get_l2(self, key: str) -> Any:
        """Get from L2 (Redis) cache"""
        try:
            redis_key = f"{self.l2_prefix}:{key}"
            data = await self.redis_client.get(redis_key)
            
            if data:
                # Try to deserialize
                try:
                    # Check if compressed
                    if data.startswith(b'ZLIB:'):
                        data = zlib.decompress(data[5:])
                    
                    value = pickle.loads(data)
                    return value
                except:
                    # Fallback to JSON
                    return json.loads(data.decode('utf-8'))
            
            return None
            
        except Exception as e:
            logger.error(f"❌ L2 cache get error: {e}")
            return None
    
    async def _set_l2(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set in L2 (Redis) cache"""
        try:
            redis_key = f"{self.l2_prefix}:{key}"
            
            # Serialize value
            data = pickle.dumps(value)
            
            # Compress if large
            if len(data) > self.config.compression_threshold:
                data = b'ZLIB:' + zlib.compress(data)
            
            # Set with TTL
            cache_ttl = ttl or self.config.l2_default_ttl
            await self.redis_client.setex(redis_key, cache_ttl, data)
            
        except Exception as e:
            logger.error(f"❌ L2 cache set error: {e}")
    
    async def _get_l3(self, key: str) -> Any:
        """Get from L3 (persistent) cache - simplified implementation"""
        # This would typically interact with a persistent storage system
        # For now, using Redis with longer TTL as L3
        try:
            redis_key = f"{self.l3_prefix}:{key}"
            data = await self.redis_client.get(redis_key)
            
            if data:
                return pickle.loads(data)
            return None
            
        except Exception as e:
            logger.error(f"❌ L3 cache get error: {e}")
            return None
    
    async def _set_l3(self, key: str, value: Any, ttl: Optional[int] = None):
        """Set in L3 (persistent) cache"""
        try:
            redis_key = f"{self.l3_prefix}:{key}"
            data = pickle.dumps(value)
            
            cache_ttl = ttl or self.config.l3_default_ttl
            await self.redis_client.setex(redis_key, cache_ttl, data)
            
        except Exception as e:
            logger.error(f"❌ L3 cache set error: {e}")
    
    async def _delete_l1(self, key: str):
        """Delete from L1 cache"""
        async with self.l1_lock:
            if key in self.l1_cache:
                item = self.l1_cache.pop(key)
                self.l1_access_counts.pop(key, None)
                self.metrics.cache_size_bytes -= item.size_bytes
    
    async def _delete_l2(self, key: str):
        """Delete from L2 cache"""
        try:
            redis_key = f"{self.l2_prefix}:{key}"
            await self.redis_client.delete(redis_key)
        except Exception as e:
            logger.error(f"❌ L2 cache delete error: {e}")
    
    async def _delete_l3(self, key: str):
        """Delete from L3 cache"""
        try:
            redis_key = f"{self.l3_prefix}:{key}"
            await self.redis_client.delete(redis_key)
        except Exception as e:
            logger.error(f"❌ L3 cache delete error: {e}")
    
    async def _evict_l1_if_needed(self, new_item_size: int):
        """Evict items from L1 cache if needed"""
        max_size_bytes = self.config.l1_max_memory_mb * 1024 * 1024
        
        while (len(self.l1_cache) >= self.config.l1_max_size or 
               self.metrics.cache_size_bytes + new_item_size > max_size_bytes):
            
            if not self.l1_cache:
                break
            
            # Choose eviction strategy
            if self.config.eviction_strategy == CacheStrategy.LRU:
                # Remove least recently used (first item)
                key_to_evict = next(iter(self.l1_cache))
            elif self.config.eviction_strategy == CacheStrategy.LFU:
                # Remove least frequently used
                key_to_evict = min(self.l1_access_counts.items(), key=lambda x: x[1])[0]
            elif self.config.eviction_strategy == CacheStrategy.TTL:
                # Remove expired items first, then oldest
                expired_keys = [k for k, v in self.l1_cache.items() if v.is_expired()]
                if expired_keys:
                    key_to_evict = expired_keys[0]
                else:
                    key_to_evict = min(self.l1_cache.items(), key=lambda x: x[1].created_at)[0]
            else:  # ADAPTIVE
                # Intelligent eviction based on access patterns
                key_to_evict = await self._adaptive_eviction()
            
            # Evict the item
            item = self.l1_cache.pop(key_to_evict)
            self.l1_access_counts.pop(key_to_evict, None)
            self.metrics.cache_size_bytes -= item.size_bytes
            self.metrics.evictions += 1
    
    async def _adaptive_eviction(self) -> str:
        """Adaptive eviction based on access patterns and predictions"""
        # Score each item based on multiple factors
        scores = {}
        now = datetime.now(timezone.utc)
        
        for key, item in self.l1_cache.items():
            access_count = self.l1_access_counts.get(key, 1)
            time_since_access = (now - item.accessed_at).total_seconds()
            time_since_creation = (now - item.created_at).total_seconds()
            
            # Factors: recency, frequency, size, prediction
            recency_score = 1 / (time_since_access + 1)
            frequency_score = access_count / (time_since_creation + 1)
            size_penalty = item.size_bytes / 1000  # Penalize large items
            prediction_bonus = 2 if key in self.predicted_keys else 0
            
            # Combined score (higher = keep, lower = evict)
            scores[key] = recency_score + frequency_score - size_penalty + prediction_bonus
        
        # Return key with lowest score for eviction
        return min(scores.items(), key=lambda x: x[1])[0]
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes"""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value).encode('utf-8'))
    
    def _record_access_time(self, start_time: float):
        """Record cache access time for metrics"""
        access_time = time.time() - start_time
        self.access_times.append(access_time)
        
        # Keep only last 1000 measurements
        if len(self.access_times) > 1000:
            self.access_times = self.access_times[-1000:]
        
        # Update running average
        self.metrics.avg_access_time = sum(self.access_times) / len(self.access_times)
    
    async def _update_access_pattern(self, key: str):
        """Update access pattern for predictive caching"""
        now = datetime.now(timezone.utc)
        
        if key not in self.access_pattern_history:
            self.access_pattern_history[key] = []
        
        self.access_pattern_history[key].append(now)
        
        # Keep only last 100 accesses per key
        if len(self.access_pattern_history[key]) > 100:
            self.access_pattern_history[key] = self.access_pattern_history[key][-100:]
    
    async def _start_cache_warming(self):
        """Start cache warming background task"""
        self.warming_task = asyncio.create_task(self._cache_warming_loop())
        logger.info("🔥 Cache warming started")
    
    async def _cache_warming_loop(self):
        """Background cache warming loop"""
        while True:
            try:
                await asyncio.sleep(self.config.cache_warming_interval)
                
                # Predict keys that might be accessed soon
                predicted_keys = await self._predict_cache_needs()
                self.predicted_keys = predicted_keys
                
                # Warm cache with predicted keys
                await self._warm_cache(predicted_keys)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Cache warming error: {e}")
                await asyncio.sleep(60)
    
    async def _predict_cache_needs(self) -> List[str]:
        """Predict which keys might be needed soon"""
        predictions = []
        now = datetime.now(timezone.utc)
        
        for key, access_times in self.access_pattern_history.items():
            if len(access_times) < 3:
                continue
            
            # Calculate access frequency and pattern
            recent_accesses = [t for t in access_times if (now - t).seconds < 3600]  # Last hour
            
            if len(recent_accesses) >= 2:
                # Calculate average interval between accesses
                intervals = []
                for i in range(1, len(recent_accesses)):
                    interval = (recent_accesses[i] - recent_accesses[i-1]).total_seconds()
                    intervals.append(interval)
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    last_access = recent_accesses[-1]
                    time_since_last = (now - last_access).total_seconds()
                    
                    # Predict if key might be accessed soon
                    if time_since_last >= avg_interval * 0.8:
                        predictions.append(key)
        
        return predictions[:50]  # Limit predictions
    
    async def _warm_cache(self, keys: List[str]):
        """Warm cache with predicted keys"""
        # This would typically fetch data from the source
        # For now, just log the warming
        if keys:
            logger.info(f"🔥 Cache warming for {len(keys)} predicted keys")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        try:
            # Calculate hit rates
            total_l1 = self.metrics.l1_hits + self.metrics.l1_misses
            total_l2 = self.metrics.l2_hits + self.metrics.l2_misses
            total_l3 = self.metrics.l3_hits + self.metrics.l3_misses
            
            l1_hit_rate = self.metrics.l1_hits / max(total_l1, 1)
            l2_hit_rate = self.metrics.l2_hits / max(total_l2, 1)
            l3_hit_rate = self.metrics.l3_hits / max(total_l3, 1)
            overall_hit_rate = (self.metrics.l1_hits + self.metrics.l2_hits + self.metrics.l3_hits) / max(total_l1 + total_l2 + total_l3, 1)
            
            return {
                "hit_rates": {
                    "l1_hit_rate": l1_hit_rate,
                    "l2_hit_rate": l2_hit_rate,
                    "l3_hit_rate": l3_hit_rate,
                    "overall_hit_rate": overall_hit_rate
                },
                "cache_sizes": {
                    "l1_items": len(self.l1_cache),
                    "l1_size_bytes": self.metrics.cache_size_bytes,
                    "l1_max_items": self.config.l1_max_size,
                    "l1_max_bytes": self.config.l1_max_memory_mb * 1024 * 1024
                },
                "performance": {
                    "avg_access_time": self.metrics.avg_access_time,
                    "total_evictions": self.metrics.evictions,
                    "predicted_keys": len(self.predicted_keys)
                },
                "raw_metrics": asdict(self.metrics)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get cache stats: {e}")
            return {}
    
    async def shutdown(self):
        """Shutdown caching system"""
        try:
            if self.warming_task:
                self.warming_task.cancel()
                try:
                    await self.warming_task
                except asyncio.CancelledError:
                    pass
            
            if self.redis_client:
                await self.redis_client.close()
            
            self.thread_pool.shutdown(wait=True)
            
            logger.info("✅ Cache system shutdown successfully")
            
        except Exception as e:
            logger.error(f"❌ Cache shutdown error: {e}")

# Factory function
async def create_high_performance_cache(config: CacheConfig = None, 
                                      redis_url: str = "redis://localhost:6379") -> HighPerformanceCache:
    """Factory function to create and initialize cache"""
    if config is None:
        config = CacheConfig()
    
    cache = HighPerformanceCache(config, redis_url)
    await cache.initialize()
    return cache

# Example usage
if __name__ == "__main__":
    async def main():
        config = CacheConfig(
            l1_max_size=5000,
            l1_max_memory_mb=256,
            cache_warming_enabled=True
        )
        
        cache = await create_high_performance_cache(config)
        
        # Test cache operations
        await cache.set("test_key", {"data": "test_value", "timestamp": time.time()})
        value = await cache.get("test_key")
        print(f"Cached value: {value}")
        
        # Get stats
        stats = await cache.get_cache_stats()
        print(f"Cache stats: {stats}")
        
        await cache.shutdown()
    
    asyncio.run(main()) 