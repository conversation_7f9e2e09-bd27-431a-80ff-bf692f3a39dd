#!/usr/bin/env python3
"""
Advanced Market Microstructure Analysis Engine
Professional-grade order book analysis, market impact modeling, and liquidity assessment
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger("AdvancedMarketMicrostructure")


class LiquidityRegime(Enum):
    """Market liquidity regimes."""
    ULTRA_HIGH = "ultra_high"
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"
    CRISIS = "crisis"


class MarketImpactModel(Enum):
    """Market impact models."""
    LINEAR = "linear"
    SQUARE_ROOT = "square_root"
    LOGARITHMIC = "logarithmic"
    ALMGREN_CHRISS = "almgren_chriss"


@dataclass
class OrderBookLevel:
    """Order book level data."""
    price: float
    size: float
    orders: int
    timestamp: datetime


@dataclass
class MarketMicrostructureMetrics:
    """Market microstructure analysis results."""
    bid_ask_spread: float
    effective_spread: float
    realized_spread: float
    price_impact: float
    market_depth: float
    liquidity_score: float
    order_flow_imbalance: float
    volume_weighted_spread: float
    resilience_score: float
    toxicity_score: float
    liquidity_regime: LiquidityRegime
    timestamp: datetime


class AdvancedMarketMicrostructure:
    """Advanced market microstructure analysis engine."""
    
    def __init__(self):
        self.order_book_history = {}
        self.trade_history = {}
        self.microstructure_metrics = {}
        
        # Market impact models
        self.impact_models = {
            MarketImpactModel.LINEAR: self._linear_impact_model,
            MarketImpactModel.SQUARE_ROOT: self._square_root_impact_model,
            MarketImpactModel.LOGARITHMIC: self._logarithmic_impact_model,
            MarketImpactModel.ALMGREN_CHRISS: self._almgren_chriss_model
        }
        
        # Liquidity thresholds
        self.liquidity_thresholds = {
            LiquidityRegime.ULTRA_HIGH: 0.9,
            LiquidityRegime.HIGH: 0.7,
            LiquidityRegime.NORMAL: 0.5,
            LiquidityRegime.LOW: 0.3,
            LiquidityRegime.CRISIS: 0.1
        }
    
    def analyze_market_microstructure(self, symbol: str, order_book_data: Dict[str, Any], 
                                    trade_data: List[Dict[str, Any]]) -> MarketMicrostructureMetrics:
        """Analyze comprehensive market microstructure."""
        try:
            # Extract order book levels
            bids = self._parse_order_book_levels(order_book_data.get("bids", []))
            asks = self._parse_order_book_levels(order_book_data.get("asks", []))
            
            if not bids or not asks:
                return self._default_microstructure_metrics()
            
            # Calculate basic spreads
            bid_ask_spread = self._calculate_bid_ask_spread(bids[0], asks[0])
            effective_spread = self._calculate_effective_spread(trade_data, bids[0], asks[0])
            realized_spread = self._calculate_realized_spread(trade_data)
            
            # Calculate market depth
            market_depth = self._calculate_market_depth(bids, asks)
            
            # Calculate liquidity metrics
            liquidity_score = self._calculate_liquidity_score(bids, asks, trade_data)
            
            # Calculate order flow imbalance
            order_flow_imbalance = self._calculate_order_flow_imbalance(bids, asks)
            
            # Calculate price impact
            price_impact = self._calculate_price_impact(trade_data, order_book_data)
            
            # Calculate volume weighted spread
            vw_spread = self._calculate_volume_weighted_spread(bids, asks)
            
            # Calculate resilience score
            resilience_score = self._calculate_resilience_score(trade_data, order_book_data)
            
            # Calculate toxicity score
            toxicity_score = self._calculate_toxicity_score(trade_data)
            
            # Determine liquidity regime
            liquidity_regime = self._determine_liquidity_regime(liquidity_score)
            
            metrics = MarketMicrostructureMetrics(
                bid_ask_spread=round(bid_ask_spread, 6),
                effective_spread=round(effective_spread, 6),
                realized_spread=round(realized_spread, 6),
                price_impact=round(price_impact, 6),
                market_depth=round(market_depth, 2),
                liquidity_score=round(liquidity_score, 4),
                order_flow_imbalance=round(order_flow_imbalance, 4),
                volume_weighted_spread=round(vw_spread, 6),
                resilience_score=round(resilience_score, 4),
                toxicity_score=round(toxicity_score, 4),
                liquidity_regime=liquidity_regime,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Store metrics
            if symbol not in self.microstructure_metrics:
                self.microstructure_metrics[symbol] = []
            self.microstructure_metrics[symbol].append(metrics)
            
            # Keep last 1000 metrics
            if len(self.microstructure_metrics[symbol]) > 1000:
                self.microstructure_metrics[symbol] = self.microstructure_metrics[symbol][-1000:]
            
            logger.info(f"✅ Market microstructure analyzed for {symbol}: {liquidity_regime.value} liquidity")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Market microstructure analysis error: {e}")
            return self._default_microstructure_metrics()
    
    def _parse_order_book_levels(self, levels: List[List[float]]) -> List[OrderBookLevel]:
        """Parse order book levels."""
        parsed_levels = []
        for level in levels[:10]:  # Top 10 levels
            if len(level) >= 2:
                price, size = level[0], level[1]
                orders = level[2] if len(level) > 2 else 1
                parsed_levels.append(OrderBookLevel(
                    price=price,
                    size=size,
                    orders=orders,
                    timestamp=datetime.now(timezone.utc)
                ))
        return parsed_levels
    
    def _calculate_bid_ask_spread(self, best_bid: OrderBookLevel, best_ask: OrderBookLevel) -> float:
        """Calculate bid-ask spread."""
        return (best_ask.price - best_bid.price) / ((best_bid.price + best_ask.price) / 2)
    
    def _calculate_effective_spread(self, trades: List[Dict[str, Any]], 
                                  best_bid: OrderBookLevel, best_ask: OrderBookLevel) -> float:
        """Calculate effective spread."""
        if not trades:
            return self._calculate_bid_ask_spread(best_bid, best_ask)
        
        mid_price = (best_bid.price + best_ask.price) / 2
        effective_spreads = []
        
        for trade in trades[-10:]:  # Last 10 trades
            trade_price = trade.get("price", mid_price)
            side = trade.get("side", "buy")
            
            if side == "buy":
                effective_spread = 2 * (trade_price - mid_price) / mid_price
            else:
                effective_spread = 2 * (mid_price - trade_price) / mid_price
            
            effective_spreads.append(abs(effective_spread))
        
        return np.mean(effective_spreads) if effective_spreads else 0.0
    
    def _calculate_realized_spread(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate realized spread."""
        if len(trades) < 2:
            return 0.0
        
        realized_spreads = []
        for i in range(1, min(len(trades), 11)):  # Last 10 trade pairs
            current_trade = trades[-i]
            previous_trade = trades[-(i+1)]
            
            price_change = abs(current_trade.get("price", 0) - previous_trade.get("price", 0))
            mid_price = (current_trade.get("price", 0) + previous_trade.get("price", 0)) / 2
            
            if mid_price > 0:
                realized_spread = price_change / mid_price
                realized_spreads.append(realized_spread)
        
        return np.mean(realized_spreads) if realized_spreads else 0.0
    
    def _calculate_market_depth(self, bids: List[OrderBookLevel], asks: List[OrderBookLevel]) -> float:
        """Calculate market depth."""
        bid_depth = sum(level.size for level in bids[:5])  # Top 5 levels
        ask_depth = sum(level.size for level in asks[:5])  # Top 5 levels
        return bid_depth + ask_depth
    
    def _calculate_liquidity_score(self, bids: List[OrderBookLevel], asks: List[OrderBookLevel], 
                                 trades: List[Dict[str, Any]]) -> float:
        """Calculate comprehensive liquidity score."""
        # Depth component
        depth_score = min(self._calculate_market_depth(bids, asks) / 1000000, 1.0)  # Normalize to 1M
        
        # Spread component
        if bids and asks:
            spread = self._calculate_bid_ask_spread(bids[0], asks[0])
            spread_score = max(0, 1 - spread * 100)  # Lower spread = higher score
        else:
            spread_score = 0.0
        
        # Volume component
        recent_volume = sum(trade.get("size", 0) for trade in trades[-20:])  # Last 20 trades
        volume_score = min(recent_volume / 10000000, 1.0)  # Normalize to 10M
        
        # Resilience component (simplified)
        resilience_score = 0.7  # Default resilience
        
        # Weighted combination
        liquidity_score = (0.3 * depth_score + 0.3 * spread_score + 
                          0.2 * volume_score + 0.2 * resilience_score)
        
        return liquidity_score
    
    def _calculate_order_flow_imbalance(self, bids: List[OrderBookLevel], asks: List[OrderBookLevel]) -> float:
        """Calculate order flow imbalance."""
        if not bids or not asks:
            return 0.0
        
        bid_volume = sum(level.size for level in bids[:5])
        ask_volume = sum(level.size for level in asks[:5])
        total_volume = bid_volume + ask_volume
        
        if total_volume == 0:
            return 0.0
        
        imbalance = (bid_volume - ask_volume) / total_volume
        return imbalance
    
    def _calculate_price_impact(self, trades: List[Dict[str, Any]], order_book: Dict[str, Any]) -> float:
        """Calculate price impact."""
        if len(trades) < 2:
            return 0.0
        
        # Simplified price impact calculation
        recent_trades = trades[-5:]  # Last 5 trades
        price_changes = []
        
        for i in range(1, len(recent_trades)):
            current_price = recent_trades[i].get("price", 0)
            previous_price = recent_trades[i-1].get("price", 0)
            
            if previous_price > 0:
                price_change = abs(current_price - previous_price) / previous_price
                price_changes.append(price_change)
        
        return np.mean(price_changes) if price_changes else 0.0
    
    def _calculate_volume_weighted_spread(self, bids: List[OrderBookLevel], asks: List[OrderBookLevel]) -> float:
        """Calculate volume-weighted spread."""
        if not bids or not asks:
            return 0.0
        
        total_volume = 0
        weighted_spread = 0
        
        for i in range(min(5, len(bids), len(asks))):  # Top 5 levels
            bid_level = bids[i]
            ask_level = asks[i]
            
            level_volume = min(bid_level.size, ask_level.size)
            level_spread = (ask_level.price - bid_level.price) / ((bid_level.price + ask_level.price) / 2)
            
            weighted_spread += level_spread * level_volume
            total_volume += level_volume
        
        return weighted_spread / total_volume if total_volume > 0 else 0.0
    
    def _calculate_resilience_score(self, trades: List[Dict[str, Any]], order_book: Dict[str, Any]) -> float:
        """Calculate market resilience score."""
        # Simplified resilience calculation
        if len(trades) < 10:
            return 0.5  # Default resilience
        
        # Measure how quickly prices revert after large trades
        large_trades = [t for t in trades[-20:] if t.get("size", 0) > 1000000]  # $1M+ trades
        
        if not large_trades:
            return 0.8  # High resilience if no large trades
        
        # Calculate price reversion after large trades
        reversion_scores = []
        for trade in large_trades[-5:]:  # Last 5 large trades
            # Simplified: assume 70% reversion (would need more sophisticated calculation)
            reversion_scores.append(0.7)
        
        return np.mean(reversion_scores) if reversion_scores else 0.5
    
    def _calculate_toxicity_score(self, trades: List[Dict[str, Any]]) -> float:
        """Calculate order flow toxicity score."""
        if len(trades) < 10:
            return 0.3  # Default low toxicity
        
        # Simplified toxicity calculation based on trade patterns
        recent_trades = trades[-20:]
        
        # Look for patterns indicating informed trading
        large_trade_ratio = len([t for t in recent_trades if t.get("size", 0) > 500000]) / len(recent_trades)
        
        # High frequency of large trades might indicate informed trading
        toxicity_score = min(large_trade_ratio * 2, 1.0)
        
        return toxicity_score
    
    def _determine_liquidity_regime(self, liquidity_score: float) -> LiquidityRegime:
        """Determine current liquidity regime."""
        if liquidity_score >= self.liquidity_thresholds[LiquidityRegime.ULTRA_HIGH]:
            return LiquidityRegime.ULTRA_HIGH
        elif liquidity_score >= self.liquidity_thresholds[LiquidityRegime.HIGH]:
            return LiquidityRegime.HIGH
        elif liquidity_score >= self.liquidity_thresholds[LiquidityRegime.NORMAL]:
            return LiquidityRegime.NORMAL
        elif liquidity_score >= self.liquidity_thresholds[LiquidityRegime.LOW]:
            return LiquidityRegime.LOW
        else:
            return LiquidityRegime.CRISIS
    
    def _linear_impact_model(self, trade_size: float, daily_volume: float) -> float:
        """Linear market impact model."""
        participation_rate = trade_size / daily_volume if daily_volume > 0 else 0
        return 0.1 * participation_rate  # 10 bps per 1% participation
    
    def _square_root_impact_model(self, trade_size: float, daily_volume: float) -> float:
        """Square root market impact model."""
        participation_rate = trade_size / daily_volume if daily_volume > 0 else 0
        return 0.1 * np.sqrt(participation_rate)
    
    def _logarithmic_impact_model(self, trade_size: float, daily_volume: float) -> float:
        """Logarithmic market impact model."""
        participation_rate = trade_size / daily_volume if daily_volume > 0 else 0
        if participation_rate <= 0:
            return 0
        return 0.05 * np.log(1 + participation_rate)
    
    def _almgren_chriss_model(self, trade_size: float, daily_volume: float, volatility: float = 0.02) -> float:
        """Almgren-Chriss market impact model."""
        participation_rate = trade_size / daily_volume if daily_volume > 0 else 0
        return volatility * 0.5 * np.sqrt(participation_rate)
    
    def estimate_market_impact(self, trade_size: float, symbol: str, 
                             model: MarketImpactModel = MarketImpactModel.SQUARE_ROOT) -> float:
        """Estimate market impact for a given trade size."""
        try:
            # Get recent market data
            daily_volume = 100000000  # Default $100M daily volume
            volatility = 0.02  # Default 2% volatility
            
            # Use specified impact model
            impact_func = self.impact_models.get(model, self._square_root_impact_model)
            
            if model == MarketImpactModel.ALMGREN_CHRISS:
                impact = impact_func(trade_size, daily_volume, volatility)
            else:
                impact = impact_func(trade_size, daily_volume)
            
            return impact
            
        except Exception as e:
            logger.error(f"Market impact estimation error: {e}")
            return 0.001  # Default 10 bps impact
    
    def _default_microstructure_metrics(self) -> MarketMicrostructureMetrics:
        """Default microstructure metrics for error cases."""
        return MarketMicrostructureMetrics(
            bid_ask_spread=0.001,
            effective_spread=0.001,
            realized_spread=0.001,
            price_impact=0.001,
            market_depth=1000000,
            liquidity_score=0.5,
            order_flow_imbalance=0.0,
            volume_weighted_spread=0.001,
            resilience_score=0.5,
            toxicity_score=0.3,
            liquidity_regime=LiquidityRegime.NORMAL,
            timestamp=datetime.now(timezone.utc)
        )
    
    def get_microstructure_summary(self, symbol: str) -> Dict[str, Any]:
        """Get microstructure summary for a symbol."""
        if symbol not in self.microstructure_metrics or not self.microstructure_metrics[symbol]:
            return {"error": "No microstructure data available"}
        
        recent_metrics = self.microstructure_metrics[symbol][-10:]  # Last 10 measurements
        
        return {
            "symbol": symbol,
            "current_liquidity_regime": recent_metrics[-1].liquidity_regime.value,
            "avg_liquidity_score": round(np.mean([m.liquidity_score for m in recent_metrics]), 4),
            "avg_bid_ask_spread": round(np.mean([m.bid_ask_spread for m in recent_metrics]) * 10000, 2),  # In bps
            "avg_market_depth": round(np.mean([m.market_depth for m in recent_metrics]), 0),
            "avg_price_impact": round(np.mean([m.price_impact for m in recent_metrics]) * 10000, 2),  # In bps
            "avg_toxicity_score": round(np.mean([m.toxicity_score for m in recent_metrics]), 4),
            "measurements_count": len(recent_metrics),
            "last_updated": recent_metrics[-1].timestamp.isoformat()
        }


# Test the Advanced Market Microstructure Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🔬 Testing Advanced Market Microstructure Engine")
    print("=" * 70)
    
    # Initialize microstructure engine
    microstructure_engine = AdvancedMarketMicrostructure()
    
    # Sample order book data
    order_book_data = {
        "bids": [[45000, 1.5], [44995, 2.0], [44990, 1.8], [44985, 2.2], [44980, 1.9]],
        "asks": [[45005, 1.4], [45010, 1.9], [45015, 2.1], [45020, 1.7], [45025, 2.0]]
    }
    
    # Sample trade data
    trade_data = [
        {"price": 45002, "size": 1.2, "side": "buy", "timestamp": datetime.now()},
        {"price": 45001, "size": 0.8, "side": "sell", "timestamp": datetime.now()},
        {"price": 45003, "size": 2.1, "side": "buy", "timestamp": datetime.now()},
        {"price": 45000, "size": 1.5, "side": "sell", "timestamp": datetime.now()}
    ]
    
    # Analyze market microstructure
    print("🔍 Analyzing market microstructure...")
    metrics = microstructure_engine.analyze_market_microstructure("BTCUSDT", order_book_data, trade_data)
    
    print(f"✅ Liquidity Regime: {metrics.liquidity_regime.value}")
    print(f"✅ Liquidity Score: {metrics.liquidity_score:.4f}")
    print(f"✅ Bid-Ask Spread: {metrics.bid_ask_spread * 10000:.1f} bps")
    print(f"✅ Market Depth: ${metrics.market_depth:,.0f}")
    print(f"✅ Price Impact: {metrics.price_impact * 10000:.1f} bps")
    print(f"✅ Order Flow Imbalance: {metrics.order_flow_imbalance:.4f}")
    
    # Test market impact estimation
    print("\n💥 Testing market impact estimation...")
    impact_models = [MarketImpactModel.LINEAR, MarketImpactModel.SQUARE_ROOT, 
                    MarketImpactModel.LOGARITHMIC, MarketImpactModel.ALMGREN_CHRISS]
    
    trade_size = 1000000  # $1M trade
    for model in impact_models:
        impact = microstructure_engine.estimate_market_impact(trade_size, "BTCUSDT", model)
        print(f"✅ {model.value}: {impact * 10000:.1f} bps impact")
    
    # Get microstructure summary
    print("\n📊 Microstructure summary...")
    summary = microstructure_engine.get_microstructure_summary("BTCUSDT")
    print(f"✅ Current Regime: {summary.get('current_liquidity_regime', 'N/A')}")
    print(f"✅ Avg Liquidity: {summary.get('avg_liquidity_score', 0):.4f}")
    print(f"✅ Avg Spread: {summary.get('avg_bid_ask_spread', 0):.1f} bps")
    
    print("\n✅ Advanced Market Microstructure test completed!")
