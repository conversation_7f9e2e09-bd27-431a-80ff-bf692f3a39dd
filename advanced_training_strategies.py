#!/usr/bin/env python3
"""
🎯 ADVANCED TRAINING STRATEGIES
Real training techniques that improve model performance
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import (
    TimeSeriesSplit, GroupKFold, StratifiedKFold,
    learning_curve, validation_curve
)
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.utils.class_weight import compute_sample_weight
import optuna
import logging
from typing import Dict, List, Tuple, Any, Callable
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AdvancedTraining")

class AdvancedTrainingStrategies:
    """Advanced training strategies for better model performance"""
    
    def __init__(self):
        self.training_history = []
        self.best_models = {}
        self.optimization_results = {}
        
    def time_series_cross_validation(self, X: np.ndarray, y: np.ndarray, 
                                   model: Any, n_splits: int = 5) -> Dict[str, Any]:
        """Proper time series cross-validation"""
        
        # Use TimeSeriesSplit for time series data
        tscv = TimeSeriesSplit(n_splits=n_splits)
        
        cv_scores = []
        feature_importance_list = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # Train model on fold
            model_fold = model.__class__(**model.get_params())
            model_fold.fit(X_train_fold, y_train_fold)
            
            # Validate
            y_pred = model_fold.predict(X_val_fold)
            mse = mean_squared_error(y_val_fold, y_pred)
            mae = mean_absolute_error(y_val_fold, y_pred)
            
            cv_scores.append({
                'fold': fold,
                'mse': mse,
                'mae': mae,
                'train_size': len(X_train_fold),
                'val_size': len(X_val_fold)
            })
            
            # Feature importance (if available)
            if hasattr(model_fold, 'feature_importances_'):
                feature_importance_list.append(model_fold.feature_importances_)
            
            logger.info(f"Fold {fold}: MSE={mse:.6f}, MAE={mae:.6f}")
        
        # Aggregate results
        cv_results = {
            'cv_scores': cv_scores,
            'mean_mse': np.mean([score['mse'] for score in cv_scores]),
            'std_mse': np.std([score['mse'] for score in cv_scores]),
            'mean_mae': np.mean([score['mae'] for score in cv_scores]),
            'std_mae': np.std([score['mae'] for score in cv_scores]),
            'feature_importance_mean': np.mean(feature_importance_list, axis=0) if feature_importance_list else None,
            'feature_importance_std': np.std(feature_importance_list, axis=0) if feature_importance_list else None
        }
        
        logger.info(f"✅ Time series CV: MSE={cv_results['mean_mse']:.6f}±{cv_results['std_mse']:.6f}")
        return cv_results

    def walk_forward_validation(self, X: np.ndarray, y: np.ndarray, 
                               model: Any, window_size: int = 100, 
                               step_size: int = 20) -> Dict[str, Any]:
        """Walk-forward validation for time series"""
        
        if len(X) < window_size + step_size:
            raise ValueError("Not enough data for walk-forward validation")
        
        predictions = []
        actuals = []
        model_performances = []
        
        start_idx = 0
        while start_idx + window_size + step_size <= len(X):
            # Training window
            train_end = start_idx + window_size
            X_train = X[start_idx:train_end]
            y_train = y[start_idx:train_end]
            
            # Test window
            test_start = train_end
            test_end = test_start + step_size
            X_test = X[test_start:test_end]
            y_test = y[test_start:test_end]
            
            # Train model
            model_step = model.__class__(**model.get_params())
            model_step.fit(X_train, y_train)
            
            # Predict
            y_pred = model_step.predict(X_test)
            
            # Store results
            predictions.extend(y_pred)
            actuals.extend(y_test)
            
            # Performance for this step
            step_mse = mean_squared_error(y_test, y_pred)
            step_mae = mean_absolute_error(y_test, y_pred)
            
            model_performances.append({
                'step': len(model_performances),
                'train_start': start_idx,
                'train_end': train_end,
                'test_start': test_start,
                'test_end': test_end,
                'mse': step_mse,
                'mae': step_mae
            })
            
            start_idx += step_size
        
        # Overall performance
        overall_mse = mean_squared_error(actuals, predictions)
        overall_mae = mean_absolute_error(actuals, predictions)
        
        results = {
            'predictions': predictions,
            'actuals': actuals,
            'model_performances': model_performances,
            'overall_mse': overall_mse,
            'overall_mae': overall_mae,
            'n_steps': len(model_performances)
        }
        
        logger.info(f"✅ Walk-forward validation: {results['n_steps']} steps, MSE={overall_mse:.6f}")
        return results

    def bayesian_optimization(self, X_train: np.ndarray, y_train: np.ndarray,
                             X_val: np.ndarray, y_val: np.ndarray,
                             model_type: str = 'random_forest',
                             n_trials: int = 100) -> Dict[str, Any]:
        """Bayesian optimization for hyperparameter tuning"""
        
        def objective(trial):
            if model_type == 'random_forest':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 500),
                    'max_depth': trial.suggest_int('max_depth', 5, 30),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                    'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                    'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                    'random_state': 42,
                    'n_jobs': -1
                }
                model = RandomForestRegressor(**params)
                
            elif model_type == 'gradient_boosting':
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 500),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'max_depth': trial.suggest_int('max_depth', 3, 15),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'random_state': 42
                }
                model = GradientBoostingRegressor(**params)
            else:
                raise ValueError(f"Unknown model type: {model_type}")
            
            # Train and evaluate
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            mse = mean_squared_error(y_val, y_pred)
            
            return mse
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)
        
        # Get best model
        best_params = study.best_params
        if model_type == 'random_forest':
            best_params['random_state'] = 42
            best_params['n_jobs'] = -1
            best_model = RandomForestRegressor(**best_params)
        else:
            best_params['random_state'] = 42
            best_model = GradientBoostingRegressor(**best_params)
        
        best_model.fit(X_train, y_train)
        
        results = {
            'best_params': best_params,
            'best_model': best_model,
            'best_score': study.best_value,
            'n_trials': len(study.trials),
            'study': study
        }
        
        logger.info(f"✅ Bayesian optimization: Best MSE={study.best_value:.6f} in {n_trials} trials")
        return results

    def learning_curve_analysis(self, X: np.ndarray, y: np.ndarray, 
                               model: Any, train_sizes: np.ndarray = None) -> Dict[str, Any]:
        """Analyze learning curves to detect overfitting/underfitting"""
        
        if train_sizes is None:
            train_sizes = np.linspace(0.1, 1.0, 10)
        
        # Use TimeSeriesSplit for time series data
        cv = TimeSeriesSplit(n_splits=5)
        
        train_sizes_abs, train_scores, val_scores = learning_curve(
            model, X, y, 
            train_sizes=train_sizes,
            cv=cv,
            scoring='neg_mean_squared_error',
            n_jobs=-1
        )
        
        # Convert to positive MSE
        train_scores = -train_scores
        val_scores = -val_scores
        
        results = {
            'train_sizes': train_sizes_abs,
            'train_scores_mean': np.mean(train_scores, axis=1),
            'train_scores_std': np.std(train_scores, axis=1),
            'val_scores_mean': np.mean(val_scores, axis=1),
            'val_scores_std': np.std(val_scores, axis=1),
            'overfitting_score': np.mean(train_scores, axis=1)[-1] - np.mean(val_scores, axis=1)[-1]
        }
        
        logger.info(f"✅ Learning curve analysis: Overfitting score={results['overfitting_score']:.6f}")
        return results

    def ensemble_training(self, X_train: np.ndarray, y_train: np.ndarray,
                         X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """Train ensemble of diverse models"""
        
        # Define diverse base models
        base_models = {
            'rf_deep': RandomForestRegressor(
                n_estimators=200, max_depth=20, min_samples_split=2,
                random_state=42, n_jobs=-1
            ),
            'rf_shallow': RandomForestRegressor(
                n_estimators=200, max_depth=8, min_samples_split=10,
                random_state=43, n_jobs=-1
            ),
            'gb_fast': GradientBoostingRegressor(
                n_estimators=100, learning_rate=0.2, max_depth=6,
                random_state=42
            ),
            'gb_slow': GradientBoostingRegressor(
                n_estimators=300, learning_rate=0.05, max_depth=8,
                random_state=43
            )
        }
        
        # Train each model
        trained_models = {}
        model_predictions = {}
        model_scores = {}
        
        for name, model in base_models.items():
            logger.info(f"Training {name}...")
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            mse = mean_squared_error(y_val, y_pred)
            
            trained_models[name] = model
            model_predictions[name] = y_pred
            model_scores[name] = mse
            
            logger.info(f"  {name}: MSE={mse:.6f}")
        
        # Create ensemble predictions
        ensemble_methods = {
            'simple_average': np.mean(list(model_predictions.values()), axis=0),
            'weighted_average': self._weighted_ensemble(model_predictions, model_scores),
            'best_model': model_predictions[min(model_scores.keys(), key=lambda k: model_scores[k])]
        }
        
        # Evaluate ensemble methods
        ensemble_scores = {}
        for method, predictions in ensemble_methods.items():
            mse = mean_squared_error(y_val, predictions)
            ensemble_scores[method] = mse
            logger.info(f"Ensemble {method}: MSE={mse:.6f}")
        
        # Select best ensemble
        best_ensemble_method = min(ensemble_scores.keys(), key=lambda k: ensemble_scores[k])
        
        results = {
            'base_models': trained_models,
            'model_scores': model_scores,
            'ensemble_methods': ensemble_methods,
            'ensemble_scores': ensemble_scores,
            'best_ensemble_method': best_ensemble_method,
            'best_ensemble_score': ensemble_scores[best_ensemble_method]
        }
        
        logger.info(f"✅ Ensemble training: Best method={best_ensemble_method}, MSE={ensemble_scores[best_ensemble_method]:.6f}")
        return results

    def _weighted_ensemble(self, predictions: Dict[str, np.ndarray], 
                          scores: Dict[str, float]) -> np.ndarray:
        """Create weighted ensemble based on inverse of scores"""
        
        # Calculate weights (inverse of MSE)
        weights = {}
        total_weight = 0
        
        for name in predictions.keys():
            weight = 1.0 / (scores[name] + 1e-8)  # Add small epsilon to avoid division by zero
            weights[name] = weight
            total_weight += weight
        
        # Normalize weights
        for name in weights.keys():
            weights[name] /= total_weight
        
        # Create weighted prediction
        weighted_pred = np.zeros_like(list(predictions.values())[0])
        for name, pred in predictions.items():
            weighted_pred += weights[name] * pred
        
        return weighted_pred

    def adaptive_learning_rate(self, X_train: np.ndarray, y_train: np.ndarray,
                              X_val: np.ndarray, y_val: np.ndarray,
                              initial_lr: float = 0.1, patience: int = 10) -> Dict[str, Any]:
        """Adaptive learning rate for gradient boosting"""
        
        best_score = float('inf')
        best_model = None
        patience_counter = 0
        learning_rates = []
        scores = []
        
        current_lr = initial_lr
        
        for iteration in range(100):  # Max 100 iterations
            # Train model with current learning rate
            model = GradientBoostingRegressor(
                n_estimators=50,
                learning_rate=current_lr,
                max_depth=8,
                random_state=42
            )
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_val)
            score = mean_squared_error(y_val, y_pred)
            
            learning_rates.append(current_lr)
            scores.append(score)
            
            if score < best_score:
                best_score = score
                best_model = model
                patience_counter = 0
            else:
                patience_counter += 1
            
            # Adjust learning rate
            if patience_counter >= patience:
                current_lr *= 0.5  # Reduce learning rate
                patience_counter = 0
                
                if current_lr < 0.001:  # Minimum learning rate
                    break
            
            if iteration % 10 == 0:
                logger.info(f"Iteration {iteration}: LR={current_lr:.4f}, Score={score:.6f}")
        
        results = {
            'best_model': best_model,
            'best_score': best_score,
            'learning_rates': learning_rates,
            'scores': scores,
            'final_lr': current_lr,
            'iterations': len(scores)
        }
        
        logger.info(f"✅ Adaptive learning rate: Best score={best_score:.6f} in {len(scores)} iterations")
        return results

    def comprehensive_training_pipeline(self, X_train: np.ndarray, y_train: np.ndarray,
                                       X_val: np.ndarray, y_val: np.ndarray,
                                       X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """Comprehensive training pipeline with all advanced strategies"""
        
        logger.info("🎯 Starting comprehensive training pipeline")
        
        results = {}
        
        # 1. Time series cross-validation
        logger.info("1️⃣ Time series cross-validation...")
        base_model = RandomForestRegressor(n_estimators=100, random_state=42)
        cv_results = self.time_series_cross_validation(
            np.vstack([X_train, X_val]), 
            np.hstack([y_train, y_val]), 
            base_model
        )
        results['cv_results'] = cv_results
        
        # 2. Bayesian optimization
        logger.info("2️⃣ Bayesian optimization...")
        bayes_results = self.bayesian_optimization(X_train, y_train, X_val, y_val, 'random_forest', 50)
        results['bayes_results'] = bayes_results
        
        # 3. Learning curve analysis
        logger.info("3️⃣ Learning curve analysis...")
        learning_results = self.learning_curve_analysis(
            np.vstack([X_train, X_val]), 
            np.hstack([y_train, y_val]), 
            bayes_results['best_model']
        )
        results['learning_results'] = learning_results
        
        # 4. Ensemble training
        logger.info("4️⃣ Ensemble training...")
        ensemble_results = self.ensemble_training(X_train, y_train, X_val, y_val)
        results['ensemble_results'] = ensemble_results
        
        # 5. Final evaluation on test set
        logger.info("5️⃣ Final test evaluation...")
        best_model = bayes_results['best_model']
        y_test_pred = best_model.predict(X_test)
        test_mse = mean_squared_error(y_test, y_test_pred)
        test_mae = mean_absolute_error(y_test, y_test_pred)
        
        # Ensemble test evaluation
        ensemble_model = ensemble_results['base_models'][min(ensemble_results['model_scores'].keys(), 
                                                           key=lambda k: ensemble_results['model_scores'][k])]
        y_test_pred_ensemble = ensemble_model.predict(X_test)
        test_mse_ensemble = mean_squared_error(y_test, y_test_pred_ensemble)
        
        results['final_evaluation'] = {
            'single_model_mse': test_mse,
            'single_model_mae': test_mae,
            'ensemble_mse': test_mse_ensemble,
            'best_approach': 'ensemble' if test_mse_ensemble < test_mse else 'single'
        }
        
        logger.info(f"✅ Comprehensive training completed")
        logger.info(f"   Single model test MSE: {test_mse:.6f}")
        logger.info(f"   Ensemble test MSE: {test_mse_ensemble:.6f}")
        logger.info(f"   Best approach: {results['final_evaluation']['best_approach']}")
        
        return results

def main():
    """Demonstration of advanced training strategies"""
    print("🎯 ADVANCED TRAINING STRATEGIES DEMO")
    print("=" * 50)
    
    # Create sample time series data
    np.random.seed(42)
    n_samples = 1000
    n_features = 20
    
    # Generate time series with trend and seasonality
    time = np.arange(n_samples)
    trend = 0.001 * time
    seasonality = 0.1 * np.sin(2 * np.pi * time / 50)
    noise = np.random.randn(n_samples) * 0.1
    
    X = np.random.randn(n_samples, n_features)
    y = trend + seasonality + X[:, 0] * 0.5 + X[:, 1] * 0.3 + noise
    
    # Split data (time series split)
    train_size = int(0.6 * n_samples)
    val_size = int(0.2 * n_samples)
    
    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]
    
    print(f"📊 Data splits:")
    print(f"  Training: {X_train.shape}")
    print(f"  Validation: {X_val.shape}")
    print(f"  Test: {X_test.shape}")
    
    # Run comprehensive training pipeline
    trainer = AdvancedTrainingStrategies()
    results = trainer.comprehensive_training_pipeline(
        X_train, y_train, X_val, y_val, X_test, y_test
    )
    
    print(f"\n🏆 TRAINING RESULTS SUMMARY:")
    print(f"Cross-validation MSE: {results['cv_results']['mean_mse']:.6f}")
    print(f"Bayesian optimization MSE: {results['bayes_results']['best_score']:.6f}")
    print(f"Ensemble MSE: {results['ensemble_results']['best_ensemble_score']:.6f}")
    print(f"Final test MSE: {results['final_evaluation']['single_model_mse']:.6f}")
    print(f"Best approach: {results['final_evaluation']['best_approach']}")
    
    print(f"\n✅ Advanced training strategies demonstration completed!")

if __name__ == "__main__":
    main()
