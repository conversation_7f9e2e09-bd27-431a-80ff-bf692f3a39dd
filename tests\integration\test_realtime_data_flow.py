"""
Real-time Data Flow Integration Tests for NORYON V2

Tests the complete data pipeline from market data ingestion through AI analysis 
to API responses, ensuring real-time processing capabilities.
"""

import pytest
import asyncio
import json
import time
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

from src.services.market_simulator import MarketBroadcaster
from src.services.data_ingestion import DataIngestionService
from src.services.ai_service import AIService
from src.agents.agent_manager import AgentManager
from src.api.routes.market import router as market_router
from fastapi.testclient import TestClient
from fastapi import FastAPI


class TestRealtimeDataFlow:
    """Test suite for real-time data flow verification."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.test_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        self.data_points = []
        self.processing_times = []
        
    @pytest.mark.asyncio
    async def test_market_data_generation_pipeline(self):
        """Test continuous market data generation and distribution."""
        market_broadcaster = MarketBroadcaster()
        await market_broadcaster.start()
        
        # Collect data over time
        data_snapshots = []
        for i in range(5):
            await asyncio.sleep(1)
            snapshot = market_broadcaster.get_all_latest()
            data_snapshots.append({
                "timestamp": datetime.utcnow(),
                "data": snapshot.copy()
            })
        
        await market_broadcaster.stop()
        
        # Verify continuous data generation
        assert len(data_snapshots) == 5
        
        # Verify data is updating
        for i in range(1, len(data_snapshots)):
            prev_data = data_snapshots[i-1]["data"]
            curr_data = data_snapshots[i]["data"]
            
            # At least some symbols should have updated prices
            price_changes = 0
            for symbol in self.test_symbols:
                if symbol in prev_data and symbol in curr_data:
                    if prev_data[symbol]["price"] != curr_data[symbol]["price"]:
                        price_changes += 1
            
            # Allow for some stability but expect some changes
            assert price_changes >= 0  # Prices may stay same briefly

    @pytest.mark.asyncio
    async def test_data_ingestion_pipeline(self, mock_clickhouse, mock_redis):
        """Test real-time data ingestion and storage pipeline."""
        with patch('src.services.data_ingestion.get_client', return_value=mock_clickhouse), \
             patch('src.db.redis.get_client', return_value=mock_redis):
            
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            
            # Simulate real-time tick data stream
            tick_stream = []
            for i in range(10):
                tick = {
                    "symbol": "BTCUSDT",
                    "price": 45000.0 + (i * 10),  # Simulate price movement
                    "volume": 1000.0 + (i * 100),
                    "bid": 44995.0 + (i * 10),
                    "ask": 45005.0 + (i * 10),
                    "timestamp": datetime.utcnow() + timedelta(seconds=i)
                }
                tick_stream.append(tick)
            
            # Process ticks in real-time simulation
            processing_times = []
            for tick in tick_stream:
                start_time = time.perf_counter()
                await data_ingestion.ingest_tick(tick)
                end_time = time.perf_counter()
                processing_times.append(end_time - start_time)
            
            # Verify performance requirements
            avg_processing_time = sum(processing_times) / len(processing_times)
            max_processing_time = max(processing_times)
            
            # Should process ticks quickly (< 10ms average, < 50ms max)
            assert avg_processing_time < 0.01  # 10ms
            assert max_processing_time < 0.05   # 50ms
            
            # Verify all ticks were processed
            assert mock_clickhouse.execute.call_count == len(tick_stream)

    @pytest.mark.asyncio
    async def test_ai_analysis_pipeline(self, mock_ai_service):
        """Test real-time AI analysis pipeline."""
        with patch('src.services.ai_service.ai_service', mock_ai_service):
            
            # Simulate market data stream
            market_data_stream = []
            for i in range(5):
                data = {
                    "symbol": "BTCUSDT",
                    "price": 45000.0 + (i * 100),
                    "volume": 1000000.0,
                    "change_24h": 2.5 + (i * 0.5),
                    "timestamp": datetime.utcnow() + timedelta(seconds=i)
                }
                market_data_stream.append(data)
            
            # Process AI analysis in real-time
            analysis_results = []
            analysis_times = []
            
            for data in market_data_stream:
                start_time = time.perf_counter()
                analysis = await mock_ai_service.analyze_market_data(
                    data["symbol"], 
                    data
                )
                end_time = time.perf_counter()
                
                analysis_results.append(analysis)
                analysis_times.append(end_time - start_time)
            
            # Verify AI analysis performance
            avg_analysis_time = sum(analysis_times) / len(analysis_times)
            max_analysis_time = max(analysis_times)
            
            # AI analysis should be fast (< 100ms average, < 500ms max)
            assert avg_analysis_time < 0.1   # 100ms
            assert max_analysis_time < 0.5    # 500ms
            
            # Verify all analyses completed
            assert len(analysis_results) == len(market_data_stream)
            assert all(result is not None for result in analysis_results)

    @pytest.mark.asyncio
    async def test_agent_coordination_pipeline(self, mock_ai_service, mock_redis):
        """Test real-time agent coordination and communication."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup mock market data stream
            market_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 3.0, "volume": 1000000.0},
                "ETHUSDT": {"price": 2800.0, "change_24h": 2.5, "volume": 500000.0}
            }
            mock_broadcaster.get_all_latest.return_value = market_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Let agents process data for a few cycles
            await asyncio.sleep(3)
            
            # Verify agents are coordinating
            status = agent_manager.get_agent_status()
            
            # All agents should be running
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            # Test agent communication
            coordination_messages = []
            
            # Simulate significant market movement
            market_data["BTCUSDT"]["change_24h"] = 10.0  # Large movement
            mock_broadcaster.get_all_latest.return_value = market_data
            
            # Allow time for agents to react
            await asyncio.sleep(2)
            
            # Verify AI service was called for analysis
            assert mock_ai_service.analyze_market_data.call_count > 0
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_api_response_pipeline(self, mock_redis, mock_clickhouse, mock_ai_service):
        """Test real-time API response pipeline."""
        app = FastAPI()
        app.include_router(market_router)
        
        with patch('src.api.routes.market.redis_client', mock_redis), \
             patch('src.api.routes.market.get_client', return_value=mock_clickhouse), \
             patch('src.api.routes.market.ai_service', mock_ai_service):
            
            # Setup mock data
            mock_tick_data = {
                "symbol": "BTCUSDT",
                "last": 45000.0,
                "volume": 1000000.0,
                "ts": datetime.utcnow().isoformat()
            }
            mock_redis.get.return_value = json.dumps(mock_tick_data)
            mock_ai_service.analyze_market_data.return_value = "Strong bullish momentum"
            
            client = TestClient(app)
            
            # Test API response times
            response_times = []
            
            for i in range(10):
                start_time = time.perf_counter()
                response = client.get("/market/ai-analysis/BTCUSDT")
                end_time = time.perf_counter()
                
                response_times.append(end_time - start_time)
                assert response.status_code == 200
                
                data = response.json()
                assert "ai_analysis" in data
                assert "market_data" in data
                assert data["symbol"] == "BTCUSDT"
            
            # Verify API performance
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            # API should respond quickly (< 50ms average, < 200ms max)
            assert avg_response_time < 0.05   # 50ms
            assert max_response_time < 0.2     # 200ms

    @pytest.mark.asyncio
    async def test_end_to_end_data_flow(self, mock_redis, mock_clickhouse, mock_ai_service):
        """Test complete end-to-end data flow pipeline."""
        with patch('src.db.redis.get_client', return_value=mock_redis), \
             patch('src.db.clickhouse.get_client', return_value=mock_clickhouse), \
             patch('src.services.ai_service.ai_service', mock_ai_service):
            
            # 1. Market Data Generation
            market_broadcaster = MarketBroadcaster()
            await market_broadcaster.start()
            
            # 2. Data Ingestion
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            
            # 3. Simulate complete pipeline
            pipeline_times = []
            
            for i in range(5):
                pipeline_start = time.perf_counter()
                
                # Get market data
                market_data = market_broadcaster.get_all_latest()
                
                # Process first symbol
                if market_data:
                    symbol = list(market_data.keys())[0]
                    tick_data = market_data[symbol]
                    
                    # Ingest data
                    await data_ingestion.ingest_tick({
                        "symbol": symbol,
                        "price": tick_data["price"],
                        "volume": tick_data["volume"],
                        "timestamp": datetime.utcnow()
                    })
                    
                    # Cache in Redis
                    await mock_redis.setex(
                        f"tick:latest:{symbol}",
                        300,
                        json.dumps(tick_data, default=str)
                    )
                    
                    # AI Analysis
                    analysis = await mock_ai_service.analyze_market_data(symbol, tick_data)
                    
                    pipeline_end = time.perf_counter()
                    pipeline_times.append(pipeline_end - pipeline_start)
                
                await asyncio.sleep(0.5)  # Brief pause between cycles
            
            await market_broadcaster.stop()
            
            # Verify end-to-end performance
            if pipeline_times:
                avg_pipeline_time = sum(pipeline_times) / len(pipeline_times)
                max_pipeline_time = max(pipeline_times)
                
                # Complete pipeline should be fast (< 100ms average, < 500ms max)
                assert avg_pipeline_time < 0.1   # 100ms
                assert max_pipeline_time < 0.5    # 500ms
                
                # Verify all components were called
                assert mock_clickhouse.execute.call_count > 0
                assert mock_redis.setex.call_count > 0
                assert mock_ai_service.analyze_market_data.call_count > 0

    @pytest.mark.asyncio
    async def test_data_consistency_pipeline(self, mock_redis, mock_clickhouse):
        """Test data consistency across the pipeline."""
        with patch('src.db.redis.get_client', return_value=mock_redis), \
             patch('src.db.clickhouse.get_client', return_value=mock_clickhouse):
            
            # Test data consistency between Redis and ClickHouse
            test_data = {
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 1000000.0,
                "timestamp": datetime.utcnow()
            }
            
            # Store in Redis
            await mock_redis.setex(
                f"tick:latest:{test_data['symbol']}",
                300,
                json.dumps(test_data, default=str)
            )
            
            # Store in ClickHouse
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            await data_ingestion.ingest_tick(test_data)
            
            # Verify both storage calls were made
            mock_redis.setex.assert_called_once()
            mock_clickhouse.execute.assert_called()
            
            # Verify data integrity
            redis_call_args = mock_redis.setex.call_args[0]
            assert redis_call_args[0] == f"tick:latest:{test_data['symbol']}"
            
            stored_data = json.loads(redis_call_args[2])
            assert stored_data["symbol"] == test_data["symbol"]
            assert stored_data["price"] == test_data["price"]

    @pytest.mark.asyncio
    async def test_pipeline_error_recovery(self, mock_redis, mock_clickhouse, mock_ai_service):
        """Test pipeline behavior during component failures."""
        # Test Redis failure recovery
        mock_redis.setex.side_effect = Exception("Redis connection failed")
        
        with patch('src.db.redis.get_client', return_value=mock_redis):
            # Pipeline should continue despite Redis failure
            try:
                await mock_redis.setex("test_key", 300, "test_value")
                assert False, "Should have raised exception"
            except Exception as e:
                assert "Redis connection failed" in str(e)
        
        # Test AI service failure recovery
        mock_ai_service.analyze_market_data.side_effect = Exception("AI service timeout")
        
        try:
            result = await mock_ai_service.analyze_market_data("BTCUSDT", {})
            assert False, "Should have raised exception"
        except Exception as e:
            assert "AI service timeout" in str(e)
        
        # Test ClickHouse failure recovery
        mock_clickhouse.execute.side_effect = Exception("ClickHouse connection failed")
        
        with patch('src.db.clickhouse.get_client', return_value=mock_clickhouse):
            try:
                mock_clickhouse.execute("SELECT 1")
                assert False, "Should have raised exception"
            except Exception as e:
                assert "ClickHouse connection failed" in str(e)

    @pytest.mark.asyncio
    async def test_pipeline_throughput(self, mock_redis, mock_clickhouse):
        """Test pipeline throughput under load."""
        with patch('src.db.redis.get_client', return_value=mock_redis), \
             patch('src.db.clickhouse.get_client', return_value=mock_clickhouse):
            
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            
            # Generate high-volume test data
            test_data_batch = []
            for i in range(100):
                tick = {
                    "symbol": f"TEST{i % 10}USDT",  # 10 different symbols
                    "price": 1000.0 + i,
                    "volume": 1000.0,
                    "timestamp": datetime.utcnow() + timedelta(microseconds=i*1000)
                }
                test_data_batch.append(tick)
            
            # Process batch and measure throughput
            start_time = time.perf_counter()
            
            # Process in parallel batches
            batch_size = 10
            tasks = []
            for i in range(0, len(test_data_batch), batch_size):
                batch = test_data_batch[i:i+batch_size]
                task = asyncio.create_task(data_ingestion.ingest_batch(batch))
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            end_time = time.perf_counter()
            total_time = end_time - start_time
            throughput = len(test_data_batch) / total_time
            
            # Should process at least 100 ticks/second
            assert throughput > 100, f"Throughput too low: {throughput} ticks/second"
            
            # Verify all data was processed
            expected_calls = len(test_data_batch) // batch_size
            assert mock_clickhouse.execute.call_count >= expected_calls
