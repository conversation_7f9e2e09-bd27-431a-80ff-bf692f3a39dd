#!/usr/bin/env python3
"""
🚀 UNIFIED AI TRADING SYSTEM
Consolidated system with unified API layer, combining best components
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import joblib
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# Import our core components
try:
    from advanced_model_architectures import AdvancedModelArchitectures
except ImportError:
    AdvancedModelArchitectures = None
try:
    from advanced_risk_management import AdvancedRiskManager, RiskLimits
except ImportError:
    AdvancedRiskManager = None
    RiskLimits = None
try:
    from advanced_feature_engineering import AdvancedFeatureEngineering
except ImportError:
    AdvancedFeatureEngineering = None
try:
    from advanced_training_strategies import AdvancedTrainingStrategies
except ImportError:
    AdvancedTrainingStrategies = None

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("UnifiedTradingSystem")

@dataclass
class SystemConfig:
    """System configuration settings"""
    initial_capital: float = 10000.0
    max_position_size: float = 0.15  # 15% max position
    risk_tolerance: float = 0.02     # 2% daily risk
    data_collection_interval: int = 60  # seconds
    api_timeout: float = 10.0
    max_concurrent_requests: int = 5
    database_path: str = "unified_trading_system.db"
    log_level: str = "INFO"
    paper_trading: bool = True

@dataclass
class TradingSignal:
    """Trading signal structure"""
    symbol: str
    signal: str  # BUY, SELL, HOLD
    confidence: float
    prediction: float
    timestamp: str
    reasoning: str
    risk_score: float
    position_size: float

@dataclass
class Portfolio:
    """Portfolio state structure"""
    cash: float
    positions: Dict[str, Dict[str, float]]
    total_value: float
    unrealized_pnl: float
    realized_pnl: float
    trade_count: int
    win_rate: float
    sharpe_ratio: float
    max_drawdown: float

class UnifiedTradingSystem:
    """Unified AI Trading System with comprehensive functionality"""
    
    def __init__(self, config: SystemConfig = None):
        self.config = config or SystemConfig()
        self.db_path = self.config.database_path
        self.session = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Core components (with graceful fallbacks)
        self.model_architect = AdvancedModelArchitectures() if AdvancedModelArchitectures else None
        self.risk_manager = AdvancedRiskManager() if AdvancedRiskManager else None
        self.feature_engineer = AdvancedFeatureEngineering() if AdvancedFeatureEngineering else None
        self.training_strategies = AdvancedTrainingStrategies() if AdvancedTrainingStrategies else None
        
        # System state
        self.portfolio = Portfolio(
            cash=self.config.initial_capital,
            positions={},
            total_value=self.config.initial_capital,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            trade_count=0,
            win_rate=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0
        )
        
        # Market data
        self.market_data = {}
        self.price_history = {}
        self.signals_history = []
        self.trades_history = []
        
        # Performance tracking
        self.performance_metrics = {}
        self.error_log = []
        self.system_status = "INITIALIZING"
        
        # API endpoints
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        
        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the unified system"""
        try:
            logger.info("🚀 Initializing Unified Trading System...")
            
            # Initialize database
            self._initialize_database()
            
            # Initialize components
            self._initialize_components()
            
            # Set up monitoring
            self._setup_monitoring()
            
            self.system_status = "READY"
            logger.info("✅ Unified Trading System initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            self.system_status = "ERROR"
            self.error_log.append({"timestamp": datetime.now().isoformat(), "error": str(e)})
    
    def _initialize_database(self):
        """Initialize unified database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Market data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume REAL,
                change_24h REAL,
                volatility REAL,
                source TEXT
            )
        ''')
        
        # Trading signals table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                signal TEXT NOT NULL,
                confidence REAL,
                prediction REAL,
                reasoning TEXT,
                risk_score REAL,
                position_size REAL
            )
        ''')
        
        # Trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                quantity REAL,
                price REAL,
                value REAL,
                commission REAL,
                pnl REAL,
                portfolio_value REAL
            )
        ''')
        
        # Portfolio snapshots table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                cash REAL,
                positions_value REAL,
                total_value REAL,
                unrealized_pnl REAL,
                realized_pnl REAL,
                trade_count INTEGER,
                win_rate REAL,
                sharpe_ratio REAL,
                max_drawdown REAL
            )
        ''')
        
        # System metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                metric_name TEXT NOT NULL,
                metric_value REAL,
                component TEXT
            )
        ''')
        
        # Error log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS error_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                error_type TEXT,
                error_message TEXT,
                component TEXT,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Database initialized: {self.db_path}")
    
    def _initialize_components(self):
        """Initialize all system components"""
        # Initialize risk manager with portfolio data
        portfolio_data = {
            "total_value": self.portfolio.total_value,
            "positions": self.portfolio.positions
        }
        
        historical_data = [{"date": f"2024-01-{i:02d}", "returns": np.random.normal(0.001, 0.02)} 
                          for i in range(1, 31)]
        
        if self.risk_manager:
            self.risk_manager.initialize_risk_system(portfolio_data, historical_data)
        
        logger.info("✅ Components initialized")
    
    def _setup_monitoring(self):
        """Setup system monitoring and alerting"""
        self.monitoring_metrics = {
            "api_calls_per_minute": 0,
            "error_rate": 0.0,
            "system_uptime": 0.0,
            "memory_usage": 0.0,
            "last_heartbeat": datetime.now().isoformat()
        }
        
        logger.info("✅ Monitoring setup complete")
    
    # =============================================================================
    # UNIFIED API LAYER
    # =============================================================================
    
    async def start_system(self, duration_minutes: int = None):
        """Start the unified trading system"""
        if self.system_status != "READY":
            raise RuntimeError("System not ready. Check initialization.")
        
        logger.info(f"🚀 Starting Unified Trading System for {duration_minutes or 'indefinite'} minutes")
        
        self.system_status = "RUNNING"
        self.start_time = time.time()
        
        try:
            # Initialize HTTP session
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.api_timeout)) as session:
                self.session = session
                
                # Main trading loop
                end_time = self.start_time + (duration_minutes * 60) if duration_minutes else None
                
                while self.system_status == "RUNNING":
                    loop_start = time.time()
                    
                    try:
                        # 1. Collect market data
                        await self.collect_market_data()
                        
                        # 2. Generate trading signals
                        signals = await self.generate_trading_signals()
                        
                        # 3. Execute trades
                        if signals:
                            await self.execute_trades(signals)
                        
                        # 4. Update portfolio
                        await self.update_portfolio()
                        
                        # 5. Monitor risk
                        await self.monitor_risk()
                        
                        # 6. Log performance
                        await self.log_performance()
                        
                        # Check if we should stop
                        if end_time and time.time() >= end_time:
                            break
                        
                        # Wait for next iteration
                        loop_duration = time.time() - loop_start
                        wait_time = max(0, self.config.data_collection_interval - loop_duration)
                        await asyncio.sleep(wait_time)
                        
                    except Exception as e:
                        logger.error(f"❌ Error in trading loop: {e}")
                        await self.handle_error(e)
                        await asyncio.sleep(5)  # Wait before retrying
                
        except Exception as e:
            logger.error(f"❌ System error: {e}")
            self.system_status = "ERROR"
            await self.handle_error(e)
        
        finally:
            self.system_status = "STOPPED"
            logger.info("🛑 Unified Trading System stopped")
    
    async def collect_market_data(self) -> Dict[str, Any]:
        """Collect real-time market data"""
        try:
            # CoinGecko API for crypto prices
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': 'bitcoin,ethereum,cardano,solana,polkadot',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Map to our symbols
                    symbol_map = {
                        'bitcoin': 'BTCUSDT',
                        'ethereum': 'ETHUSDT',
                        'cardano': 'ADAUSDT',
                        'solana': 'SOLUSDT',
                        'polkadot': 'DOTUSDT'
                    }
                    
                    # Process and store data
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    for coin_id, coin_data in data.items():
                        if coin_id in symbol_map:
                            symbol = symbol_map[coin_id]
                            
                            market_data = {
                                'timestamp': datetime.now().isoformat(),
                                'symbol': symbol,
                                'close_price': coin_data['usd'],
                                'volume': coin_data.get('usd_24h_vol', 0),
                                'change_24h': coin_data.get('usd_24h_change', 0),
                                'source': 'coingecko'
                            }
                            
                            # Store in database
                            cursor.execute('''
                                INSERT INTO market_data 
                                (timestamp, symbol, close_price, volume, change_24h, source)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (market_data['timestamp'], market_data['symbol'], 
                                 market_data['close_price'], market_data['volume'],
                                 market_data['change_24h'], market_data['source']))
                            
                            # Update in-memory cache
                            self.market_data[symbol] = market_data
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Collected market data for {len(self.market_data)} symbols")
                    return self.market_data
                    
                else:
                    logger.error(f"❌ API error: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"❌ Market data collection error: {e}")
            await self.handle_error(e)
            return {}
    
    async def generate_trading_signals(self) -> List[TradingSignal]:
        """Generate AI-powered trading signals"""
        try:
            if not self.market_data:
                logger.warning("No market data available for signal generation")
                return []
            
            signals = []
            
            # Get latest market data from database
            conn = sqlite3.connect(self.db_path)
            
            for symbol in self.crypto_pairs:
                try:
                    # Get recent price data
                    df = pd.read_sql_query('''
                        SELECT * FROM market_data 
                        WHERE symbol = ? 
                        ORDER BY timestamp DESC 
                        LIMIT 100
                    ''', conn, params=(symbol,))
                    
                    if len(df) < 10:
                        continue
                    
                    # Feature engineering (with fallback)
                    if self.feature_engineer and hasattr(self.feature_engineer, 'create_all_features'):
                        features_df = self.feature_engineer.create_all_features(df)
                        if features_df.empty:
                            continue
                        
                        # Get latest features
                        latest_features = features_df.iloc[-1]
                        
                        # Simple signal generation logic
                        prediction = self._calculate_prediction(latest_features)
                        confidence = self._calculate_confidence(latest_features)
                        risk_score = self._calculate_risk_score(symbol, latest_features)
                    else:
                        # Fallback to simple calculation using basic market data
                        data = self.market_data[symbol]
                        change_24h = data.get('change_24h', 0)
                        volume = data.get('volume', 0)
                        
                        # Simple calculations
                        prediction = change_24h / 100  # Convert percentage to decimal
                        confidence = min(1.0, abs(prediction) * 10 + 0.4)
                        risk_score = min(1.0, abs(prediction) * 5 + 0.2)
                        
                        # Generate signal
                        if prediction > 0.005 and confidence > 0.6:
                            signal_type = "BUY"
                        elif prediction < -0.005 and confidence > 0.6:
                            signal_type = "SELL"
                        else:
                            signal_type = "HOLD"
                        
                        # Calculate position size
                        position_size = self._calculate_position_size(symbol, confidence, risk_score)
                        
                    # Generate signal
                    if prediction > 0.005 and confidence > 0.6:
                        signal_type = "BUY"
                    elif prediction < -0.005 and confidence > 0.6:
                        signal_type = "SELL"
                    else:
                        signal_type = "HOLD"
                    
                    # Calculate position size
                    position_size = self._calculate_position_size(symbol, confidence, risk_score)
                    
                    signal = TradingSignal(
                        symbol=symbol,
                        signal=signal_type,
                        confidence=confidence,
                        prediction=prediction,
                        timestamp=datetime.now().isoformat(),
                        reasoning=f"Simple momentum analysis based on 24h change",
                        risk_score=risk_score,
                        position_size=position_size
                    )
                    
                    signals.append(signal)
                    
                    # Store signal in database
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO trading_signals 
                        (timestamp, symbol, signal, confidence, prediction, reasoning, risk_score, position_size)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (signal.timestamp, signal.symbol, signal.signal, signal.confidence,
                          signal.prediction, signal.reasoning, signal.risk_score, signal.position_size))
                
                except Exception as e:
                    logger.error(f"❌ Error generating signal for {symbol}: {e}")
                    continue
            
            conn.commit()
            conn.close()
            
            self.signals_history.extend(signals)
            logger.info(f"✅ Generated {len(signals)} trading signals")
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ Signal generation error: {e}")
            await self.handle_error(e)
            return []
    
    def _calculate_prediction(self, features: pd.Series) -> float:
        """Calculate prediction score from features"""
        # Simple prediction logic (can be enhanced with trained models)
        momentum = features.get('price_momentum', 0)
        rsi = features.get('rsi', 50)
        volume_ratio = features.get('volume_ratio', 1)
        
        # Combine signals
        prediction = momentum * 0.4 + (rsi - 50) / 1000 + (volume_ratio - 1) * 0.1
        return np.clip(prediction, -0.1, 0.1)
    
    def _calculate_confidence(self, features: pd.Series) -> float:
        """Calculate confidence score"""
        # Confidence based on feature consistency
        momentum = abs(features.get('price_momentum', 0))
        volatility = features.get('volatility', 0.02)
        
        confidence = momentum * 10 + (1 - volatility) * 0.5
        return np.clip(confidence, 0.1, 1.0)
    
    def _calculate_risk_score(self, symbol: str, features: pd.Series) -> float:
        """Calculate risk score for the trade"""
        volatility = features.get('volatility', 0.02)
        volume_ratio = features.get('volume_ratio', 1)
        
        # Higher volatility = higher risk
        risk_score = volatility * 5 + (1 / max(volume_ratio, 0.1)) * 0.1
        return np.clip(risk_score, 0.1, 1.0)
    
    def _calculate_position_size(self, symbol: str, confidence: float, risk_score: float) -> float:
        """Calculate optimal position size"""
        # Kelly criterion inspired sizing
        max_position = self.config.max_position_size
        risk_adjusted_size = max_position * confidence / max(risk_score, 0.1)
        
        return min(risk_adjusted_size, max_position)
    
    async def execute_trades(self, signals: List[TradingSignal]):
        """Execute trading signals (paper trading)"""
        if not self.config.paper_trading:
            logger.warning("Live trading not implemented yet")
            return
        
        executed_trades = []
        
        for signal in signals:
            if signal.signal == "HOLD":
                continue
            
            try:
                # Get current market price
                current_price = self.market_data.get(signal.symbol, {}).get('close_price')
                if not current_price:
                    continue
                
                # Calculate trade parameters
                trade_value = self.portfolio.cash * signal.position_size
                
                if signal.signal == "BUY" and trade_value > 50:  # Min $50 trade
                    quantity = trade_value / current_price
                    
                    if trade_value <= self.portfolio.cash:
                        # Execute buy order
                        self.portfolio.cash -= trade_value
                        
                        if signal.symbol not in self.portfolio.positions:
                            self.portfolio.positions[signal.symbol] = {"quantity": 0, "avg_price": 0}
                        
                        pos = self.portfolio.positions[signal.symbol]
                        total_quantity = pos["quantity"] + quantity
                        total_cost = (pos["quantity"] * pos["avg_price"]) + trade_value
                        
                        self.portfolio.positions[signal.symbol] = {
                            "quantity": total_quantity,
                            "avg_price": total_cost / total_quantity if total_quantity > 0 else 0
                        }
                        
                        trade = {
                            "timestamp": datetime.now().isoformat(),
                            "symbol": signal.symbol,
                            "action": "BUY",
                            "quantity": quantity,
                            "price": current_price,
                            "value": trade_value,
                            "commission": trade_value * 0.001,  # 0.1% commission
                            "pnl": 0,
                            "portfolio_value": self._calculate_portfolio_value()
                        }
                        
                        executed_trades.append(trade)
                        self.portfolio.trade_count += 1
                        
                        logger.info(f"🟢 BUY {quantity:.6f} {signal.symbol} @ ${current_price:.4f}")
                
                elif signal.signal == "SELL" and signal.symbol in self.portfolio.positions:
                    pos = self.portfolio.positions[signal.symbol]
                    
                    if pos["quantity"] > 0:
                        # Sell portion based on signal strength
                        sell_ratio = min(0.5, signal.confidence)
                        sell_quantity = pos["quantity"] * sell_ratio
                        sell_value = sell_quantity * current_price
                        
                        # Calculate P&L
                        pnl = (current_price - pos["avg_price"]) * sell_quantity
                        
                        # Execute sell order
                        self.portfolio.cash += sell_value
                        self.portfolio.realized_pnl += pnl
                        pos["quantity"] -= sell_quantity
                        
                        if pos["quantity"] < 0.000001:
                            del self.portfolio.positions[signal.symbol]
                        
                        trade = {
                            "timestamp": datetime.now().isoformat(),
                            "symbol": signal.symbol,
                            "action": "SELL",
                            "quantity": sell_quantity,
                            "price": current_price,
                            "value": sell_value,
                            "commission": sell_value * 0.001,
                            "pnl": pnl,
                            "portfolio_value": self._calculate_portfolio_value()
                        }
                        
                        executed_trades.append(trade)
                        self.portfolio.trade_count += 1
                        
                        logger.info(f"🔴 SELL {sell_quantity:.6f} {signal.symbol} @ ${current_price:.4f} (P&L: ${pnl:.2f})")
                
            except Exception as e:
                logger.error(f"❌ Error executing trade for {signal.symbol}: {e}")
                continue
        
        # Store trades in database
        if executed_trades:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for trade in executed_trades:
                cursor.execute('''
                    INSERT INTO trades 
                    (timestamp, symbol, action, quantity, price, value, commission, pnl, portfolio_value)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (trade["timestamp"], trade["symbol"], trade["action"], trade["quantity"],
                      trade["price"], trade["value"], trade["commission"], trade["pnl"], trade["portfolio_value"]))
            
            conn.commit()
            conn.close()
            
            self.trades_history.extend(executed_trades)
            logger.info(f"✅ Executed {len(executed_trades)} trades")
    
    def _calculate_portfolio_value(self) -> float:
        """Calculate current portfolio value"""
        positions_value = 0
        
        for symbol, position in self.portfolio.positions.items():
            current_price = self.market_data.get(symbol, {}).get('close_price', 0)
            positions_value += position["quantity"] * current_price
        
        return self.portfolio.cash + positions_value
    
    async def update_portfolio(self):
        """Update portfolio metrics"""
        try:
            # Calculate current values
            self.portfolio.total_value = self._calculate_portfolio_value()
            
            # Calculate unrealized P&L
            unrealized_pnl = 0
            for symbol, position in self.portfolio.positions.items():
                current_price = self.market_data.get(symbol, {}).get('close_price', 0)
                unrealized_pnl += (current_price - position["avg_price"]) * position["quantity"]
            
            self.portfolio.unrealized_pnl = unrealized_pnl
            
            # Calculate performance metrics
            if self.portfolio.trade_count > 0:
                self._calculate_performance_metrics()
            
            # Store portfolio snapshot
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO portfolio_snapshots 
                (timestamp, cash, positions_value, total_value, unrealized_pnl, realized_pnl, 
                 trade_count, win_rate, sharpe_ratio, max_drawdown)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                self.portfolio.cash,
                self.portfolio.total_value - self.portfolio.cash,
                self.portfolio.total_value,
                self.portfolio.unrealized_pnl,
                self.portfolio.realized_pnl,
                self.portfolio.trade_count,
                self.portfolio.win_rate,
                self.portfolio.sharpe_ratio,
                self.portfolio.max_drawdown
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Portfolio update error: {e}")
            await self.handle_error(e)
    
    def _calculate_performance_metrics(self):
        """Calculate portfolio performance metrics"""
        if len(self.trades_history) < 2:
            return
        
        # Win rate
        profitable_trades = sum(1 for trade in self.trades_history if trade.get("pnl", 0) > 0)
        self.portfolio.win_rate = profitable_trades / len(self.trades_history)
        
        # Sharpe ratio (simplified)
        if len(self.trades_history) >= 10:
            returns = [trade.get("pnl", 0) / self.config.initial_capital for trade in self.trades_history]
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            self.portfolio.sharpe_ratio = mean_return / std_return if std_return > 0 else 0
        
        # Max drawdown
        portfolio_values = [trade.get("portfolio_value", self.config.initial_capital) for trade in self.trades_history]
        peak = self.config.initial_capital
        max_dd = 0
        
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_dd:
                max_dd = drawdown
        
        self.portfolio.max_drawdown = max_dd
    
    async def monitor_risk(self):
        """Monitor portfolio risk"""
        try:
            # Update risk manager with current portfolio
            portfolio_data = {
                "total_value": self.portfolio.total_value,
                "positions": self.portfolio.positions
            }
            
            # Calculate risk metrics
            risk_metrics = self.risk_manager.calculate_comprehensive_risk_metrics()
            
            # Check for risk limit violations
            if "risk_limits_status" in risk_metrics:
                status = risk_metrics["risk_limits_status"]
                if status.get("violation_count", 0) > 0:
                    logger.warning(f"⚠️ Risk limit violations: {status['violations']}")
            
            # Store risk metrics
            self.performance_metrics["risk"] = risk_metrics
            
        except Exception as e:
            logger.error(f"❌ Risk monitoring error: {e}")
            await self.handle_error(e)
    
    async def log_performance(self):
        """Log system performance metrics"""
        try:
            current_time = datetime.now().isoformat()
            
            # System metrics
            metrics = {
                "portfolio_value": self.portfolio.total_value,
                "total_return": (self.portfolio.total_value - self.config.initial_capital) / self.config.initial_capital,
                "trade_count": self.portfolio.trade_count,
                "win_rate": self.portfolio.win_rate,
                "sharpe_ratio": self.portfolio.sharpe_ratio,
                "max_drawdown": self.portfolio.max_drawdown,
                "system_uptime": time.time() - self.start_time if hasattr(self, 'start_time') else 0
            }
            
            # Store metrics in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for metric_name, metric_value in metrics.items():
                cursor.execute('''
                    INSERT INTO system_metrics (timestamp, metric_name, metric_value, component)
                    VALUES (?, ?, ?, ?)
                ''', (current_time, metric_name, metric_value, "system"))
            
            conn.commit()
            conn.close()
            
            # Update monitoring
            self.monitoring_metrics["last_heartbeat"] = current_time
            
            # Log summary
            if self.portfolio.trade_count > 0:
                logger.info(f"📊 Portfolio: ${self.portfolio.total_value:.2f} | "
                           f"Return: {metrics['total_return']:.2%} | "
                           f"Trades: {self.portfolio.trade_count} | "
                           f"Win Rate: {self.portfolio.win_rate:.1%}")
            
        except Exception as e:
            logger.error(f"❌ Performance logging error: {e}")
            await self.handle_error(e)
    
    async def handle_error(self, error: Exception, component: str = "system"):
        """Handle and log errors"""
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "component": component,
            "resolved": False
        }
        
        self.error_log.append(error_info)
        
        # Store in database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO error_log (timestamp, error_type, error_message, component, resolved)
                VALUES (?, ?, ?, ?, ?)
            ''', (error_info["timestamp"], error_info["error_type"], 
                  error_info["error_message"], error_info["component"], False))
            
            conn.commit()
            conn.close()
            
        except Exception as db_error:
            logger.error(f"❌ Failed to log error to database: {db_error}")
    
    def stop_system(self):
        """Stop the trading system"""
        self.system_status = "STOPPING"
        logger.info("🛑 Stopping Unified Trading System...")
    
    # =============================================================================
    # API ENDPOINTS
    # =============================================================================
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "system_status": self.system_status,
            "portfolio": asdict(self.portfolio),
            "active_positions": len(self.portfolio.positions),
            "recent_signals": len([s for s in self.signals_history if 
                                 (datetime.now() - datetime.fromisoformat(s.timestamp.replace('Z', '+00:00'))).seconds < 300]),
            "error_count": len(self.error_log),
            "monitoring_metrics": self.monitoring_metrics
        }
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        return {
            "portfolio": asdict(self.portfolio),
            "positions": self.portfolio.positions,
            "recent_trades": self.trades_history[-10:] if self.trades_history else [],
            "performance_metrics": self.performance_metrics
        }
    
    def get_trading_signals(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent trading signals"""
        return [asdict(signal) for signal in self.signals_history[-limit:]]
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics"""
        return self.performance_metrics.get("risk", {})
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system performance metrics"""
        return {
            "monitoring_metrics": self.monitoring_metrics,
            "performance_metrics": self.performance_metrics,
            "error_log": self.error_log[-10:],  # Last 10 errors
            "system_health": "HEALTHY" if len(self.error_log) < 5 else "DEGRADED"
        }


# =============================================================================
# MONITORING AND ALERTING SYSTEM
# =============================================================================

class SystemMonitor:
    """Real-time system monitoring and alerting"""
    
    def __init__(self, trading_system: UnifiedTradingSystem):
        self.trading_system = trading_system
        self.alerts = []
        self.thresholds = {
            "max_drawdown": 0.10,      # 10% max drawdown
            "error_rate": 0.05,        # 5% error rate
            "api_timeout_rate": 0.10,  # 10% API timeout rate
            "min_win_rate": 0.40,      # 40% minimum win rate
            "max_risk_score": 0.80     # 80% max risk score
        }
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """Check for system alerts"""
        alerts = []
        
        # Portfolio alerts
        if self.trading_system.portfolio.max_drawdown > self.thresholds["max_drawdown"]:
            alerts.append({
                "type": "RISK",
                "severity": "HIGH",
                "message": f"Maximum drawdown exceeded: {self.trading_system.portfolio.max_drawdown:.1%}",
                "timestamp": datetime.now().isoformat()
            })
        
        # Performance alerts
        if (self.trading_system.portfolio.trade_count > 10 and 
            self.trading_system.portfolio.win_rate < self.thresholds["min_win_rate"]):
            alerts.append({
                "type": "PERFORMANCE",
                "severity": "MEDIUM",
                "message": f"Low win rate: {self.trading_system.portfolio.win_rate:.1%}",
                "timestamp": datetime.now().isoformat()
            })
        
        # System health alerts
        if len(self.trading_system.error_log) > 10:
            alerts.append({
                "type": "SYSTEM",
                "severity": "MEDIUM",
                "message": f"High error count: {len(self.trading_system.error_log)} errors",
                "timestamp": datetime.now().isoformat()
            })
        
        self.alerts.extend(alerts)
        return alerts
    
    def get_health_score(self) -> float:
        """Calculate system health score (0-1)"""
        score = 1.0
        
        # Deduct for errors
        if len(self.trading_system.error_log) > 0:
            score -= min(0.3, len(self.trading_system.error_log) * 0.05)
        
        # Deduct for poor performance
        if self.trading_system.portfolio.max_drawdown > 0.05:
            score -= self.trading_system.portfolio.max_drawdown
        
        # Deduct for low win rate
        if (self.trading_system.portfolio.trade_count > 10 and 
            self.trading_system.portfolio.win_rate < 0.5):
            score -= (0.5 - self.trading_system.portfolio.win_rate)
        
        return max(0, score)


# =============================================================================
# MAIN EXECUTION
# =============================================================================

if __name__ == "__main__":
    # Configuration
    config = SystemConfig(
        initial_capital=10000.0,
        max_position_size=0.10,
        risk_tolerance=0.02,
        data_collection_interval=60,
        paper_trading=True
    )
    
    # Initialize system
    system = UnifiedTradingSystem(config)
    monitor = SystemMonitor(system)
    
    # Run system
    async def main():
        try:
            await system.start_system(duration_minutes=30)  # Run for 30 minutes
        except KeyboardInterrupt:
            logger.info("System interrupted by user")
        except Exception as e:
            logger.error(f"System error: {e}")
        finally:
            # Print final summary
            status = system.get_system_status()
            portfolio = system.get_portfolio_summary()
            
            print("\n" + "="*50)
            print("UNIFIED TRADING SYSTEM SUMMARY")
            print("="*50)
            print(f"Final Portfolio Value: ${portfolio['portfolio']['total_value']:.2f}")
            print(f"Total Return: {((portfolio['portfolio']['total_value'] - config.initial_capital) / config.initial_capital):.2%}")
            print(f"Total Trades: {portfolio['portfolio']['trade_count']}")
            print(f"Win Rate: {portfolio['portfolio']['win_rate']:.1%}")
            print(f"Max Drawdown: {portfolio['portfolio']['max_drawdown']:.1%}")
            print(f"System Health Score: {monitor.get_health_score():.1%}")
            print("="*50)
    
    # Run the system
    asyncio.run(main()) 