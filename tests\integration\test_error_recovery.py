"""
Error Recovery Integration Tests for NORYON V2

Tests graceful degradation and recovery mechanisms when components fail,
ensuring system resilience and fault tolerance.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any

from src.services.ai_service import AIService
from src.services.market_simulator import MarketBroadcaster
from src.agents.agent_manager import AgentManager
from src.db.database_manager import DatabaseManager
from src.core.orchestrator import SystemOrchestrator


class TestErrorRecovery:
    """Test suite for error recovery and fault tolerance verification."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.recovery_times = []
        self.error_counts = {}
        
    @pytest.mark.asyncio
    async def test_ai_service_failure_recovery(self):
        """Test AI service recovery from Ollama connection failures."""
        ai_service = AIService()
        
        # Test connection timeout recovery
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Simulate timeout
            mock_post.side_effect = asyncio.TimeoutError("Request timeout")
            
            result = await ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            
            # Should return fallback message, not crash
            assert result is not None
            assert "timeout" in result.lower()
            
            # Test recovery after timeout
            mock_response = AsyncMock()
            mock_response.json.return_value = {"response": "Analysis recovered"}
            mock_response.status = 200
            mock_post.side_effect = None
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            
            assert "recovered" in result.lower() or "analysis" in result.lower()

    @pytest.mark.asyncio
    async def test_database_connection_failure_recovery(self, mock_redis, mock_clickhouse):
        """Test database connection failure and recovery."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Simulate Redis connection failure
            mock_redis.ping.side_effect = Exception("Redis connection lost")
            mock_redis.setex.side_effect = Exception("Redis connection lost")
            
            # Test graceful handling of Redis failure
            try:
                await db_manager.cache_market_data("BTCUSDT", {"price": 45000.0})
            except Exception as e:
                assert "Redis connection lost" in str(e)
            
            # Test connection recovery
            mock_redis.ping.side_effect = None
            mock_redis.ping.return_value = True
            mock_redis.setex.side_effect = None
            mock_redis.setex.return_value = True
            
            # Should work after recovery
            await db_manager.cache_market_data("BTCUSDT", {"price": 45000.0})
            mock_redis.setex.assert_called()

    @pytest.mark.asyncio
    async def test_market_data_source_failure_recovery(self):
        """Test market data source failure and fallback mechanisms."""
        # Test Binance connector failure with simulator fallback
        with patch('src.services.binance_connector.ccxt.binance') as mock_binance:
            # Simulate Binance API failure
            mock_exchange = AsyncMock()
            mock_exchange.fetch_ticker.side_effect = Exception("API rate limit exceeded")
            mock_binance.return_value = mock_exchange
            
            # Market simulator should continue providing data
            market_broadcaster = MarketBroadcaster()
            await market_broadcaster.start()
            
            # Wait for data generation
            await asyncio.sleep(2)
            
            # Should still have market data from simulator
            latest_data = market_broadcaster.get_all_latest()
            assert len(latest_data) > 0
            
            await market_broadcaster.stop()

    @pytest.mark.asyncio
    async def test_agent_failure_recovery(self, mock_ai_service, mock_redis):
        """Test individual agent failure and recovery."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup mock market data
            mock_market_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 2.5, "volume": 1000000.0}
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Simulate AI service failure for one agent
            mock_ai_service.analyze_market_data.side_effect = Exception("AI model unavailable")
            
            # Let agents attempt to process with failure
            await asyncio.sleep(2)
            
            # Agents should still be running despite AI failure
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            # Simulate AI service recovery
            mock_ai_service.analyze_market_data.side_effect = None
            mock_ai_service.analyze_market_data.return_value = "Recovered analysis"
            
            # Let agents recover
            await asyncio.sleep(2)
            
            # Verify recovery
            assert mock_ai_service.analyze_market_data.call_count > 0
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_orchestrator_failure_recovery(self, mock_redis):
        """Test system orchestrator failure and recovery."""
        with patch('src.core.orchestrator.aioredis.from_url', return_value=mock_redis):
            
            # Mock agents
            mock_agents = {
                "market_watcher": AsyncMock(),
                "strategy_researcher": AsyncMock(),
                "risk_officer": AsyncMock(),
                "technical_analyst": AsyncMock()
            }
            
            mock_db_manager = AsyncMock()
            mock_exchange_manager = AsyncMock()
            mock_config = MagicMock()
            
            orchestrator = SystemOrchestrator(
                agents=mock_agents,
                db_manager=mock_db_manager,
                exchange_manager=mock_exchange_manager,
                config=mock_config
            )
            
            await orchestrator.initialize()
            await orchestrator.start()
            
            # Simulate message processing failure
            original_handle_message = orchestrator._handle_message
            orchestrator._handle_message = AsyncMock(side_effect=Exception("Message processing failed"))
            
            # Send test message
            from src.core.models import AgentMessage
            test_message = AgentMessage(
                source="test",
                target="market_watcher",
                message_type="test_message",
                content={"test": "data"},
                timestamp=datetime.utcnow()
            )
            
            await orchestrator.send_message(test_message)
            
            # Let message processor attempt to handle
            await asyncio.sleep(0.1)
            
            # Orchestrator should still be running
            assert orchestrator.running == True
            
            # Restore message handling
            orchestrator._handle_message = original_handle_message
            
            await orchestrator.stop()

    @pytest.mark.asyncio
    async def test_network_partition_recovery(self, mock_redis, mock_clickhouse):
        """Test recovery from network partition scenarios."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Simulate network partition (all external services fail)
            mock_redis.ping.side_effect = Exception("Network unreachable")
            mock_clickhouse.execute.side_effect = Exception("Network unreachable")
            
            # Test graceful degradation
            health_status = await db_manager.check_connection_health()
            
            # Should detect all failures
            assert health_status["redis"] == False
            assert health_status["clickhouse"] == False
            
            # Simulate network recovery
            mock_redis.ping.side_effect = None
            mock_redis.ping.return_value = True
            mock_clickhouse.execute.side_effect = None
            mock_clickhouse.execute.return_value = []
            
            # Test recovery
            health_status = await db_manager.check_connection_health()
            assert health_status["redis"] == True
            assert health_status["clickhouse"] == True

    @pytest.mark.asyncio
    async def test_memory_pressure_recovery(self, mock_ai_service):
        """Test system behavior under memory pressure."""
        # Simulate memory pressure by creating large data structures
        large_data_sets = []
        
        try:
            # Create memory pressure
            for i in range(10):
                large_data = {f"key_{j}": f"value_{j}" * 1000 for j in range(1000)}
                large_data_sets.append(large_data)
            
            # Test AI service under memory pressure
            result = await mock_ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            
            # Should still function
            assert result is not None
            
        finally:
            # Clean up memory
            large_data_sets.clear()

    @pytest.mark.asyncio
    async def test_concurrent_failure_recovery(self, mock_redis, mock_ai_service):
        """Test recovery from multiple concurrent failures."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup mock data
            mock_market_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 2.5, "volume": 1000000.0}
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Simulate multiple concurrent failures
            mock_ai_service.analyze_market_data.side_effect = Exception("AI service down")
            mock_redis.setex.side_effect = Exception("Redis connection lost")
            mock_broadcaster.get_all_latest.side_effect = Exception("Market data unavailable")
            
            # Let system attempt to operate with multiple failures
            await asyncio.sleep(2)
            
            # System should still be running (graceful degradation)
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            # Simulate gradual recovery
            mock_broadcaster.get_all_latest.side_effect = None
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            await asyncio.sleep(1)
            
            mock_redis.setex.side_effect = None
            mock_redis.setex.return_value = True
            await asyncio.sleep(1)
            
            mock_ai_service.analyze_market_data.side_effect = None
            mock_ai_service.analyze_market_data.return_value = "Recovered analysis"
            await asyncio.sleep(1)
            
            # Verify full recovery
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_cascading_failure_prevention(self, mock_redis, mock_clickhouse):
        """Test prevention of cascading failures."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Simulate initial failure in Redis
            mock_redis.ping.side_effect = Exception("Redis primary failure")
            
            # ClickHouse should remain operational
            mock_clickhouse.execute.return_value = []
            
            # Test that Redis failure doesn't cascade to ClickHouse
            health_status = await db_manager.check_connection_health()
            
            assert health_status["redis"] == False
            assert health_status["clickhouse"] == True  # Should remain healthy

    @pytest.mark.asyncio
    async def test_recovery_time_measurement(self, mock_ai_service):
        """Test measurement of recovery times."""
        recovery_times = []
        
        for i in range(3):
            # Simulate failure
            mock_ai_service.analyze_market_data.side_effect = Exception("Service unavailable")
            
            failure_start = time.perf_counter()
            
            # Attempt operation during failure
            try:
                await mock_ai_service.analyze_market_data("BTCUSDT", {"price": 45000.0})
            except Exception:
                pass
            
            # Simulate recovery
            mock_ai_service.analyze_market_data.side_effect = None
            mock_ai_service.analyze_market_data.return_value = "Service recovered"
            
            # Measure recovery time
            recovery_start = time.perf_counter()
            result = await mock_ai_service.analyze_market_data("BTCUSDT", {"price": 45000.0})
            recovery_end = time.perf_counter()
            
            recovery_time = recovery_end - recovery_start
            recovery_times.append(recovery_time)
            
            assert "recovered" in result.lower()
        
        # Recovery should be fast (< 100ms)
        avg_recovery_time = sum(recovery_times) / len(recovery_times)
        assert avg_recovery_time < 0.1

    @pytest.mark.asyncio
    async def test_error_rate_monitoring(self, mock_ai_service):
        """Test error rate monitoring and circuit breaker behavior."""
        error_count = 0
        success_count = 0
        
        # Simulate high error rate
        for i in range(10):
            if i < 7:  # 70% error rate
                mock_ai_service.analyze_market_data.side_effect = Exception("Service error")
                try:
                    await mock_ai_service.analyze_market_data("BTCUSDT", {"price": 45000.0})
                except Exception:
                    error_count += 1
            else:
                mock_ai_service.analyze_market_data.side_effect = None
                mock_ai_service.analyze_market_data.return_value = "Success"
                result = await mock_ai_service.analyze_market_data("BTCUSDT", {"price": 45000.0})
                if "success" in result.lower():
                    success_count += 1
        
        error_rate = error_count / (error_count + success_count)
        
        # Should detect high error rate
        assert error_rate > 0.5

    @pytest.mark.asyncio
    async def test_graceful_shutdown_during_failure(self, mock_redis):
        """Test graceful shutdown when components are failing."""
        with patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Simulate Redis failure during shutdown
            mock_redis.close.side_effect = Exception("Connection already closed")
            
            # Mock agents
            mock_agents = {
                "market_watcher": AsyncMock(),
                "strategy_researcher": AsyncMock(),
                "risk_officer": AsyncMock(),
                "technical_analyst": AsyncMock()
            }
            
            mock_db_manager = AsyncMock()
            mock_exchange_manager = AsyncMock()
            mock_config = MagicMock()
            
            orchestrator = SystemOrchestrator(
                agents=mock_agents,
                db_manager=mock_db_manager,
                exchange_manager=mock_exchange_manager,
                config=mock_config
            )
            
            await orchestrator.initialize()
            await orchestrator.start()
            
            # Should shutdown gracefully even with Redis failure
            await orchestrator.stop()
            
            assert orchestrator.running == False

    @pytest.mark.asyncio
    async def test_data_consistency_during_failures(self, mock_redis, mock_clickhouse):
        """Test data consistency maintenance during component failures."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test partial failure scenario
            test_data = {
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "timestamp": datetime.utcnow()
            }
            
            # Redis succeeds, ClickHouse fails
            mock_redis.setex.return_value = True
            mock_clickhouse.execute.side_effect = Exception("ClickHouse unavailable")
            
            # Should handle partial failure gracefully
            try:
                await db_manager.cache_market_data("BTCUSDT", test_data)
                await db_manager.insert_price_tick(test_data)
            except Exception:
                pass  # Expected to fail for ClickHouse
            
            # Verify Redis operation succeeded
            mock_redis.setex.assert_called()
            
            # Verify ClickHouse failure was handled
            mock_clickhouse.execute.assert_called()
