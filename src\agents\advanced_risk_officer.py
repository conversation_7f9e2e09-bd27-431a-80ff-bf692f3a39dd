"""
Advanced Risk Officer Agent - Llama3.3:70b Model
Sophisticated risk management with real-time monitoring, stress testing,
scenario analysis, and dynamic risk controls.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import logging
from scipy import stats
from sklearn.covariance import EmpiricalCovariance
import warnings
warnings.filterwarnings('ignore')

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, RiskAlert, RiskMetrics
from src.utils.risk_models import VaRCalculator, StressTestEngine, CorrelationAnalyzer
from src.utils.portfolio_analytics import PortfolioAnalytics
from src.utils.market_data import MarketDataProvider


class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EXTREME = "extreme"


class RiskType(Enum):
    MARKET_RISK = "market_risk"
    CREDIT_RISK = "credit_risk"
    LIQUIDITY_RISK = "liquidity_risk"
    OPERATIONAL_RISK = "operational_risk"
    CONCENTRATION_RISK = "concentration_risk"
    CORRELATION_RISK = "correlation_risk"
    VOLATILITY_RISK = "volatility_risk"
    TAIL_RISK = "tail_risk"
    SYSTEMIC_RISK = "systemic_risk"


@dataclass
class RiskMetric:
    metric_name: str
    current_value: float
    threshold: float
    risk_level: RiskLevel
    trend: str  # increasing, decreasing, stable
    confidence: float
    timestamp: datetime


@dataclass
class AdvancedRiskAlert:
    alert_id: str
    risk_type: RiskType
    risk_level: RiskLevel
    symbol: str
    portfolio_id: Optional[str]
    description: str
    current_value: float
    threshold: float
    recommended_action: str
    urgency: int  # 1-10 scale
    impact_assessment: str
    mitigation_strategies: List[str]
    ai_analysis: str
    related_positions: List[str]
    estimated_loss: float
    confidence: float
    timestamp: datetime


@dataclass
class StressTestScenario:
    scenario_id: str
    name: str
    description: str
    market_shocks: Dict[str, float]
    correlation_changes: Dict[str, float]
    volatility_multipliers: Dict[str, float]
    probability: float
    expected_loss: float
    max_loss: float
    recovery_time: int
    mitigation_effectiveness: float


class AdvancedRiskOfficer(BaseAgent):
    """
    Advanced Risk Officer using Llama3.3:70b for sophisticated risk management.
    
    Features:
    - Real-time portfolio risk monitoring
    - Advanced VaR calculations (Historical, Parametric, Monte Carlo)
    - Stress testing and scenario analysis
    - Dynamic correlation monitoring
    - Tail risk assessment (CVaR, Expected Shortfall)
    - Liquidity risk analysis
    - Concentration risk monitoring
    - Real-time risk limit enforcement
    - Predictive risk modeling
    - Systemic risk detection
    - Risk attribution analysis
    - Dynamic hedging recommendations
    - Regulatory compliance monitoring
    - Risk reporting and visualization
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_risk_officer"
        self.model_name = "llama3.3:70b"
        
        # Risk calculation engines
        self.var_calculator = VaRCalculator()
        self.stress_test_engine = StressTestEngine()
        self.correlation_analyzer = CorrelationAnalyzer()
        self.portfolio_analytics = PortfolioAnalytics()
        self.market_data_provider = MarketDataProvider()
        
        # Risk monitoring data
        self.portfolio_positions = {}
        self.risk_metrics = {}
        self.risk_limits = {}
        self.correlation_matrices = {}
        self.stress_test_results = {}
        self.risk_alerts = {}
        
        # Risk parameters
        self.confidence_levels = [0.95, 0.99, 0.999]
        self.time_horizons = [1, 5, 10, 22]  # days
        self.lookback_periods = [30, 60, 252]  # days
        
        # Risk limits (configurable)
        self.risk_limits_config = {
            "portfolio_var_95": 0.02,      # 2% daily VaR at 95% confidence
            "portfolio_var_99": 0.03,      # 3% daily VaR at 99% confidence
            "max_drawdown": 0.10,          # 10% maximum drawdown
            "concentration_limit": 0.20,    # 20% max position size
            "correlation_threshold": 0.80,  # 80% correlation warning
            "volatility_threshold": 0.30,   # 30% annualized volatility
            "liquidity_ratio": 0.05,       # 5% minimum liquid assets
            "leverage_ratio": 3.0,          # 3:1 maximum leverage
            "sector_concentration": 0.30,   # 30% max sector exposure
            "tail_risk_threshold": 0.05     # 5% tail risk limit
        }
        
        # Stress test scenarios
        self.stress_scenarios = {}
        
        # Risk model parameters
        self.decay_factor = 0.94  # EWMA decay factor
        self.min_observations = 100
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced risk officer components."""
        self.logger.info("🛡️ Initializing Advanced Risk Officer with Llama3.3:70b")
        
        # Initialize risk calculation engines
        await self.var_calculator.initialize()
        await self.stress_test_engine.initialize()
        await self.correlation_analyzer.initialize()
        await self.portfolio_analytics.initialize()
        
        # Load historical data for risk models
        await self._load_historical_risk_data()
        
        # Initialize stress test scenarios
        await self._initialize_stress_scenarios()
        
        # Set up risk limits
        await self._setup_risk_limits()
        
        self.logger.info("✅ Advanced Risk Officer initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced risk monitoring tasks."""
        return [
            asyncio.create_task(self._real_time_risk_monitoring()),
            asyncio.create_task(self._var_calculation()),
            asyncio.create_task(self._stress_testing()),
            asyncio.create_task(self._correlation_monitoring()),
            asyncio.create_task(self._liquidity_risk_assessment()),
            asyncio.create_task(self._concentration_risk_monitoring()),
            asyncio.create_task(self._tail_risk_analysis()),
            asyncio.create_task(self._systemic_risk_detection()),
            asyncio.create_task(self._risk_limit_enforcement()),
            asyncio.create_task(self._predictive_risk_modeling()),
            asyncio.create_task(self._risk_attribution_analysis()),
            asyncio.create_task(self._dynamic_hedging_analysis())
        ]

    async def _real_time_risk_monitoring(self):
        """Continuous real-time risk monitoring."""
        while self.running:
            try:
                # Get current portfolio positions
                portfolios = await self._get_portfolio_positions()
                
                for portfolio_id, positions in portfolios.items():
                    if not positions:
                        continue
                    
                    # Calculate real-time risk metrics
                    risk_metrics = await self._calculate_real_time_risk(portfolio_id, positions)
                    
                    # Check risk limits
                    violations = await self._check_risk_limits(portfolio_id, risk_metrics)
                    
                    # Generate alerts for violations
                    for violation in violations:
                        await self._generate_risk_alert(violation)
                    
                    # Update risk metrics storage
                    self.risk_metrics[portfolio_id] = risk_metrics
                    
                    # Perform AI-powered risk analysis
                    ai_analysis = await self._perform_ai_risk_analysis(portfolio_id, risk_metrics)
                    
                    # Store analysis results
                    risk_metrics["ai_analysis"] = ai_analysis
                
                await asyncio.sleep(30)  # Every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Real-time risk monitoring error: {e}")
                await asyncio.sleep(10)

    async def _var_calculation(self):
        """Calculate Value at Risk using multiple methods."""
        while self.running:
            try:
                portfolios = await self._get_portfolio_positions()
                
                for portfolio_id, positions in portfolios.items():
                    if not positions:
                        continue
                    
                    # Get historical price data
                    price_data = await self._get_portfolio_price_data(positions)
                    
                    if len(price_data) < self.min_observations:
                        continue
                    
                    var_results = {}
                    
                    for confidence in self.confidence_levels:
                        for horizon in self.time_horizons:
                            # Historical VaR
                            hist_var = await self.var_calculator.calculate_historical_var(
                                price_data, confidence, horizon
                            )
                            
                            # Parametric VaR
                            param_var = await self.var_calculator.calculate_parametric_var(
                                price_data, confidence, horizon
                            )
                            
                            # Monte Carlo VaR
                            mc_var = await self.var_calculator.calculate_monte_carlo_var(
                                price_data, confidence, horizon
                            )
                            
                            var_results[f"var_{int(confidence*100)}_{horizon}d"] = {
                                "historical": hist_var,
                                "parametric": param_var,
                                "monte_carlo": mc_var,
                                "average": (hist_var + param_var + mc_var) / 3
                            }
                    
                    # Calculate Expected Shortfall (CVaR)
                    es_results = await self._calculate_expected_shortfall(price_data)
                    var_results["expected_shortfall"] = es_results
                    
                    # Store VaR results
                    self.risk_metrics[portfolio_id] = self.risk_metrics.get(portfolio_id, {})
                    self.risk_metrics[portfolio_id]["var"] = var_results
                    self.risk_metrics[portfolio_id]["var_timestamp"] = datetime.utcnow()
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"VaR calculation error: {e}")
                await asyncio.sleep(60)

    async def _stress_testing(self):
        """Perform comprehensive stress testing."""
        while self.running:
            try:
                portfolios = await self._get_portfolio_positions()
                
                for portfolio_id, positions in portfolios.items():
                    if not positions:
                        continue
                    
                    stress_results = {}
                    
                    # Run predefined stress scenarios
                    for scenario_id, scenario in self.stress_scenarios.items():
                        result = await self.stress_test_engine.run_scenario(
                            positions, scenario
                        )
                        stress_results[scenario_id] = result
                    
                    # Generate custom scenarios using AI
                    ai_scenarios = await self._generate_ai_stress_scenarios(portfolio_id, positions)
                    
                    for scenario in ai_scenarios:
                        result = await self.stress_test_engine.run_scenario(positions, scenario)
                        stress_results[scenario["scenario_id"]] = result
                    
                    # Analyze stress test results
                    stress_analysis = await self._analyze_stress_results(stress_results)
                    
                    # Store results
                    self.stress_test_results[portfolio_id] = {
                        "results": stress_results,
                        "analysis": stress_analysis,
                        "timestamp": datetime.utcnow()
                    }
                    
                    # Check for critical stress test failures
                    critical_scenarios = [
                        result for result in stress_results.values()
                        if result.get("loss_percentage", 0) > 0.20  # 20% loss threshold
                    ]
                    
                    if critical_scenarios:
                        await self._generate_stress_test_alert(portfolio_id, critical_scenarios)
                
                await asyncio.sleep(900)  # Every 15 minutes
                
            except Exception as e:
                self.logger.error(f"Stress testing error: {e}")
                await asyncio.sleep(180)

    async def _correlation_monitoring(self):
        """Monitor portfolio correlations and detect regime changes."""
        while self.running:
            try:
                portfolios = await self._get_portfolio_positions()
                
                for portfolio_id, positions in portfolios.items():
                    if len(positions) < 2:  # Need at least 2 positions for correlation
                        continue
                    
                    # Get price data for correlation analysis
                    price_data = await self._get_portfolio_price_data(positions)
                    
                    if len(price_data) < 60:  # Need minimum data
                        continue
                    
                    # Calculate correlation matrices for different periods
                    correlation_analysis = {}
                    
                    for period in self.lookback_periods:
                        if len(price_data) >= period:
                            recent_data = price_data.tail(period)
                            
                            # Calculate correlation matrix
                            correlation_matrix = recent_data.pct_change().corr()
                            
                            # Analyze correlation structure
                            analysis = await self.correlation_analyzer.analyze_correlation_structure(
                                correlation_matrix
                            )
                            
                            correlation_analysis[f"{period}d"] = {
                                "matrix": correlation_matrix.to_dict(),
                                "analysis": analysis
                            }
                    
                    # Detect correlation regime changes
                    regime_changes = await self._detect_correlation_regime_changes(
                        portfolio_id, correlation_analysis
                    )
                    
                    # Store correlation data
                    self.correlation_matrices[portfolio_id] = {
                        "analysis": correlation_analysis,
                        "regime_changes": regime_changes,
                        "timestamp": datetime.utcnow()
                    }
                    
                    # Generate alerts for high correlations
                    await self._check_correlation_alerts(portfolio_id, correlation_analysis)
                
                await asyncio.sleep(600)  # Every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Correlation monitoring error: {e}")
                await asyncio.sleep(120)

    async def _perform_ai_risk_analysis(self, portfolio_id: str, risk_metrics: Dict) -> str:
        """Perform comprehensive AI-powered risk analysis."""
        
        ai_context = {
            "portfolio_id": portfolio_id,
            "risk_metrics": risk_metrics,
            "risk_limits": self.risk_limits.get(portfolio_id, {}),
            "market_conditions": await self._get_current_market_conditions(),
            "historical_performance": await self._get_portfolio_performance_history(portfolio_id)
        }
        
        ai_analysis = await ai_service.generate_response(
            "risk_officer",
            f"""
            As an expert risk management specialist using the Llama3.3:70b model, perform a comprehensive risk analysis for portfolio {portfolio_id}.
            
            Current Risk Metrics: {risk_metrics}
            Risk Limits: {ai_context['risk_limits']}
            Market Conditions: {ai_context['market_conditions']}
            Historical Performance: {ai_context['historical_performance']}
            
            Provide detailed analysis including:
            1. Overall risk assessment and risk level classification
            2. Key risk factors and vulnerabilities
            3. Risk concentration analysis
            4. Potential tail risks and black swan scenarios
            5. Risk-adjusted performance evaluation
            6. Specific risk mitigation recommendations
            7. Dynamic hedging strategies
            8. Position sizing adjustments
            9. Liquidity considerations
            10. Regulatory compliance status
            
            Focus on actionable insights and specific risk management actions.
            Identify any emerging risks or concerning trends.
            Provide probability-weighted risk scenarios.
            """,
            ai_context
        )
        
        return ai_analysis

    async def _generate_ai_stress_scenarios(self, portfolio_id: str, positions: Dict) -> List[StressTestScenario]:
        """Generate custom stress test scenarios using AI."""
        
        ai_context = {
            "portfolio_id": portfolio_id,
            "positions": positions,
            "current_market_conditions": await self._get_current_market_conditions(),
            "historical_stress_events": await self._get_historical_stress_events(),
            "portfolio_characteristics": await self._analyze_portfolio_characteristics(positions)
        }
        
        ai_response = await ai_service.generate_response(
            "risk_officer",
            f"""
            As a risk management expert, generate 3-5 custom stress test scenarios for portfolio {portfolio_id}.
            
            Portfolio Positions: {positions}
            Current Market Conditions: {ai_context['current_market_conditions']}
            Historical Stress Events: {ai_context['historical_stress_events']}
            Portfolio Characteristics: {ai_context['portfolio_characteristics']}
            
            For each scenario, provide:
            1. Scenario name and description
            2. Specific market shocks (price movements, volatility changes)
            3. Correlation structure changes
            4. Probability of occurrence
            5. Expected timeline and duration
            6. Potential triggers and catalysts
            7. Recovery expectations
            
            Focus on scenarios that are:
            - Relevant to current market conditions
            - Tailored to portfolio composition
            - Based on historical precedents
            - Covering different risk factors
            - Ranging from moderate to extreme severity
            """,
            ai_context
        )
        
        # Parse AI response into stress test scenarios
        scenarios = await self._parse_stress_scenarios(ai_response)
        
        return scenarios

    async def _calculate_expected_shortfall(self, price_data: pd.DataFrame) -> Dict:
        """Calculate Expected Shortfall (Conditional VaR)."""
        
        returns = price_data.pct_change().dropna()
        portfolio_returns = returns.mean(axis=1)  # Equal weighted for simplicity
        
        es_results = {}
        
        for confidence in self.confidence_levels:
            # Calculate VaR threshold
            var_threshold = np.percentile(portfolio_returns, (1 - confidence) * 100)
            
            # Calculate Expected Shortfall
            tail_losses = portfolio_returns[portfolio_returns <= var_threshold]
            expected_shortfall = tail_losses.mean() if len(tail_losses) > 0 else 0
            
            es_results[f"es_{int(confidence*100)}"] = {
                "value": expected_shortfall,
                "var_threshold": var_threshold,
                "tail_observations": len(tail_losses)
            }
        
        return es_results

    async def _check_risk_limits(self, portfolio_id: str, risk_metrics: Dict) -> List[Dict]:
        """Check risk metrics against defined limits."""
        
        violations = []
        limits = self.risk_limits.get(portfolio_id, self.risk_limits_config)
        
        # Check VaR limits
        var_data = risk_metrics.get("var", {})
        for var_key, var_values in var_data.items():
            if "95" in var_key and "1d" in var_key:
                if var_values.get("average", 0) > limits.get("portfolio_var_95", 0.02):
                    violations.append({
                        "type": "var_limit_breach",
                        "metric": var_key,
                        "current_value": var_values["average"],
                        "limit": limits["portfolio_var_95"],
                        "severity": "high"
                    })
        
        # Check concentration limits
        concentration = risk_metrics.get("concentration", {})
        max_position = concentration.get("max_position_weight", 0)
        if max_position > limits.get("concentration_limit", 0.20):
            violations.append({
                "type": "concentration_limit_breach",
                "metric": "max_position_weight",
                "current_value": max_position,
                "limit": limits["concentration_limit"],
                "severity": "medium"
            })
        
        # Check correlation limits
        correlation_data = risk_metrics.get("correlation", {})
        max_correlation = correlation_data.get("max_correlation", 0)
        if max_correlation > limits.get("correlation_threshold", 0.80):
            violations.append({
                "type": "correlation_limit_breach",
                "metric": "max_correlation",
                "current_value": max_correlation,
                "limit": limits["correlation_threshold"],
                "severity": "medium"
            })
        
        return violations

    async def _generate_risk_alert(self, violation: Dict):
        """Generate comprehensive risk alert."""
        
        alert = AdvancedRiskAlert(
            alert_id=f"risk_alert_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            risk_type=RiskType.MARKET_RISK,  # Determine based on violation type
            risk_level=self._determine_risk_level(violation["severity"]),
            symbol="PORTFOLIO",
            portfolio_id=violation.get("portfolio_id"),
            description=f"{violation['type']}: {violation['metric']} exceeded limit",
            current_value=violation["current_value"],
            threshold=violation["limit"],
            recommended_action=await self._get_recommended_action(violation),
            urgency=self._calculate_urgency(violation),
            impact_assessment=await self._assess_impact(violation),
            mitigation_strategies=await self._get_mitigation_strategies(violation),
            ai_analysis=await self._get_ai_alert_analysis(violation),
            related_positions=[],
            estimated_loss=await self._estimate_potential_loss(violation),
            confidence=0.85,
            timestamp=datetime.utcnow()
        )
        
        # Store alert
        self.risk_alerts[alert.alert_id] = alert
        
        # Send alert to other agents
        await self._send_risk_alert(alert)

    def _determine_risk_level(self, severity: str) -> RiskLevel:
        """Determine risk level from severity."""
        severity_mapping = {
            "low": RiskLevel.LOW,
            "medium": RiskLevel.MEDIUM,
            "high": RiskLevel.HIGH,
            "critical": RiskLevel.CRITICAL,
            "extreme": RiskLevel.EXTREME
        }
        return severity_mapping.get(severity, RiskLevel.MEDIUM)

    def _calculate_urgency(self, violation: Dict) -> int:
        """Calculate urgency score (1-10)."""
        base_urgency = {
            "low": 3,
            "medium": 6,
            "high": 8,
            "critical": 9,
            "extreme": 10
        }
        
        urgency = base_urgency.get(violation["severity"], 5)
        
        # Adjust based on magnitude of breach
        breach_magnitude = violation["current_value"] / violation["limit"]
        if breach_magnitude > 2.0:
            urgency = min(10, urgency + 2)
        elif breach_magnitude > 1.5:
            urgency = min(10, urgency + 1)
        
        return urgency

    async def _cleanup_agent(self):
        """Cleanup risk officer resources."""
        self.logger.info("🧹 Cleaning up Advanced Risk Officer resources")
        
        # Save risk data
        await self._save_risk_data()
        
        # Clear memory structures
        self.portfolio_positions.clear()
        self.risk_metrics.clear()
        self.correlation_matrices.clear()
        self.stress_test_results.clear()
        self.risk_alerts.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "position_update":
            await self._process_position_update(message.content)
        elif message.message_type == "risk_assessment_request":
            await self._process_risk_assessment_request(message.content)
        elif message.message_type == "stress_test_request":
            await self._process_stress_test_request(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic risk analysis."""
        while self.running:
            try:
                # Generate risk reports
                await self._generate_risk_reports()
                
                # Update risk models
                await self._update_risk_models()
                
                # Validate risk limits
                await self._validate_risk_limits()
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Periodic risk analysis error: {e}")
                await asyncio.sleep(300)
