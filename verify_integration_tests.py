#!/usr/bin/env python3
"""
Verification script for NORYON V2 Integration Test Framework

This script verifies that the integration test framework is properly set up
and ready to run comprehensive tests.
"""

import sys
import os
from pathlib import Path


def check_file_exists(file_path, description):
    """Check if a file exists and report status."""
    if Path(file_path).exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False


def check_directory_exists(dir_path, description):
    """Check if a directory exists and report status."""
    if Path(dir_path).exists() and Path(dir_path).is_dir():
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} - NOT FOUND")
        return False


def check_imports():
    """Check if required modules can be imported."""
    print("\n🔍 Checking Python imports...")
    
    imports_to_check = [
        ("pytest", "pytest testing framework"),
        ("asyncio", "asyncio for async testing"),
        ("unittest.mock", "mocking framework"),
        ("json", "JSON handling"),
        ("datetime", "datetime handling"),
        ("pathlib", "path handling")
    ]
    
    all_imports_ok = True
    
    for module_name, description in imports_to_check:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
        except ImportError as e:
            print(f"❌ {description}: {module_name} - IMPORT ERROR: {e}")
            all_imports_ok = False
    
    return all_imports_ok


def main():
    """Main verification function."""
    print("🎯 NORYON V2 Integration Test Framework Verification")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("tests").exists():
        print("❌ Error: tests directory not found")
        print("Please run this script from the project root directory")
        sys.exit(1)
    
    print("📁 Checking test framework structure...")
    
    # Check main test directories
    checks = []
    
    # Test directories
    checks.append(check_directory_exists("tests", "Main tests directory"))
    checks.append(check_directory_exists("tests/integration", "Integration tests directory"))
    
    # Core test files
    checks.append(check_file_exists("tests/conftest.py", "Main test configuration"))
    checks.append(check_file_exists("tests/integration/conftest.py", "Integration test configuration"))
    checks.append(check_file_exists("tests/integration/README.md", "Integration test documentation"))
    
    # Test suite files
    test_suites = [
        ("tests/integration/test_component_integration.py", "Component Integration Tests"),
        ("tests/integration/test_realtime_data_flow.py", "Real-time Data Flow Tests"),
        ("tests/integration/test_ai_agent_coordination.py", "AI Agent Coordination Tests"),
        ("tests/integration/test_api_endpoints.py", "API Endpoint Tests"),
        ("tests/integration/test_database_integration.py", "Database Integration Tests"),
        ("tests/integration/test_error_recovery.py", "Error Recovery Tests"),
        ("tests/integration/test_performance_load.py", "Performance & Load Tests"),
        ("tests/integration/test_setup_verification.py", "Setup Verification Tests")
    ]
    
    for file_path, description in test_suites:
        checks.append(check_file_exists(file_path, description))
    
    # Test runner and utilities
    checks.append(check_file_exists("tests/integration/test_runner.py", "Comprehensive Test Runner"))
    checks.append(check_file_exists("run_integration_tests.py", "Test Execution Script"))
    checks.append(check_file_exists("INTEGRATION_TESTING_SUMMARY.md", "Integration Testing Summary"))
    
    # Check Python imports
    import_check = check_imports()
    checks.append(import_check)
    
    # Check source code structure
    print("\n📦 Checking source code structure...")
    source_checks = [
        ("src", "Source code directory"),
        ("src/api", "API module"),
        ("src/services", "Services module"),
        ("src/agents", "Agents module"),
        ("src/db", "Database module"),
        ("src/core", "Core module")
    ]
    
    for dir_path, description in source_checks:
        checks.append(check_directory_exists(dir_path, description))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    total_checks = len(checks)
    passed_checks = sum(1 for check in checks if check)
    failed_checks = total_checks - passed_checks
    
    print(f"Total Checks: {total_checks}")
    print(f"✅ Passed: {passed_checks}")
    print(f"❌ Failed: {failed_checks}")
    print(f"Success Rate: {passed_checks/total_checks:.1%}")
    
    if failed_checks == 0:
        print("\n🎉 INTEGRATION TEST FRAMEWORK VERIFICATION SUCCESSFUL!")
        print("✅ All components are properly set up")
        print("🚀 Ready to run comprehensive integration tests")
        print("\nNext steps:")
        print("1. Run: python run_integration_tests.py --quick")
        print("2. Run: python run_integration_tests.py")
        print("3. Check generated test reports")
        return True
    else:
        print("\n💥 INTEGRATION TEST FRAMEWORK VERIFICATION FAILED!")
        print(f"❌ {failed_checks} components are missing or misconfigured")
        print("Please fix the issues above before running integration tests")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
