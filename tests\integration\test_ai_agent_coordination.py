"""
AI Agent Coordination Integration Tests for NORYON V2

Tests that verify all 4 AI agents (<PERSON> Watcher, Strategy Researcher, Risk Officer, 
Technical Analyst) can operate independently and coordinate when needed.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List

from src.agents.agent_manager import Agent<PERSON>anager
from src.agents.market_watcher import MarketWatcherAgent
from src.agents.strategy_researcher import StrategyResearcher
from src.agents.risk_officer import RiskOfficerAgent
from src.agents.technical_analyst import TechnicalAnalystAgent
from src.core.orchestrator import SystemOrchestrator
from src.core.models import AgentMessage, AgentStatus
from src.services.ai_service import AIService


class TestAIAgentCoordination:
    """Test suite for AI agent coordination verification."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.test_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        self.agent_messages = []
        self.coordination_events = []
        
    @pytest.mark.asyncio
    async def test_individual_agent_initialization(self, mock_ai_service, mock_redis):
        """Test that each AI agent can initialize independently."""
        with patch('src.services.ai_service.ai_service', mock_ai_service), \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Test Market Watcher Agent
            market_watcher = MarketWatcherAgent()
            await market_watcher.start()
            assert market_watcher._running == True
            await market_watcher.stop()
            
            # Test Strategy Researcher Agent
            strategy_researcher = StrategyResearcher()
            await strategy_researcher.start()
            assert strategy_researcher._running == True
            await strategy_researcher.stop()
            
            # Test Risk Officer Agent
            risk_officer = RiskOfficerAgent()
            await risk_officer.start()
            assert risk_officer._running == True
            await risk_officer.stop()
            
            # Test Technical Analyst Agent
            technical_analyst = TechnicalAnalystAgent()
            await technical_analyst.start()
            assert technical_analyst._running == True
            await technical_analyst.stop()

    @pytest.mark.asyncio
    async def test_agent_manager_coordination(self, mock_ai_service, mock_redis):
        """Test agent manager can coordinate all agents."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup mock market data
            mock_market_data = {
                "BTCUSDT": {
                    "price": 45000.0,
                    "change_24h": 5.0,  # Significant movement
                    "volume": 1000000.0,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Let agents run for coordination cycles
            await asyncio.sleep(3)
            
            # Verify all agents are running
            status = agent_manager.get_agent_status()
            assert len(status) == 4
            
            expected_agents = ["market_watcher", "strategy_researcher", "risk_officer", "technical_analyst"]
            for agent_name in expected_agents:
                assert agent_name in status
                assert status[agent_name].get("running", False) == True
            
            # Test coordination health check
            await agent_manager._health_check()
            
            # Test coordination decision making
            await agent_manager._coordinate_decisions()
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_market_watcher_analysis_trigger(self, mock_ai_service, mock_redis):
        """Test Market Watcher triggers analysis on significant movements."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup significant market movement
            mock_market_data = {
                "BTCUSDT": {
                    "price": 45000.0,
                    "change_24h": 8.0,  # Large movement (> 1.0 threshold)
                    "volume": 1000000.0
                }
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            market_watcher = MarketWatcherAgent()
            await market_watcher.start()
            
            # Let market watcher analyze
            await asyncio.sleep(2)
            
            # Verify AI analysis was triggered
            assert mock_ai_service.analyze_market_data.call_count > 0
            
            # Verify analysis was called with correct parameters
            call_args = mock_ai_service.analyze_market_data.call_args
            assert call_args[0][0] == "BTCUSDT"  # symbol
            assert "price" in call_args[0][1]     # market data
            
            await market_watcher.stop()

    @pytest.mark.asyncio
    async def test_strategy_researcher_signal_generation(self, mock_ai_service, mock_redis):
        """Test Strategy Researcher generates trading signals."""
        with patch('src.agents.strategy_researcher.ai_service', mock_ai_service), \
             patch('src.agents.strategy_researcher.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup mock market data with price trend
            mock_market_data = {
                "BTCUSDT": {
                    "price": 45000.0,
                    "volume": 1000000.0,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            # Mock AI strategy generation
            mock_ai_service.generate_trading_strategy.return_value = "Strong buy signal based on momentum"
            
            strategy_researcher = StrategyResearcher()
            await strategy_researcher.start()
            
            # Let strategy researcher analyze
            await asyncio.sleep(3)
            
            # Verify signal generation was attempted
            # (Note: Actual signal generation depends on price window accumulation)
            assert strategy_researcher._running == True
            
            await strategy_researcher.stop()

    @pytest.mark.asyncio
    async def test_risk_officer_monitoring(self, mock_ai_service, mock_redis):
        """Test Risk Officer monitors and assesses risk."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup high-risk market scenario
            mock_market_data = {
                "BTCUSDT": {
                    "price": 45000.0,
                    "change_24h": -15.0,  # Large negative movement
                    "volume": 2000000.0   # High volume
                }
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            risk_officer = RiskOfficerAgent()
            await risk_officer.start()
            
            # Let risk officer analyze
            await asyncio.sleep(2)
            
            # Verify risk analysis was performed
            assert risk_officer._running == True
            
            await risk_officer.stop()

    @pytest.mark.asyncio
    async def test_technical_analyst_chart_analysis(self, mock_ai_service, mock_redis):
        """Test Technical Analyst performs chart analysis."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup market data for technical analysis
            mock_market_data = {
                "BTCUSDT": {
                    "price": 45000.0,
                    "volume": 1000000.0,
                    "high": 45500.0,
                    "low": 44500.0
                }
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            technical_analyst = TechnicalAnalystAgent()
            await technical_analyst.start()
            
            # Let technical analyst analyze
            await asyncio.sleep(2)
            
            # Verify technical analysis was performed
            assert technical_analyst._running == True
            
            await technical_analyst.stop()

    @pytest.mark.asyncio
    async def test_agent_message_coordination(self, mock_redis):
        """Test agents can send and receive coordination messages."""
        with patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Mock agents for message testing
            mock_agents = {
                "market_watcher": AsyncMock(),
                "strategy_researcher": AsyncMock(),
                "risk_officer": AsyncMock(),
                "technical_analyst": AsyncMock()
            }
            
            # Mock database and exchange managers
            mock_db_manager = AsyncMock()
            mock_exchange_manager = AsyncMock()
            mock_config = MagicMock()
            
            orchestrator = SystemOrchestrator(
                agents=mock_agents,
                db_manager=mock_db_manager,
                exchange_manager=mock_exchange_manager,
                config=mock_config
            )
            
            await orchestrator.initialize()
            await orchestrator.start()
            
            # Test message sending between agents
            test_message = AgentMessage(
                source="market_watcher",
                target="strategy_researcher",
                message_type="market_alert",
                content={
                    "symbol": "BTCUSDT",
                    "alert_type": "significant_movement",
                    "change_percent": 5.0
                },
                timestamp=datetime.utcnow(),
                priority=1
            )
            
            await orchestrator.send_message(test_message)
            
            # Verify message was queued
            assert orchestrator.message_queue.qsize() > 0
            
            # Let message processor handle the message
            await asyncio.sleep(0.1)
            
            await orchestrator.stop()

    @pytest.mark.asyncio
    async def test_agent_coordination_scenarios(self, mock_ai_service, mock_redis):
        """Test specific coordination scenarios between agents."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Scenario 1: Market crash detection
            crash_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": -20.0, "volume": 5000000.0}
            }
            mock_broadcaster.get_all_latest.return_value = crash_data
            
            # Let agents coordinate on crash scenario
            await asyncio.sleep(2)
            
            # Scenario 2: Bull market detection
            bull_data = {
                "BTCUSDT": {"price": 50000.0, "change_24h": 15.0, "volume": 3000000.0}
            }
            mock_broadcaster.get_all_latest.return_value = bull_data
            
            # Let agents coordinate on bull scenario
            await asyncio.sleep(2)
            
            # Scenario 3: Low volatility period
            stable_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 0.1, "volume": 100000.0}
            }
            mock_broadcaster.get_all_latest.return_value = stable_data
            
            # Let agents coordinate on stable scenario
            await asyncio.sleep(2)
            
            # Verify agents handled all scenarios
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_agent_performance_coordination(self, mock_ai_service, mock_redis):
        """Test agent coordination performance under load."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup multiple symbols with varying data
            multi_symbol_data = {}
            for i, symbol in enumerate(["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]):
                multi_symbol_data[symbol] = {
                    "price": 1000.0 * (i + 1),
                    "change_24h": (-5.0 + i * 2.5),  # Varying changes
                    "volume": 1000000.0 * (i + 1)
                }
            
            mock_broadcaster.get_all_latest.return_value = multi_symbol_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Run coordination under load
            coordination_cycles = 5
            for cycle in range(coordination_cycles):
                # Update market data
                for symbol in multi_symbol_data:
                    multi_symbol_data[symbol]["change_24h"] += (cycle * 0.5)
                
                mock_broadcaster.get_all_latest.return_value = multi_symbol_data
                
                # Let agents process
                await asyncio.sleep(1)
                
                # Verify agents are still coordinating
                status = agent_manager.get_agent_status()
                assert len(status) == 4
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_agent_error_handling_coordination(self, mock_ai_service, mock_redis):
        """Test agent coordination when individual agents encounter errors."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup normal market data
            mock_market_data = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 2.0, "volume": 1000000.0}
            }
            mock_broadcaster.get_all_latest.return_value = mock_market_data
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Simulate AI service failure
            mock_ai_service.analyze_market_data.side_effect = Exception("AI service timeout")
            
            # Let agents attempt to coordinate with AI failure
            await asyncio.sleep(2)
            
            # Verify agents are still running despite AI failure
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            # Reset AI service
            mock_ai_service.analyze_market_data.side_effect = None
            mock_ai_service.analyze_market_data.return_value = "Recovery analysis"
            
            # Let agents recover
            await asyncio.sleep(2)
            
            # Verify recovery
            status = agent_manager.get_agent_status()
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_agent_decision_consensus(self, mock_ai_service, mock_redis):
        """Test agents can reach consensus on trading decisions."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup strong bullish scenario
            bullish_data = {
                "BTCUSDT": {
                    "price": 50000.0,
                    "change_24h": 10.0,
                    "volume": 2000000.0,
                    "momentum": "strong_up"
                }
            }
            mock_broadcaster.get_all_latest.return_value = bullish_data
            
            # Mock AI responses for consensus
            mock_ai_service.analyze_market_data.return_value = "Strong bullish signal - recommend BUY"
            mock_ai_service.generate_trading_strategy.return_value = "BUY signal with high confidence"
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Let agents analyze and coordinate
            await asyncio.sleep(3)
            
            # Test coordination decision making
            await agent_manager._coordinate_decisions()
            
            # Verify AI analysis was called multiple times (by different agents)
            assert mock_ai_service.analyze_market_data.call_count > 0
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_agent_priority_coordination(self, mock_ai_service, mock_redis):
        """Test agent coordination respects priority levels."""
        with patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Mock agents with different priorities
            mock_agents = {
                "risk_officer": AsyncMock(),      # Highest priority
                "market_watcher": AsyncMock(),    # High priority
                "technical_analyst": AsyncMock(), # Medium priority
                "strategy_researcher": AsyncMock() # Lower priority
            }
            
            mock_db_manager = AsyncMock()
            mock_exchange_manager = AsyncMock()
            mock_config = MagicMock()
            
            orchestrator = SystemOrchestrator(
                agents=mock_agents,
                db_manager=mock_db_manager,
                exchange_manager=mock_exchange_manager,
                config=mock_config
            )
            
            await orchestrator.initialize()
            await orchestrator.start()
            
            # Send messages with different priorities
            high_priority_msg = AgentMessage(
                source="risk_officer",
                target="market_watcher",
                message_type="risk_alert",
                content={"alert": "high_risk_detected"},
                timestamp=datetime.utcnow(),
                priority=3  # High priority
            )
            
            low_priority_msg = AgentMessage(
                source="strategy_researcher",
                target="technical_analyst",
                message_type="strategy_update",
                content={"update": "minor_adjustment"},
                timestamp=datetime.utcnow(),
                priority=1  # Low priority
            )
            
            # Send low priority first, then high priority
            await orchestrator.send_message(low_priority_msg)
            await orchestrator.send_message(high_priority_msg)
            
            # Let message processor handle messages
            await asyncio.sleep(0.2)
            
            # High priority message should be processed first
            # (This is verified by the orchestrator's priority queue implementation)
            
            await orchestrator.stop()
