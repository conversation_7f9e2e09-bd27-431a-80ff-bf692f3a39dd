"""
Advanced Memory Management Optimizer for Noryon V2
Intelligent memory management with garbage collection optimization, memory pools, and leak detection
"""

import asyncio
import gc
import logging
import psutil
import threading
import time
import weakref
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Set, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import sys
import tracemalloc
from memory_profiler import profile

logger = logging.getLogger(__name__)

class MemoryLevel(Enum):
    LOW = "low"        # < 50% memory usage
    NORMAL = "normal"  # 50-75% memory usage  
    HIGH = "high"      # 75-90% memory usage
    CRITICAL = "critical"  # > 90% memory usage

class OptimizationStrategy(Enum):
    AGGRESSIVE = "aggressive"    # Frequent GC, strict limits
    BALANCED = "balanced"        # Moderate optimization
    CONSERVATIVE = "conservative" # Minimal intervention

@dataclass
class MemoryConfig:
    """Configuration for memory management"""
    max_memory_usage_pct: float = 80.0  # Max memory usage percentage
    gc_threshold_mb: int = 500           # Trigger GC when memory increases by this amount
    memory_check_interval: int = 30      # Check memory every 30 seconds
    leak_detection_enabled: bool = True
    memory_profiling_enabled: bool = False
    object_tracking_enabled: bool = True
    pool_management_enabled: bool = True

@dataclass
class MemoryMetrics:
    """Memory usage metrics"""
    total_memory_mb: float
    used_memory_mb: float
    available_memory_mb: float
    memory_usage_pct: float
    gc_collections: int
    objects_tracked: int
    potential_leaks: int
    optimization_actions: int
    last_gc_time: datetime
    memory_level: MemoryLevel

class MemoryPool:
    """Memory pool for reusable objects"""
    
    def __init__(self, object_factory: Callable, max_size: int = 1000):
        self.object_factory = object_factory
        self.max_size = max_size
        self.pool = deque()
        self._lock = threading.Lock()
        self.created_count = 0
        self.reused_count = 0
    
    def get_object(self):
        """Get object from pool or create new one"""
        with self._lock:
            if self.pool:
                self.reused_count += 1
                return self.pool.popleft()
            else:
                self.created_count += 1
                return self.object_factory()
    
    def return_object(self, obj):
        """Return object to pool"""
        with self._lock:
            if len(self.pool) < self.max_size:
                # Reset object state if needed
                if hasattr(obj, 'reset'):
                    obj.reset()
                self.pool.append(obj)
    
    def get_stats(self) -> Dict[str, int]:
        """Get pool statistics"""
        return {
            "pool_size": len(self.pool),
            "max_size": self.max_size,
            "created_count": self.created_count,
            "reused_count": self.reused_count,
            "reuse_ratio": self.reused_count / max(self.created_count + self.reused_count, 1)
        }

class ObjectTracker:
    """Track object lifecycle to detect potential memory leaks"""
    
    def __init__(self):
        self.object_counts: Dict[str, int] = defaultdict(int)
        self.object_refs: Dict[str, Set[weakref.ref]] = defaultdict(set)
        self.creation_times: Dict[str, List[datetime]] = defaultdict(list)
        self._lock = threading.Lock()
    
    def track_object(self, obj, obj_type: str = None):
        """Track an object for lifecycle monitoring"""
        if obj_type is None:
            obj_type = type(obj).__name__
        
        with self._lock:
            self.object_counts[obj_type] += 1
            
            # Create weak reference with cleanup callback
            def cleanup_callback(ref):
                with self._lock:
                    self.object_refs[obj_type].discard(ref)
                    self.object_counts[obj_type] -= 1
            
            weak_ref = weakref.ref(obj, cleanup_callback)
            self.object_refs[obj_type].add(weak_ref)
            self.creation_times[obj_type].append(datetime.now(timezone.utc))
            
            # Keep only last 1000 creation times
            if len(self.creation_times[obj_type]) > 1000:
                self.creation_times[obj_type] = self.creation_times[obj_type][-1000:]
    
    def get_object_stats(self) -> Dict[str, Any]:
        """Get object tracking statistics"""
        with self._lock:
            stats = {}
            for obj_type, count in self.object_counts.items():
                recent_creations = [
                    t for t in self.creation_times[obj_type] 
                    if (datetime.now(timezone.utc) - t).seconds < 3600  # Last hour
                ]
                
                stats[obj_type] = {
                    "current_count": count,
                    "recent_creations": len(recent_creations),
                    "total_refs": len(self.object_refs[obj_type])
                }
            
            return stats
    
    def detect_potential_leaks(self) -> List[Dict[str, Any]]:
        """Detect potential memory leaks"""
        potential_leaks = []
        now = datetime.now(timezone.utc)
        
        with self._lock:
            for obj_type, count in self.object_counts.items():
                if count > 1000:  # High object count threshold
                    recent_creations = [
                        t for t in self.creation_times[obj_type]
                        if (now - t).seconds < 3600
                    ]
                    
                    if len(recent_creations) > 100:  # High creation rate
                        potential_leaks.append({
                            "object_type": obj_type,
                            "current_count": count,
                            "recent_creations": len(recent_creations),
                            "leak_score": count + len(recent_creations),
                            "recommendation": f"High {obj_type} object count - check for proper cleanup"
                        })
        
        return sorted(potential_leaks, key=lambda x: x["leak_score"], reverse=True)

class MemoryOptimizer:
    """Advanced memory management and optimization system"""
    
    def __init__(self, config: MemoryConfig = None):
        self.config = config or MemoryConfig()
        
        # Memory tracking
        self.metrics = MemoryMetrics(
            total_memory_mb=0,
            used_memory_mb=0,
            available_memory_mb=0,
            memory_usage_pct=0,
            gc_collections=0,
            objects_tracked=0,
            potential_leaks=0,
            optimization_actions=0,
            last_gc_time=datetime.now(timezone.utc),
            memory_level=MemoryLevel.NORMAL
        )
        
        # Object tracking
        self.object_tracker = ObjectTracker() if self.config.object_tracking_enabled else None
        
        # Memory pools
        self.memory_pools: Dict[str, MemoryPool] = {}
        
        # Monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Memory history for trend analysis
        self.memory_history: deque = deque(maxlen=1000)
        
        # GC optimization
        self.last_memory_check = 0
        self.gc_stats = {"collections": 0, "freed_objects": 0, "freed_memory_mb": 0}
        
        # Performance tracking
        self.optimization_history: List[Dict[str, Any]] = []
        
        logger.info("[INIT] Memory Management Optimizer initialized")
    
    async def initialize(self):
        """Initialize memory optimizer"""
        try:
            # Start memory profiling if enabled
            if self.config.memory_profiling_enabled:
                tracemalloc.start()
            
            # Configure garbage collection
            self._configure_gc()
            
            # Start monitoring
            await self._start_monitoring()
            
            self.running = True
            logger.info("✅ Memory optimizer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize memory optimizer: {e}")
            raise
    
    def _configure_gc(self):
        """Configure garbage collection settings"""
        # Set GC thresholds for better performance
        # (threshold0, threshold1, threshold2)
        gc.set_threshold(700, 10, 10)  # More aggressive collection
        
        # Enable automatic GC
        gc.enable()
        
        logger.info("🔧 Garbage collection configured")
    
    async def _start_monitoring(self):
        """Start memory monitoring background task"""
        self.monitoring_task = asyncio.create_task(self._memory_monitor_loop())
        logger.info("📊 Memory monitoring started")
    
    async def _memory_monitor_loop(self):
        """Background memory monitoring loop"""
        while self.running:
            try:
                await self._update_memory_metrics()
                await self._check_memory_optimization()
                await asyncio.sleep(self.config.memory_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Memory monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _update_memory_metrics(self):
        """Update current memory metrics"""
        try:
            # Get system memory info
            memory_info = psutil.virtual_memory()
            
            self.metrics.total_memory_mb = memory_info.total / (1024 * 1024)
            self.metrics.used_memory_mb = memory_info.used / (1024 * 1024)
            self.metrics.available_memory_mb = memory_info.available / (1024 * 1024)
            self.metrics.memory_usage_pct = memory_info.percent
            
            # Determine memory level
            if self.metrics.memory_usage_pct < 50:
                self.metrics.memory_level = MemoryLevel.LOW
            elif self.metrics.memory_usage_pct < 75:
                self.metrics.memory_level = MemoryLevel.NORMAL
            elif self.metrics.memory_usage_pct < 90:
                self.metrics.memory_level = MemoryLevel.HIGH
            else:
                self.metrics.memory_level = MemoryLevel.CRITICAL
            
            # Update object tracking metrics
            if self.object_tracker:
                obj_stats = self.object_tracker.get_object_stats()
                self.metrics.objects_tracked = sum(stats["current_count"] for stats in obj_stats.values())
                
                # Check for potential leaks
                potential_leaks = self.object_tracker.detect_potential_leaks()
                self.metrics.potential_leaks = len(potential_leaks)
            
            # Store in history
            self.memory_history.append({
                "timestamp": datetime.now(timezone.utc),
                "memory_usage_pct": self.metrics.memory_usage_pct,
                "used_memory_mb": self.metrics.used_memory_mb,
                "memory_level": self.metrics.memory_level.value
            })
            
        except Exception as e:
            logger.error(f"❌ Failed to update memory metrics: {e}")
    
    async def _check_memory_optimization(self):
        """Check if memory optimization is needed"""
        try:
            current_memory = self.metrics.used_memory_mb
            memory_increase = current_memory - self.last_memory_check
            
            # Trigger optimization based on conditions
            should_optimize = (
                self.metrics.memory_level in [MemoryLevel.HIGH, MemoryLevel.CRITICAL] or
                memory_increase > self.config.gc_threshold_mb or
                self.metrics.potential_leaks > 5
            )
            
            if should_optimize:
                await self._perform_memory_optimization()
            
            self.last_memory_check = current_memory
            
        except Exception as e:
            logger.error(f"❌ Memory optimization check error: {e}")
    
    async def _perform_memory_optimization(self):
        """Perform memory optimization"""
        try:
            optimization_start = time.time()
            actions_taken = []
            
            # Force garbage collection
            freed_objects = gc.collect()
            actions_taken.append(f"GC freed {freed_objects} objects")
            
            # Clear weak references
            if self.object_tracker:
                self._cleanup_weak_references()
                actions_taken.append("Cleaned up weak references")
            
            # Optimize memory pools
            if self.config.pool_management_enabled:
                pool_stats = self._optimize_memory_pools()
                actions_taken.append(f"Optimized {len(pool_stats)} memory pools")
            
            # Clear caches if memory is critical
            if self.metrics.memory_level == MemoryLevel.CRITICAL:
                await self._emergency_memory_cleanup()
                actions_taken.append("Emergency memory cleanup")
            
            optimization_time = time.time() - optimization_start
            
            # Update metrics
            self.metrics.optimization_actions += 1
            self.metrics.last_gc_time = datetime.now(timezone.utc)
            self.gc_stats["collections"] += 1
            self.gc_stats["freed_objects"] += freed_objects
            
            # Log optimization
            logger.info(f"🧹 Memory optimization completed in {optimization_time:.2f}s: {', '.join(actions_taken)}")
            
            # Store optimization history
            self.optimization_history.append({
                "timestamp": datetime.now(timezone.utc),
                "optimization_time": optimization_time,
                "actions_taken": actions_taken,
                "memory_before": self.metrics.used_memory_mb,
                "freed_objects": freed_objects
            })
            
            # Keep only last 100 optimizations
            if len(self.optimization_history) > 100:
                self.optimization_history = self.optimization_history[-100:]
            
        except Exception as e:
            logger.error(f"❌ Memory optimization error: {e}")
    
    def _cleanup_weak_references(self):
        """Clean up dead weak references"""
        if not self.object_tracker:
            return
        
        cleaned_count = 0
        for obj_type, refs in self.object_tracker.object_refs.items():
            dead_refs = [ref for ref in refs if ref() is None]
            for ref in dead_refs:
                refs.discard(ref)
                cleaned_count += 1
        
        logger.debug(f"🧹 Cleaned up {cleaned_count} dead weak references")
    
    def _optimize_memory_pools(self) -> Dict[str, Any]:
        """Optimize memory pools"""
        pool_stats = {}
        
        for pool_name, pool in self.memory_pools.items():
            # Get current stats
            stats = pool.get_stats()
            
            # If pool is too large, reduce it
            if stats["pool_size"] > stats["max_size"] * 0.8:
                # Remove some objects from pool
                removed = 0
                while len(pool.pool) > stats["max_size"] // 2:
                    pool.pool.popleft()
                    removed += 1
                
                stats["objects_removed"] = removed
            
            pool_stats[pool_name] = stats
        
        return pool_stats
    
    async def _emergency_memory_cleanup(self):
        """Emergency memory cleanup when memory is critical"""
        logger.warning("⚠️ Performing emergency memory cleanup")
        
        # Force aggressive garbage collection
        for generation in range(3):
            collected = gc.collect(generation)
            logger.info(f"GC generation {generation}: collected {collected} objects")
        
        # Clear all memory pools
        for pool in self.memory_pools.values():
            pool.pool.clear()
        
        # Clear internal caches
        self.memory_history.clear()
        self.optimization_history = self.optimization_history[-10:]  # Keep only last 10
        
        logger.warning("🚨 Emergency memory cleanup completed")
    
    def create_memory_pool(self, name: str, object_factory: Callable, max_size: int = 1000) -> MemoryPool:
        """Create a new memory pool"""
        pool = MemoryPool(object_factory, max_size)
        self.memory_pools[name] = pool
        logger.info(f"🏊 Created memory pool '{name}' with max size {max_size}")
        return pool
    
    def get_memory_pool(self, name: str) -> Optional[MemoryPool]:
        """Get existing memory pool"""
        return self.memory_pools.get(name)
    
    def track_object(self, obj, obj_type: str = None):
        """Track an object for lifecycle monitoring"""
        if self.object_tracker:
            self.object_tracker.track_object(obj, obj_type)
    
    def force_gc(self) -> Dict[str, int]:
        """Force garbage collection and return stats"""
        start_time = time.time()
        
        freed_objects = 0
        for generation in range(3):
            freed = gc.collect(generation)
            freed_objects += freed
        
        gc_time = time.time() - start_time
        
        self.gc_stats["collections"] += 1
        self.gc_stats["freed_objects"] += freed_objects
        
        logger.info(f"🧹 Forced GC: freed {freed_objects} objects in {gc_time:.3f}s")
        
        return {
            "freed_objects": freed_objects,
            "gc_time": gc_time,
            "total_collections": self.gc_stats["collections"]
        }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics"""
        try:
            # Basic memory stats
            stats = {
                "current_metrics": asdict(self.metrics),
                "gc_stats": self.gc_stats.copy(),
                "pool_stats": {}
            }
            
            # Memory pool stats
            for name, pool in self.memory_pools.items():
                stats["pool_stats"][name] = pool.get_stats()
            
            # Object tracking stats
            if self.object_tracker:
                stats["object_stats"] = self.object_tracker.get_object_stats()
                stats["potential_leaks"] = self.object_tracker.detect_potential_leaks()
            
            # Memory trend analysis
            if len(self.memory_history) > 10:
                recent_history = list(self.memory_history)[-10:]
                avg_usage = sum(h["memory_usage_pct"] for h in recent_history) / len(recent_history)
                usage_trend = recent_history[-1]["memory_usage_pct"] - recent_history[0]["memory_usage_pct"]
                
                stats["memory_trends"] = {
                    "avg_usage_last_10": avg_usage,
                    "usage_trend": usage_trend,
                    "trend_direction": "increasing" if usage_trend > 1 else "decreasing" if usage_trend < -1 else "stable"
                }
            
            # Recent optimizations
            if self.optimization_history:
                recent_opts = self.optimization_history[-5:]
                stats["recent_optimizations"] = recent_opts
                stats["optimization_frequency"] = len(self.optimization_history) / max((datetime.now(timezone.utc) - self.optimization_history[0]["timestamp"]).total_seconds() / 3600, 1)
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get memory stats: {e}")
            return {}
    
    def get_memory_recommendations(self) -> List[str]:
        """Get memory optimization recommendations"""
        recommendations = []
        
        try:
            # Check memory level
            if self.metrics.memory_level == MemoryLevel.HIGH:
                recommendations.append("Memory usage is high - consider optimizing data structures")
            elif self.metrics.memory_level == MemoryLevel.CRITICAL:
                recommendations.append("Memory usage is critical - immediate optimization required")
            
            # Check for potential leaks
            if self.metrics.potential_leaks > 3:
                recommendations.append(f"Detected {self.metrics.potential_leaks} potential memory leaks - review object lifecycle")
            
            # Check optimization frequency
            if len(self.optimization_history) > 10:
                recent_opts = len([opt for opt in self.optimization_history if (datetime.now(timezone.utc) - opt["timestamp"]).seconds < 3600])
                if recent_opts > 5:
                    recommendations.append("Frequent memory optimizations - check for memory pressure sources")
            
            # Check pool efficiency
            for name, pool in self.memory_pools.items():
                stats = pool.get_stats()
                if stats["reuse_ratio"] < 0.5:
                    recommendations.append(f"Memory pool '{name}' has low reuse ratio - consider adjusting size or usage patterns")
            
            if not recommendations:
                recommendations.append("Memory usage is optimal - no immediate actions needed")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Failed to generate recommendations: {e}")
            return ["Error generating recommendations"]
    
    async def shutdown(self):
        """Shutdown memory optimizer"""
        try:
            self.running = False
            
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            # Final cleanup
            if self.metrics.memory_level in [MemoryLevel.HIGH, MemoryLevel.CRITICAL]:
                await self._perform_memory_optimization()
            
            # Stop memory profiling
            if self.config.memory_profiling_enabled and tracemalloc.is_tracing():
                tracemalloc.stop()
            
            logger.info("✅ Memory optimizer shutdown successfully")
            
        except Exception as e:
            logger.error(f"❌ Memory optimizer shutdown error: {e}")

# Factory function
async def create_memory_optimizer(config: MemoryConfig = None) -> MemoryOptimizer:
    """Factory function to create and initialize memory optimizer"""
    optimizer = MemoryOptimizer(config)
    await optimizer.initialize()
    return optimizer

# Decorator for automatic object tracking
def track_memory(obj_type: str = None):
    """Decorator to automatically track object lifecycle"""
    def decorator(cls):
        original_init = cls.__init__
        
        def new_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)
            # This would need access to the global memory optimizer instance
            # In practice, you'd inject the optimizer or use a singleton pattern
            
        cls.__init__ = new_init
        return cls
    
    return decorator

# Example usage
if __name__ == "__main__":
    async def main():
        config = MemoryConfig(
            max_memory_usage_pct=75.0,
            memory_check_interval=10,
            object_tracking_enabled=True
        )
        
        optimizer = await create_memory_optimizer(config)
        
        # Create memory pool
        def create_data_object():
            return {"data": [], "timestamp": time.time()}
        
        data_pool = optimizer.create_memory_pool("data_objects", create_data_object, 500)
        
        # Simulate some work
        await asyncio.sleep(5)
        
        # Get stats
        stats = optimizer.get_memory_stats()
        print(f"Memory stats: {stats}")
        
        recommendations = optimizer.get_memory_recommendations()
        print(f"Recommendations: {recommendations}")
        
        await optimizer.shutdown()
    
    asyncio.run(main()) 