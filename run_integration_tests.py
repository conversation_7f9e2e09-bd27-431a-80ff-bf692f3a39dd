#!/usr/bin/env python3
"""
NORYON V2 Integration Test Execution Script

Simple script to run comprehensive integration tests with various options.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle output."""
    print(f"\n🚀 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Run NORYON V2 Integration Tests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_integration_tests.py                    # Run all tests
  python run_integration_tests.py --quick            # Run quick tests only
  python run_integration_tests.py --performance      # Run performance tests only
  python run_integration_tests.py --coverage         # Run with coverage report
  python run_integration_tests.py --verbose          # Run with verbose output
        """
    )
    
    parser.add_argument(
        "--quick", 
        action="store_true",
        help="Run only quick integration tests (skip performance/load tests)"
    )
    
    parser.add_argument(
        "--performance", 
        action="store_true",
        help="Run only performance and load tests"
    )
    
    parser.add_argument(
        "--coverage", 
        action="store_true",
        help="Run tests with coverage reporting"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Run tests with verbose output"
    )
    
    parser.add_argument(
        "--suite",
        choices=[
            "component", "realtime", "agents", "api", 
            "database", "recovery", "performance"
        ],
        help="Run specific test suite only"
    )
    
    parser.add_argument(
        "--report-only",
        action="store_true", 
        help="Generate report from existing test results"
    )
    
    args = parser.parse_args()
    
    print("🎯 NORYON V2 Integration Test Runner")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("tests/integration").exists():
        print("❌ Error: tests/integration directory not found")
        print("Please run this script from the project root directory")
        sys.exit(1)
    
    # Build pytest command
    if args.report_only:
        # Just run the test runner to generate report
        cmd = "python tests/integration/test_runner.py"
        success = run_command(cmd, "Generating test report")
        sys.exit(0 if success else 1)
    
    # Base pytest command
    pytest_cmd = ["python", "-m", "pytest"]
    
    # Determine test path
    if args.suite:
        suite_map = {
            "component": "tests/integration/test_component_integration.py",
            "realtime": "tests/integration/test_realtime_data_flow.py", 
            "agents": "tests/integration/test_ai_agent_coordination.py",
            "api": "tests/integration/test_api_endpoints.py",
            "database": "tests/integration/test_database_integration.py",
            "recovery": "tests/integration/test_error_recovery.py",
            "performance": "tests/integration/test_performance_load.py"
        }
        test_path = suite_map[args.suite]
    elif args.quick:
        # Exclude performance tests for quick run
        test_path = "tests/integration/ -m 'not performance and not slow'"
    elif args.performance:
        # Only performance tests
        test_path = "tests/integration/test_performance_load.py"
    else:
        # All integration tests
        test_path = "tests/integration/"
    
    pytest_cmd.append(test_path)
    
    # Add pytest options
    if args.verbose:
        pytest_cmd.extend(["-v", "-s"])
    else:
        pytest_cmd.append("-v")
    
    pytest_cmd.append("--tb=short")
    
    # Add coverage if requested
    if args.coverage:
        pytest_cmd.extend([
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    # Run the tests
    cmd = " ".join(pytest_cmd)
    success = run_command(cmd, "Running integration tests")
    
    if success:
        print("\n🎉 Integration tests completed successfully!")
        
        if args.coverage:
            print("\n📊 Coverage report generated:")
            print("  - HTML: htmlcov/index.html")
            print("  - XML: coverage.xml")
        
        # Run the comprehensive test runner for detailed report
        print("\n📄 Generating comprehensive test report...")
        run_command(
            "python tests/integration/test_runner.py", 
            "Generating detailed test report"
        )
        
    else:
        print("\n💥 Integration tests failed!")
        print("Check the output above for details on failed tests.")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
