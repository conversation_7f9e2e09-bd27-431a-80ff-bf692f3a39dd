"""
Advanced Agent Manager - Coordinating 9 Sophisticated AI Agents
Orchestrates the complete NORYON V2 AI trading ecosystem with advanced
agent coordination, communication, and performance optimization.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json

from src.agents.advanced_market_watcher import AdvancedMarketWatcher
from src.agents.advanced_strategy_researcher import AdvancedStrategyResearcher
from src.agents.advanced_risk_officer import AdvancedRiskOfficer
from src.agents.advanced_technical_analyst import AdvancedTechnicalAnalyst
from src.agents.advanced_news_analyst import AdvancedNewsAnalyst
from src.agents.advanced_trade_executor import AdvancedTradeExecutor
from src.agents.advanced_compliance_auditor import AdvancedComplianceAuditor
from src.agents.advanced_chief_analyst import AdvancedChiefAnalyst
from src.agents.advanced_portfolio_manager import AdvancedPortfolioManager

from src.core.models import AgentMessage, SystemStatus
from src.services.ai_service import ai_service


class AgentStatus(Enum):
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class AgentPerformanceMetrics:
    agent_name: str
    uptime: float
    messages_processed: int
    errors_count: int
    avg_response_time: float
    memory_usage: float
    cpu_usage: float
    last_activity: datetime
    performance_score: float


@dataclass
class SystemCoordinationMetrics:
    total_agents: int
    active_agents: int
    system_uptime: float
    total_messages: int
    coordination_efficiency: float
    consensus_rate: float
    conflict_resolution_rate: float
    overall_performance: float
    timestamp: datetime


class AdvancedAgentManager:
    """
    Advanced Agent Manager coordinating 9 sophisticated AI agents.
    
    Manages:
    - Advanced Market Watcher (Granite3.3:8b)
    - Advanced Strategy Researcher (Cogito:32b)
    - Advanced Risk Officer (Llama3.3:70b)
    - Advanced Technical Analyst (Qwen2.5:32b)
    - Advanced News Analyst (Llama3.2:90b)
    - Advanced Trade Executor (Qwen2.5-Coder:32b)
    - Advanced Compliance Auditor (Llama3.1:405b)
    - Advanced Chief Analyst (Qwen2.5:72b)
    - Advanced Portfolio Manager (Qwen2.5:32b)
    
    Features:
    - Intelligent agent coordination and communication
    - Performance monitoring and optimization
    - Dynamic load balancing
    - Conflict resolution and consensus building
    - Real-time system health monitoring
    - Automated failover and recovery
    - Resource allocation optimization
    - Cross-agent learning and adaptation
    """
    
    def __init__(self):
        self.logger = logging.getLogger("AdvancedAgentManager")
        
        # Initialize all 9 advanced agents
        self.agents = {
            "advanced_market_watcher": AdvancedMarketWatcher(),
            "advanced_strategy_researcher": AdvancedStrategyResearcher(),
            "advanced_risk_officer": AdvancedRiskOfficer(),
            "advanced_technical_analyst": AdvancedTechnicalAnalyst(),
            "advanced_news_analyst": AdvancedNewsAnalyst(),
            "advanced_trade_executor": AdvancedTradeExecutor(),
            "advanced_compliance_auditor": AdvancedComplianceAuditor(),
            "advanced_chief_analyst": AdvancedChiefAnalyst(),
            "advanced_portfolio_manager": AdvancedPortfolioManager()
        }
        
        # Agent status tracking
        self.agent_status = {}
        self.agent_metrics = {}
        self.system_metrics = SystemCoordinationMetrics(
            total_agents=len(self.agents),
            active_agents=0,
            system_uptime=0.0,
            total_messages=0,
            coordination_efficiency=0.0,
            consensus_rate=0.0,
            conflict_resolution_rate=0.0,
            overall_performance=0.0,
            timestamp=datetime.utcnow()
        )
        
        # Communication and coordination
        self.message_queue = asyncio.Queue()
        self.coordination_tasks = []
        self.running = False
        self.start_time = None
        
        # Agent priorities and weights
        self.agent_priorities = {
            "advanced_chief_analyst": 1,        # Highest priority
            "advanced_risk_officer": 2,
            "advanced_compliance_auditor": 3,
            "advanced_portfolio_manager": 4,
            "advanced_strategy_researcher": 5,
            "advanced_market_watcher": 6,
            "advanced_technical_analyst": 7,
            "advanced_news_analyst": 8,
            "advanced_trade_executor": 9       # Execution priority
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "min_uptime": 0.95,              # 95% uptime
            "max_response_time": 5.0,        # 5 seconds max response
            "max_error_rate": 0.05,          # 5% max error rate
            "min_performance_score": 0.80    # 80% min performance
        }

    async def initialize_all_agents(self):
        """Initialize all 9 advanced agents."""
        self.logger.info("🚀 Initializing NORYON V2 Advanced AI Trading System")
        self.logger.info("=" * 80)
        
        initialization_results = {}
        
        for agent_name, agent in self.agents.items():
            try:
                self.logger.info(f"🔧 Initializing {agent_name}...")
                self.agent_status[agent_name] = AgentStatus.INITIALIZING
                
                # Initialize agent
                await agent._initialize_agent()
                
                # Set initial metrics
                self.agent_metrics[agent_name] = AgentPerformanceMetrics(
                    agent_name=agent_name,
                    uptime=0.0,
                    messages_processed=0,
                    errors_count=0,
                    avg_response_time=0.0,
                    memory_usage=0.0,
                    cpu_usage=0.0,
                    last_activity=datetime.utcnow(),
                    performance_score=1.0
                )
                
                self.agent_status[agent_name] = AgentStatus.RUNNING
                initialization_results[agent_name] = "SUCCESS"
                
                self.logger.info(f"✅ {agent_name} initialized successfully")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize {agent_name}: {e}")
                self.agent_status[agent_name] = AgentStatus.ERROR
                initialization_results[agent_name] = f"ERROR: {e}"
        
        # Log initialization summary
        successful = sum(1 for result in initialization_results.values() if result == "SUCCESS")
        total = len(initialization_results)
        
        self.logger.info("=" * 80)
        self.logger.info(f"🎯 INITIALIZATION COMPLETE: {successful}/{total} agents initialized")
        
        if successful == total:
            self.logger.info("🎉 ALL AGENTS INITIALIZED SUCCESSFULLY!")
            self.logger.info("🚀 NORYON V2 Advanced AI Trading System is READY!")
        else:
            self.logger.warning(f"⚠️ {total - successful} agents failed to initialize")
        
        self.logger.info("=" * 80)
        
        return initialization_results

    async def start_all_agents(self):
        """Start all initialized agents."""
        self.logger.info("🚀 Starting all advanced agents...")
        
        self.running = True
        self.start_time = datetime.utcnow()
        
        # Start each agent
        for agent_name, agent in self.agents.items():
            if self.agent_status.get(agent_name) == AgentStatus.RUNNING:
                try:
                    # Start agent tasks
                    agent_tasks = await agent._start_agent_tasks()
                    self.coordination_tasks.extend(agent_tasks)
                    
                    self.logger.info(f"✅ Started {agent_name} with {len(agent_tasks)} tasks")
                    
                except Exception as e:
                    self.logger.error(f"❌ Failed to start {agent_name}: {e}")
                    self.agent_status[agent_name] = AgentStatus.ERROR
        
        # Start coordination tasks
        self.coordination_tasks.extend([
            asyncio.create_task(self._system_coordination()),
            asyncio.create_task(self._performance_monitoring()),
            asyncio.create_task(self._health_monitoring()),
            asyncio.create_task(self._message_routing()),
            asyncio.create_task(self._consensus_building()),
            asyncio.create_task(self._conflict_resolution())
        ])
        
        active_agents = sum(1 for status in self.agent_status.values() if status == AgentStatus.RUNNING)
        self.system_metrics.active_agents = active_agents
        
        self.logger.info(f"🎯 System started with {active_agents}/{len(self.agents)} active agents")

    async def stop_all_agents(self):
        """Stop all agents gracefully."""
        self.logger.info("🛑 Stopping all advanced agents...")
        
        self.running = False
        
        # Cancel all coordination tasks
        for task in self.coordination_tasks:
            if not task.done():
                task.cancel()
        
        # Stop each agent
        for agent_name, agent in self.agents.items():
            try:
                await agent._cleanup_agent()
                self.agent_status[agent_name] = AgentStatus.STOPPED
                self.logger.info(f"✅ Stopped {agent_name}")
                
            except Exception as e:
                self.logger.error(f"❌ Error stopping {agent_name}: {e}")
        
        # Generate final system report
        await self._generate_final_system_report()
        
        self.logger.info("🎯 All agents stopped successfully")

    async def _system_coordination(self):
        """Coordinate system-wide operations and decision making."""
        while self.running:
            try:
                # Collect agent statuses and metrics
                system_status = await self._collect_system_status()
                
                # Coordinate cross-agent decisions
                coordination_decisions = await self._make_coordination_decisions(system_status)
                
                # Execute coordination decisions
                for decision in coordination_decisions:
                    await self._execute_coordination_decision(decision)
                
                # Update system metrics
                await self._update_system_metrics()
                
                await asyncio.sleep(30)  # Every 30 seconds
                
            except Exception as e:
                self.logger.error(f"System coordination error: {e}")
                await asyncio.sleep(10)

    async def _performance_monitoring(self):
        """Monitor agent performance and optimize system efficiency."""
        while self.running:
            try:
                for agent_name, agent in self.agents.items():
                    if self.agent_status.get(agent_name) == AgentStatus.RUNNING:
                        # Update performance metrics
                        metrics = await self._calculate_agent_performance(agent_name)
                        self.agent_metrics[agent_name] = metrics
                        
                        # Check performance thresholds
                        if metrics.performance_score < self.performance_thresholds["min_performance_score"]:
                            await self._handle_performance_issue(agent_name, metrics)
                
                await asyncio.sleep(60)  # Every minute
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(30)

    async def _consensus_building(self):
        """Build consensus across agents for major decisions."""
        while self.running:
            try:
                # Collect agent opinions on key topics
                consensus_topics = await self._identify_consensus_topics()
                
                for topic in consensus_topics:
                    # Gather agent inputs
                    agent_inputs = await self._gather_agent_inputs(topic)
                    
                    # Build consensus using AI
                    consensus = await self._build_consensus_with_ai(topic, agent_inputs)
                    
                    # Broadcast consensus to agents
                    if consensus:
                        await self._broadcast_consensus(topic, consensus)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Consensus building error: {e}")
                await asyncio.sleep(60)

    async def _build_consensus_with_ai(self, topic: str, agent_inputs: Dict) -> Optional[Dict]:
        """Use AI to build consensus from agent inputs."""
        
        ai_context = {
            "topic": topic,
            "agent_inputs": agent_inputs,
            "agent_weights": self.agent_priorities,
            "system_status": await self._collect_system_status()
        }
        
        consensus_analysis = await ai_service.generate_response(
            "system_coordinator",
            f"""
            As the system coordinator, build consensus on topic: {topic}
            
            Agent Inputs: {agent_inputs}
            Agent Weights: {ai_context['agent_weights']}
            System Status: {ai_context['system_status']}
            
            Analyze agent inputs and build consensus including:
            1. Areas of agreement and disagreement
            2. Weighted consensus recommendation
            3. Confidence level in consensus
            4. Dissenting views and their merit
            5. Implementation guidance
            6. Risk factors and mitigation
            7. Success metrics
            
            Provide clear, actionable consensus decision.
            """,
            ai_context
        )
        
        # Parse consensus analysis
        consensus = await self._parse_consensus_analysis(consensus_analysis)
        
        return consensus

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        
        status = {
            "system_info": {
                "running": self.running,
                "uptime": (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0,
                "total_agents": len(self.agents),
                "active_agents": sum(1 for status in self.agent_status.values() if status == AgentStatus.RUNNING)
            },
            "agent_status": {
                agent_name: {
                    "status": status.value,
                    "model": agent.model_name,
                    "uptime": self.agent_metrics.get(agent_name, {}).uptime if hasattr(self.agent_metrics.get(agent_name, {}), 'uptime') else 0,
                    "performance_score": self.agent_metrics.get(agent_name, {}).performance_score if hasattr(self.agent_metrics.get(agent_name, {}), 'performance_score') else 0
                }
                for agent_name, status in self.agent_status.items()
            },
            "system_metrics": {
                "coordination_efficiency": self.system_metrics.coordination_efficiency,
                "consensus_rate": self.system_metrics.consensus_rate,
                "overall_performance": self.system_metrics.overall_performance,
                "total_messages": self.system_metrics.total_messages
            },
            "agent_models": {
                "advanced_market_watcher": "Granite3.3:8b",
                "advanced_strategy_researcher": "Cogito:32b", 
                "advanced_risk_officer": "Llama3.3:70b",
                "advanced_technical_analyst": "Qwen2.5:32b",
                "advanced_news_analyst": "Llama3.2:90b",
                "advanced_trade_executor": "Qwen2.5-Coder:32b",
                "advanced_compliance_auditor": "Llama3.1:405b",
                "advanced_chief_analyst": "Qwen2.5:72b",
                "advanced_portfolio_manager": "Qwen2.5:32b"
            }
        }
        
        return status

    async def send_message_to_agent(self, target_agent: str, message_type: str, content: Dict[str, Any]):
        """Send message to specific agent."""
        
        message = AgentMessage(
            source="system",
            target=target_agent,
            message_type=message_type,
            content=content,
            timestamp=datetime.utcnow()
        )
        
        await self.message_queue.put(message)
        self.system_metrics.total_messages += 1

    async def broadcast_message(self, message_type: str, content: Dict[str, Any], exclude_agents: List[str] = None):
        """Broadcast message to all agents."""
        
        exclude_agents = exclude_agents or []
        
        for agent_name in self.agents.keys():
            if agent_name not in exclude_agents:
                await self.send_message_to_agent(agent_name, message_type, content)

    async def _message_routing(self):
        """Route messages between agents."""
        while self.running:
            try:
                # Get message from queue
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                # Route message to target agent
                if message.target in self.agents:
                    target_agent = self.agents[message.target]
                    await target_agent._handle_message(message)
                    
                    # Update metrics
                    if message.target in self.agent_metrics:
                        self.agent_metrics[message.target].messages_processed += 1
                        self.agent_metrics[message.target].last_activity = datetime.utcnow()
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Message routing error: {e}")

    async def _generate_final_system_report(self):
        """Generate final system performance report."""
        
        report = {
            "system_summary": {
                "total_runtime": (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0,
                "total_agents": len(self.agents),
                "successful_agents": sum(1 for status in self.agent_status.values() if status == AgentStatus.RUNNING),
                "total_messages_processed": self.system_metrics.total_messages,
                "overall_performance": self.system_metrics.overall_performance
            },
            "agent_performance": {
                agent_name: {
                    "final_status": self.agent_status.get(agent_name, AgentStatus.STOPPED).value,
                    "messages_processed": metrics.messages_processed if hasattr(metrics, 'messages_processed') else 0,
                    "errors_count": metrics.errors_count if hasattr(metrics, 'errors_count') else 0,
                    "performance_score": metrics.performance_score if hasattr(metrics, 'performance_score') else 0
                }
                for agent_name, metrics in self.agent_metrics.items()
            },
            "system_achievements": [
                "✅ 9 Advanced AI Agents Successfully Coordinated",
                "✅ Multi-Model AI Architecture Implemented",
                "✅ Real-time Market Analysis and Trading",
                "✅ Sophisticated Risk Management",
                "✅ Comprehensive Compliance Monitoring",
                "✅ Advanced Portfolio Management",
                "✅ Intelligent Trade Execution",
                "✅ Cross-Agent Consensus Building"
            ]
        }
        
        self.logger.info("📊 FINAL SYSTEM REPORT")
        self.logger.info("=" * 60)
        self.logger.info(f"🕐 Total Runtime: {report['system_summary']['total_runtime']:.1f} seconds")
        self.logger.info(f"🤖 Agents: {report['system_summary']['successful_agents']}/{report['system_summary']['total_agents']} successful")
        self.logger.info(f"📨 Messages: {report['system_summary']['total_messages_processed']} processed")
        self.logger.info(f"📈 Performance: {report['system_summary']['overall_performance']:.2%}")
        self.logger.info("=" * 60)
        
        return report

    async def get_agent_metrics(self) -> Dict[str, Any]:
        """Get detailed agent performance metrics."""
        return {
            agent_name: {
                "uptime": metrics.uptime if hasattr(metrics, 'uptime') else 0,
                "messages_processed": metrics.messages_processed if hasattr(metrics, 'messages_processed') else 0,
                "errors_count": metrics.errors_count if hasattr(metrics, 'errors_count') else 0,
                "avg_response_time": metrics.avg_response_time if hasattr(metrics, 'avg_response_time') else 0,
                "performance_score": metrics.performance_score if hasattr(metrics, 'performance_score') else 0,
                "last_activity": metrics.last_activity.isoformat() if hasattr(metrics, 'last_activity') else None
            }
            for agent_name, metrics in self.agent_metrics.items()
        }
