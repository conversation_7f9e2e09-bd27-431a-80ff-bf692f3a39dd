#!/usr/bin/env python3
"""
🚀 UPDATED MAXIMUM REALISM SYSTEM
With your EXACT 13 Ollama models including the 2 brand new ones!
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("UpdatedMaximumRealismSystem")

@dataclass
class UpdatedRealismAgent:
    """Updated realism AI agent with exact model specifications"""
    model_name: str
    agent_id: str
    initial_balance: float
    current_balance: float
    risk_tolerance: float
    confidence_threshold: float
    max_position_size: float
    personality: str
    trading_style: str
    model_family: str
    model_size_gb: float
    trades_executed: int = 0
    total_pnl: float = 0.0
    win_rate: float = 0.0
    active: bool = True

class UpdatedMaximumRealismSystem:
    """Updated maximum realism system with your exact 13 models"""
    
    def __init__(self):
        self.db_path = "updated_maximum_realism_system.db"
        self.ollama_url = "http://localhost:11434"
        self.session = None
        
        # YOUR EXACT 13 OLLAMA MODELS (with correct names and specifications)
        self.your_exact_models = [
            # BRAND NEW MODELS (just added)
            {
                'name': 'phi4-reasoning:plus', 
                'family': 'phi4', 
                'size_gb': 11.0,
                'personality': 'Microsoft advanced reasoning with enhanced capabilities', 
                'style': 'enhanced_reasoning_trading',
                'specialty': 'Advanced logical reasoning and problem solving'
            },
            {
                'name': 'nemotron-mini:4b', 
                'family': 'nemotron', 
                'size_gb': 2.7,
                'personality': 'NVIDIA compact reasoning specialist', 
                'style': 'efficient_pattern_trading',
                'specialty': 'Compact but powerful pattern recognition'
            },
            
            # EXISTING PROVEN MODELS
            {
                'name': 'hermes3:8b', 
                'family': 'hermes', 
                'size_gb': 4.7,
                'personality': 'Hermes advanced conversational AI trader', 
                'style': 'conversational_analysis_trading',
                'specialty': 'Advanced conversational reasoning and analysis'
            },
            {
                'name': 'marco-o1:7b', 
                'family': 'marco', 
                'size_gb': 4.7,
                'personality': 'Analytical reasoning specialist with mathematical precision', 
                'style': 'technical_analysis_trading',
                'specialty': 'Mathematical and analytical reasoning'
            },
            {
                'name': 'magistral:24b', 
                'family': 'magistral', 
                'size_gb': 14.0,
                'personality': 'Strategic planning expert with long-term vision', 
                'style': 'strategic_long_term_trading',
                'specialty': 'Strategic planning and long-term analysis'
            },
            {
                'name': 'command-r:35b', 
                'family': 'command', 
                'size_gb': 18.0,
                'personality': 'Command and control specialist with systematic approach', 
                'style': 'systematic_command_trading',
                'specialty': 'Command, control, and systematic execution'
            },
            {
                'name': 'cogito:32b', 
                'family': 'cogito', 
                'size_gb': 19.0,
                'personality': 'Philosophical thinking expert with deep market understanding', 
                'style': 'philosophical_fundamental_trading',
                'specialty': 'Deep philosophical thinking and market understanding'
            },
            {
                'name': 'gemma3:27b', 
                'family': 'gemma', 
                'size_gb': 17.0,
                'personality': 'Google advanced reasoning with ML insights', 
                'style': 'ml_driven_google_trading',
                'specialty': 'Google-trained advanced reasoning and ML insights'
            },
            {
                'name': 'mistral-small:24b', 
                'family': 'mistral', 
                'size_gb': 14.0,
                'personality': 'European efficiency specialist with risk management focus', 
                'style': 'european_risk_managed_trading',
                'specialty': 'European efficiency and advanced risk management'
            },
            {
                'name': 'falcon3:10b', 
                'family': 'falcon', 
                'size_gb': 6.3,
                'personality': 'High-speed decision maker with rapid execution', 
                'style': 'high_speed_falcon_trading',
                'specialty': 'High-speed decision making and rapid execution'
            },
            {
                'name': 'granite3.3:8b', 
                'family': 'granite', 
                'size_gb': 4.9,
                'personality': 'Enterprise stability expert with conservative approach', 
                'style': 'enterprise_conservative_trading',
                'specialty': 'Enterprise-grade stability and conservative strategies'
            },
            {
                'name': 'qwen3:32b', 
                'family': 'qwen', 
                'size_gb': 20.0,
                'personality': 'Global perspective specialist with multilingual insights', 
                'style': 'global_multilingual_trading',
                'specialty': 'Global perspective and multilingual market analysis'
            },
            {
                'name': 'deepseek-r1:latest', 
                'family': 'deepseek', 
                'size_gb': 5.2,
                'personality': 'Deep reasoning expert with reflection capabilities', 
                'style': 'deep_reasoning_reflection_trading',
                'specialty': 'Deep reasoning, reflection, and introspective analysis'
            }
        ]
        
        # Active agents
        self.active_agents = {}
        self.agent_portfolios = {}
        
        # Market simulation
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'AVAXUSDT', 'MATICUSDT']
        self.current_prices = {}
        self.price_history = {}
        
        # System state
        self.total_trades = 0
        self.system_start_time = None
        
        self._initialize_updated_database()

    def _initialize_updated_database(self):
        """Initialize database for updated system"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS updated_realism_agents (
                agent_id TEXT PRIMARY KEY,
                model_name TEXT,
                model_family TEXT,
                model_size_gb REAL,
                initial_balance REAL,
                current_balance REAL,
                total_pnl REAL,
                trades_executed INTEGER,
                win_rate REAL,
                personality TEXT,
                trading_style TEXT,
                specialty TEXT,
                created_at TEXT,
                last_updated TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS updated_realism_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                model_family TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                value REAL,
                confidence REAL,
                reasoning TEXT,
                pnl REAL,
                success BOOLEAN,
                market_conditions TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS updated_realism_decisions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                model_name TEXT,
                model_family TEXT,
                prompt TEXT,
                response TEXT,
                decision TEXT,
                confidence REAL,
                executed BOOLEAN,
                response_time_ms REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS updated_market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                price REAL,
                volume REAL,
                volatility REAL,
                rsi REAL,
                sma_20 REAL,
                market_condition TEXT,
                news_sentiment REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Updated database initialized: {self.db_path}")

    def initialize_updated_agents(self):
        """Initialize agents with your exact 13 models"""
        logger.info(f"🤖 Initializing {len(self.your_exact_models)} agents with your EXACT models...")
        
        for model_config in self.your_exact_models:
            try:
                # Create realistic agent configuration based on model size and capabilities
                size_gb = model_config['size_gb']
                
                # Balance based on model size and capabilities
                if size_gb >= 15:  # Large models (command-r:35b, cogito:32b, qwen3:32b, gemma3:27b)
                    balance = random.uniform(30000, 60000)
                    risk = random.uniform(0.01, 0.025)
                    confidence = random.uniform(0.65, 0.8)
                    position = random.uniform(0.15, 0.25)
                elif size_gb >= 10:  # Medium-large models (magistral:24b, mistral-small:24b, phi4-reasoning:plus)
                    balance = random.uniform(20000, 40000)
                    risk = random.uniform(0.015, 0.03)
                    confidence = random.uniform(0.6, 0.75)
                    position = random.uniform(0.12, 0.2)
                elif size_gb >= 5:  # Medium models (falcon3:10b, deepseek-r1:latest, granite3.3:8b, hermes3:8b, marco-o1:7b)
                    balance = random.uniform(15000, 30000)
                    risk = random.uniform(0.02, 0.035)
                    confidence = random.uniform(0.55, 0.7)
                    position = random.uniform(0.1, 0.18)
                else:  # Small efficient models (nemotron-mini:4b)
                    balance = random.uniform(10000, 20000)
                    risk = random.uniform(0.025, 0.04)
                    confidence = random.uniform(0.5, 0.65)
                    position = random.uniform(0.08, 0.15)
                
                # Adjust based on model specialty
                if 'reasoning' in model_config['specialty'].lower():
                    confidence += 0.05  # Reasoning models get higher confidence threshold
                if 'risk' in model_config['style'].lower():
                    risk *= 0.7  # Risk-focused models are more conservative
                if 'speed' in model_config['style'].lower() or 'mini' in model_config['name']:
                    risk *= 1.3  # Speed/mini models take more risk
                
                agent_id = f"{model_config['name'].replace(':', '_').replace('.', '_').replace('-', '_')}_updated"
                
                agent = UpdatedRealismAgent(
                    model_name=model_config['name'],
                    agent_id=agent_id,
                    initial_balance=round(balance, 2),
                    current_balance=round(balance, 2),
                    risk_tolerance=round(risk, 4),
                    confidence_threshold=round(confidence, 2),
                    max_position_size=round(position, 2),
                    personality=model_config['personality'],
                    trading_style=model_config['style'],
                    model_family=model_config['family'],
                    model_size_gb=size_gb
                )
                
                self.active_agents[agent_id] = agent
                self.agent_portfolios[agent_id] = {
                    'cash': balance,
                    'positions': {},
                    'trades': [],
                    'decisions': []
                }
                
                logger.info(f"✅ {model_config['name']} ({size_gb}GB) → {model_config['style']} (${balance:,.0f})")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize {model_config['name']}: {e}")
        
        # Save to database
        self._save_updated_agents()
        
        logger.info(f"🚀 Initialized {len(self.active_agents)} agents with your EXACT models")

    def _save_updated_agents(self):
        """Save agents to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for agent_id, agent in self.active_agents.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO updated_realism_agents 
                    (agent_id, model_name, model_family, model_size_gb, initial_balance, 
                     current_balance, total_pnl, trades_executed, win_rate, personality, 
                     trading_style, specialty, created_at, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    agent.agent_id, agent.model_name, agent.model_family, agent.model_size_gb,
                    agent.initial_balance, agent.current_balance, agent.total_pnl, agent.trades_executed,
                    agent.win_rate, agent.personality, agent.trading_style,
                    next(m['specialty'] for m in self.your_exact_models if m['name'] == agent.model_name),
                    datetime.now().isoformat(), datetime.now().isoformat()
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error saving agents: {e}")

    def simulate_enhanced_realistic_market(self) -> Dict[str, Dict[str, Any]]:
        """Simulate enhanced realistic market with advanced features"""
        market_data = {}
        
        # Realistic base prices (current crypto market)
        base_prices = {
            'BTCUSDT': 97234.0, 'ETHUSDT': 3345.0, 'ADAUSDT': 0.89, 'SOLUSDT': 189.0,
            'DOTUSDT': 7.12, 'LINKUSDT': 22.45, 'AVAXUSDT': 38.67, 'MATICUSDT': 0.85
        }
        
        # Enhanced market conditions
        market_conditions = ['bull_run', 'bear_market', 'sideways_consolidation', 'high_volatility', 'low_volatility']
        current_condition = random.choice(market_conditions)
        
        # Global sentiment factors
        news_sentiment = random.uniform(-0.4, 0.4)  # -1 to 1 scale
        fear_greed_index = random.uniform(20, 80)   # 0 to 100 scale
        
        for symbol in self.crypto_pairs:
            if symbol not in self.price_history:
                self.price_history[symbol] = [base_prices[symbol]]
            
            last_price = self.price_history[symbol][-1]
            
            # Enhanced price movement based on conditions
            if current_condition == 'bull_run':
                trend = 0.003
                volatility = 0.02
            elif current_condition == 'bear_market':
                trend = -0.003
                volatility = 0.025
            elif current_condition == 'high_volatility':
                trend = 0.0
                volatility = 0.05
            elif current_condition == 'low_volatility':
                trend = 0.0
                volatility = 0.008
            else:  # sideways_consolidation
                trend = 0.0
                volatility = 0.015
            
            # Enhanced price calculation with multiple factors
            sentiment_effect = news_sentiment * 0.002
            fear_greed_effect = (fear_greed_index - 50) / 25000  # Normalize to small effect
            random_walk = np.random.normal(trend + sentiment_effect + fear_greed_effect, volatility)
            
            # Apply realistic limits
            max_change = 0.12  # 12% max change per cycle
            random_walk = np.clip(random_walk, -max_change, max_change)
            
            new_price = last_price * (1 + random_walk)
            
            # Update price history
            self.price_history[symbol].append(new_price)
            if len(self.price_history[symbol]) > 100:
                self.price_history[symbol] = self.price_history[symbol][-50:]
            
            self.current_prices[symbol] = new_price
            
            # Calculate enhanced technical indicators
            prices = np.array(self.price_history[symbol])
            
            # Enhanced RSI calculation
            if len(prices) >= 14:
                deltas = np.diff(prices)
                gains = deltas[deltas > 0]
                losses = -deltas[deltas < 0]
                avg_gain = np.mean(gains) if len(gains) > 0 else 0.001
                avg_loss = np.mean(losses) if len(losses) > 0 else 0.001
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            else:
                rsi = 50
            
            # Enhanced moving averages
            sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else new_price
            sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else new_price
            sma_50 = np.mean(prices[-50:]) if len(prices) >= 50 else new_price
            
            # Enhanced volume simulation
            base_volume = random.uniform(2000000, 25000000)
            volatility_factor = abs(random_walk) * 15
            volume = base_volume * (1 + volatility_factor)
            
            market_data[symbol] = {
                'price': new_price,
                'change_24h': random_walk * 100,
                'volume': volume,
                'rsi': rsi,
                'sma_10': sma_10,
                'sma_20': sma_20,
                'sma_50': sma_50,
                'volatility': abs(random_walk),
                'market_condition': current_condition,
                'news_sentiment': news_sentiment,
                'fear_greed_index': fear_greed_index,
                'timestamp': datetime.now().isoformat()
            }
        
        # Save market data
        self._save_enhanced_market_data(market_data)
        
        return market_data

    def _save_enhanced_market_data(self, market_data: Dict[str, Dict[str, Any]]):
        """Save enhanced market data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for symbol, data in market_data.items():
                cursor.execute('''
                    INSERT INTO updated_market_data 
                    (timestamp, symbol, price, volume, volatility, rsi, sma_20, 
                     market_condition, news_sentiment)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['timestamp'], symbol, data['price'], data['volume'], data['volatility'],
                    data['rsi'], data['sma_20'], data['market_condition'], data['news_sentiment']
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error saving market data: {e}")

    def create_enhanced_trading_prompt(self, agent: UpdatedRealismAgent, market_data: Dict[str, Any]) -> str:
        """Create enhanced trading prompt for your exact models"""
        portfolio = self.agent_portfolios[agent.agent_id]

        # Enhanced portfolio calculations
        positions_value = sum(
            pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
            for symbol, pos in portfolio['positions'].items()
        )
        total_value = portfolio['cash'] + positions_value
        total_return = (total_value - agent.initial_balance) / agent.initial_balance * 100

        # Recent performance
        recent_trades = portfolio['trades'][-5:] if len(portfolio['trades']) >= 5 else portfolio['trades']
        recent_pnl = sum(trade.get('pnl', 0) for trade in recent_trades)

        prompt = f"""You are {agent.personality}, an elite AI trader using {agent.model_name}.

MODEL SPECIFICATIONS:
- Model: {agent.model_name} ({agent.model_size_gb}GB)
- Family: {agent.model_family.upper()}
- Trading Style: {agent.trading_style}
- Specialty: Your expertise in advanced market analysis

PORTFOLIO STATUS:
- Total Value: ${total_value:,.2f}
- Cash Available: ${portfolio['cash']:,.2f}
- Positions Value: ${positions_value:,.2f}
- Total Return: {total_return:+.2f}%
- Recent P&L (5 trades): ${recent_pnl:+,.2f}
- Total Trades: {agent.trades_executed}
- Win Rate: {agent.win_rate:.1%}

CURRENT POSITIONS:
"""

        if portfolio['positions']:
            for symbol, pos in portfolio['positions'].items():
                current_price = self.current_prices.get(symbol, pos['avg_price'])
                unrealized_pnl = (current_price - pos['avg_price']) * pos['quantity']
                pnl_pct = (unrealized_pnl / (pos['avg_price'] * pos['quantity'])) * 100

                prompt += f"- {symbol}: {pos['quantity']:.4f} @ ${pos['avg_price']:.2f} → ${current_price:.2f}\n"
                prompt += f"  Unrealized P&L: ${unrealized_pnl:+.2f} ({pnl_pct:+.1f}%)\n"
        else:
            prompt += "- No current positions (100% cash available)\n"

        prompt += f"\nENHANCED MARKET ANALYSIS:\n"

        for symbol, data in market_data.items():
            trend_emoji = "📈" if data['change_24h'] > 0 else "📉"
            rsi_signal = "🔴 Overbought" if data['rsi'] > 70 else "🟢 Oversold" if data['rsi'] < 30 else "🟡 Neutral"

            # SMA trend analysis
            price = data['price']
            sma_20 = data['sma_20']
            sma_trend = "Above" if price > sma_20 else "Below"

            prompt += f"\n{symbol}: ${price:.4f} {trend_emoji} {data['change_24h']:+.2f}%\n"
            prompt += f"  RSI: {data['rsi']:.1f} {rsi_signal}\n"
            prompt += f"  SMA20: ${sma_20:.2f} (Price {sma_trend} SMA)\n"
            prompt += f"  Volume: {data['volume']:,.0f} | Volatility: {data['volatility']:.3f}\n"

        prompt += f"""
MARKET INTELLIGENCE:
- Market Condition: {market_data[list(market_data.keys())[0]]['market_condition'].upper().replace('_', ' ')}
- News Sentiment: {market_data[list(market_data.keys())[0]]['news_sentiment']:+.2f} (-1=very bearish, +1=very bullish)
- Fear & Greed Index: {market_data[list(market_data.keys())[0]]['fear_greed_index']:.0f}/100
- Overall Volatility: {'EXTREME' if max(d['volatility'] for d in market_data.values()) > 0.04 else 'HIGH' if max(d['volatility'] for d in market_data.values()) > 0.025 else 'NORMAL'}

TRADING PARAMETERS:
- Risk Tolerance: {agent.risk_tolerance*100:.1f}%
- Confidence Threshold: {agent.confidence_threshold*100:.0f}%
- Max Position Size: {agent.max_position_size*100:.0f}%
- Available Cash: ${portfolio['cash']:,.2f}

DECISION FRAMEWORK:
As a {agent.trading_style.replace('_', ' ')} specialist using {agent.model_name}, provide your expert analysis.
Use your {agent.model_size_gb}GB model capabilities for comprehensive market analysis.

REQUIRED RESPONSE FORMAT:
MARKET_ANALYSIS: [Your comprehensive market assessment in 2-3 sentences]
DECISION: [BUY/SELL/HOLD]
SYMBOL: [crypto symbol or NONE]
AMOUNT: [dollar amount or NONE]
CONFIDENCE: [0-100]
REASONING: [Your detailed reasoning in 2-3 sentences]
RISK_LEVEL: [LOW/MEDIUM/HIGH]

Provide decisive, expert-level analysis using your {agent.model_name} capabilities."""

        return prompt

    async def call_updated_ollama_model(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Call Ollama model with enhanced configuration"""
        start_time = datetime.now()

        try:
            url = f"{self.ollama_url}/api/generate"

            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.15,  # Lower for more consistent decisions
                    "top_p": 0.9,
                    "max_tokens": 400,
                    "stop": ["---", "END_ANALYSIS"]
                }
            }

            async with self.session.post(url, json=payload, timeout=120) as response:
                end_time = datetime.now()
                response_time_ms = (end_time - start_time).total_seconds() * 1000

                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': model_name,
                        'response_time_ms': response_time_ms,
                        'tokens_used': result.get('eval_count', 0)
                    }
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}",
                        'response_time_ms': response_time_ms
                    }

        except asyncio.TimeoutError:
            return {'success': False, 'error': 'Model timeout (120s)', 'response_time_ms': 120000}
        except Exception as e:
            return {'success': False, 'error': str(e), 'response_time_ms': 0}

    def parse_enhanced_decision(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse enhanced AI decision with validation"""
        try:
            lines = response.strip().split('\n')
            decision = {}

            for line in lines:
                line = line.strip()
                if line.startswith('MARKET_ANALYSIS:'):
                    decision['market_analysis'] = line.split(':', 1)[1].strip()
                elif line.startswith('DECISION:'):
                    decision['action'] = line.split(':', 1)[1].strip().upper()
                elif line.startswith('SYMBOL:'):
                    symbol = line.split(':', 1)[1].strip().upper()
                    decision['symbol'] = symbol if symbol != 'NONE' else None
                elif line.startswith('AMOUNT:'):
                    amount_str = line.split(':', 1)[1].strip()
                    if amount_str != 'NONE':
                        try:
                            # Extract number from string
                            amount_clean = ''.join(c for c in amount_str if c.isdigit() or c == '.')
                            decision['amount'] = float(amount_clean) if amount_clean else 0
                        except:
                            decision['amount'] = 0
                    else:
                        decision['amount'] = 0
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        decision['confidence'] = min(100, max(0, confidence))
                    except:
                        decision['confidence'] = 50
                elif line.startswith('REASONING:'):
                    decision['reasoning'] = line.split(':', 1)[1].strip()
                elif line.startswith('RISK_LEVEL:'):
                    decision['risk_level'] = line.split(':', 1)[1].strip().upper()

            # Validate decision
            if 'action' in decision and decision['action'] in ['BUY', 'SELL', 'HOLD']:
                return decision
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error parsing enhanced decision: {e}")
            return None

    async def execute_enhanced_trading_session(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute enhanced trading session with all your models"""
        session_results = {
            'decisions': {},
            'trades_executed': 0,
            'total_volume': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'avg_response_time': 0,
            'avg_confidence': 0
        }

        logger.info(f"🚀 Starting enhanced trading session with {len(self.active_agents)} of your exact models")

        response_times = []
        confidences = []

        for agent_id, agent in self.active_agents.items():
            if not agent.active:
                continue

            try:
                logger.info(f"🤖 Consulting {agent.model_name} ({agent.model_size_gb}GB, {agent.model_family})...")

                # Create enhanced prompt
                prompt = self.create_enhanced_trading_prompt(agent, market_data)

                # Call your exact model
                result = await self.call_updated_ollama_model(agent.model_name, prompt)

                if result['success']:
                    response_times.append(result['response_time_ms'])

                    # Parse enhanced decision
                    decision = self.parse_enhanced_decision(result['response'])

                    if decision:
                        confidences.append(decision.get('confidence', 0))

                        # Execute trade if not HOLD
                        trade_executed = False
                        trade_details = None

                        if decision['action'] != 'HOLD' and decision.get('symbol'):
                            trade_executed, trade_details = self.execute_enhanced_trade(agent_id, decision, market_data)

                            if trade_executed:
                                session_results['trades_executed'] += 1
                                session_results['total_volume'] += trade_details.get('value', 0)

                        # Save enhanced decision
                        self.save_enhanced_decision(agent_id, prompt, result, decision, trade_executed)

                        session_results['decisions'][agent_id] = {
                            'agent': agent,
                            'decision': decision,
                            'trade_executed': trade_executed,
                            'trade_details': trade_details,
                            'response_time_ms': result['response_time_ms']
                        }

                        session_results['successful_calls'] += 1

                        # Enhanced logging with emojis
                        action_emoji = "🟢" if decision['action'] == 'BUY' else "🔴" if decision['action'] == 'SELL' else "⚪"
                        risk_emoji = "🔥" if decision.get('risk_level') == 'HIGH' else "⚠️" if decision.get('risk_level') == 'MEDIUM' else "✅"
                        size_emoji = "🐋" if agent.model_size_gb >= 15 else "🐟" if agent.model_size_gb >= 10 else "🦐"

                        logger.info(f"   {action_emoji} {agent.model_name} {size_emoji}: {decision['action']} "
                                  f"{decision.get('symbol', 'N/A')} (Confidence: {decision.get('confidence', 0):.0f}%) {risk_emoji}")

                        if trade_executed and trade_details:
                            logger.info(f"      💰 Trade executed: ${trade_details.get('value', 0):,.0f}")
                    else:
                        logger.warning(f"   ⚠️ {agent.model_name}: Could not parse decision")
                        session_results['failed_calls'] += 1
                else:
                    logger.error(f"   ❌ {agent.model_name}: {result.get('error', 'Unknown error')}")
                    session_results['failed_calls'] += 1

                # Rate limiting between calls
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"❌ Error with {agent_id}: {e}")
                session_results['failed_calls'] += 1

        # Calculate session statistics
        if response_times:
            session_results['avg_response_time'] = sum(response_times) / len(response_times)
        if confidences:
            session_results['avg_confidence'] = sum(confidences) / len(confidences)

        self.total_trades += session_results['trades_executed']

        logger.info(f"✅ Enhanced session complete: {session_results['trades_executed']} trades, "
                   f"{session_results['successful_calls']} successful calls, "
                   f"avg confidence: {session_results['avg_confidence']:.1f}%")

        return session_results

    def execute_enhanced_trade(self, agent_id: str, decision: Dict[str, Any], market_data: Dict[str, Any]) -> tuple[bool, Optional[Dict[str, Any]]]:
        """Execute enhanced trade with your exact models"""
        try:
            agent = self.active_agents[agent_id]
            portfolio = self.agent_portfolios[agent_id]

            symbol = decision['symbol']
            action = decision['action']
            amount = decision.get('amount', 0)
            confidence = decision.get('confidence', 0)
            risk_level = decision.get('risk_level', 'MEDIUM')

            if not symbol or symbol not in market_data:
                return False, None

            current_price = market_data[symbol]['price']

            # Enhanced confidence validation based on model size
            base_threshold = agent.confidence_threshold * 100

            # Adjust threshold based on model size (larger models can be more trusted)
            if agent.model_size_gb >= 15:
                confidence_threshold = base_threshold - 5  # Large models get 5% bonus
            elif agent.model_size_gb >= 10:
                confidence_threshold = base_threshold
            else:
                confidence_threshold = base_threshold + 5  # Small models need higher confidence

            # Adjust for risk level
            if risk_level == 'HIGH':
                confidence_threshold += 10
            elif risk_level == 'LOW':
                confidence_threshold -= 5

            if confidence < confidence_threshold:
                logger.info(f"      ⚠️ Trade rejected: Confidence {confidence:.0f}% < threshold {confidence_threshold:.0f}%")
                return False, None

            # Enhanced trade size validation
            portfolio_value = portfolio['cash'] + sum(
                pos['quantity'] * self.current_prices.get(sym, pos['avg_price'])
                for sym, pos in portfolio['positions'].items()
            )

            # Minimum trade based on model size
            min_trade = 1000 if agent.model_size_gb >= 15 else 750 if agent.model_size_gb >= 10 else 500

            if amount < min_trade:
                logger.info(f"      ⚠️ Trade rejected: Amount ${amount:.0f} < minimum ${min_trade}")
                return False, None

            if action == 'BUY':
                # Enhanced buy validation
                if amount > portfolio['cash']:
                    amount = portfolio['cash'] * 0.95  # Use 95% of available cash
                    logger.info(f"      📉 Trade size adjusted to ${amount:.0f} (cash limit)")

                # Position size limits based on model confidence
                max_position_value = agent.max_position_size * portfolio_value
                if amount > max_position_value:
                    amount = max_position_value
                    logger.info(f"      📉 Trade size reduced to ${amount:.0f} (position limit)")

                if amount < min_trade:
                    return False, None

                quantity = amount / current_price

                # Execute enhanced buy
                portfolio['cash'] -= amount

                if symbol not in portfolio['positions']:
                    portfolio['positions'][symbol] = {'quantity': 0, 'avg_price': 0}

                pos = portfolio['positions'][symbol]
                total_quantity = pos['quantity'] + quantity
                total_cost = (pos['quantity'] * pos['avg_price']) + amount

                portfolio['positions'][symbol] = {
                    'quantity': total_quantity,
                    'avg_price': total_cost / total_quantity
                }

                # Create enhanced trade record
                trade_details = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'model_family': agent.model_family,
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': current_price,
                    'value': amount,
                    'confidence': confidence,
                    'reasoning': decision.get('reasoning', ''),
                    'market_analysis': decision.get('market_analysis', ''),
                    'risk_level': risk_level,
                    'pnl': 0,
                    'success': None,
                    'market_conditions': market_data[symbol]['market_condition']
                }

                portfolio['trades'].append(trade_details)
                agent.trades_executed += 1

                self.save_enhanced_trade(trade_details)

                return True, trade_details

            elif action == 'SELL':
                if symbol not in portfolio['positions']:
                    logger.info(f"      ⚠️ Sell rejected: No position in {symbol}")
                    return False, None

                pos = portfolio['positions'][symbol]
                if pos['quantity'] <= 0:
                    logger.info(f"      ⚠️ Sell rejected: Zero quantity in {symbol}")
                    return False, None

                # Enhanced sell quantity calculation
                max_sellable_value = pos['quantity'] * current_price

                if amount > 0:
                    if amount > max_sellable_value:
                        amount = max_sellable_value
                    sell_quantity = amount / current_price
                else:
                    # Sell based on confidence and risk level
                    if risk_level == 'HIGH':
                        sell_ratio = min(0.8, confidence / 100)  # Up to 80% for high risk
                    elif risk_level == 'LOW':
                        sell_ratio = min(0.3, confidence / 100)  # Up to 30% for low risk
                    else:
                        sell_ratio = min(0.5, confidence / 100)  # Up to 50% for medium risk

                    sell_quantity = pos['quantity'] * sell_ratio
                    amount = sell_quantity * current_price

                # Calculate enhanced P&L
                cost_basis = sell_quantity * pos['avg_price']
                pnl = amount - cost_basis

                # Execute enhanced sell
                portfolio['cash'] += amount
                pos['quantity'] -= sell_quantity

                if pos['quantity'] < 0.000001:
                    del portfolio['positions'][symbol]

                # Create enhanced trade record
                trade_details = {
                    'timestamp': datetime.now().isoformat(),
                    'agent_id': agent_id,
                    'model_name': agent.model_name,
                    'model_family': agent.model_family,
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'price': current_price,
                    'value': amount,
                    'confidence': confidence,
                    'reasoning': decision.get('reasoning', ''),
                    'market_analysis': decision.get('market_analysis', ''),
                    'risk_level': risk_level,
                    'pnl': pnl,
                    'success': pnl > 0,
                    'market_conditions': market_data[symbol]['market_condition']
                }

                portfolio['trades'].append(trade_details)
                agent.trades_executed += 1
                agent.total_pnl += pnl

                # Update enhanced win rate
                winning_trades = sum(1 for t in portfolio['trades'] if t.get('pnl', 0) > 0)
                agent.win_rate = winning_trades / len(portfolio['trades']) if portfolio['trades'] else 0

                self.save_enhanced_trade(trade_details)

                return True, trade_details

            return False, None

        except Exception as e:
            logger.error(f"❌ Enhanced trade execution error: {e}")
            return False, None

    def save_enhanced_trade(self, trade: Dict[str, Any]):
        """Save enhanced trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO updated_realism_trades
                (timestamp, agent_id, model_name, model_family, symbol, action, quantity,
                 price, value, confidence, reasoning, pnl, success, market_conditions)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade['timestamp'], trade['agent_id'], trade['model_name'], trade['model_family'],
                trade['symbol'], trade['action'], trade['quantity'], trade['price'], trade['value'],
                trade['confidence'], trade['reasoning'], trade['pnl'], trade['success'],
                trade['market_conditions']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving enhanced trade: {e}")

    def save_enhanced_decision(self, agent_id: str, prompt: str, result: Dict[str, Any], decision: Dict[str, Any], executed: bool):
        """Save enhanced AI decision"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO updated_realism_decisions
                (timestamp, agent_id, model_name, model_family, prompt, response, decision,
                 confidence, executed, response_time_ms)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(), agent_id, self.active_agents[agent_id].model_name,
                self.active_agents[agent_id].model_family, prompt, result['response'],
                json.dumps(decision), decision.get('confidence', 0), executed,
                result.get('response_time_ms', 0)
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"❌ Error saving enhanced decision: {e}")

    def display_enhanced_status(self):
        """Display enhanced comprehensive status for your exact models"""
        print(f"\n🚀 UPDATED MAXIMUM REALISM SYSTEM STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 120)

        # Enhanced system overview
        total_value = 0
        total_initial = 0
        total_trades = 0
        total_pnl = 0

        print(f"📊 SYSTEM OVERVIEW:")
        print(f"   Your Exact Models: {len(self.your_exact_models)}")
        print(f"   Active Agents: {len(self.active_agents)}")
        print(f"   Total Trades: {self.total_trades}")
        print(f"   New Models: phi4-reasoning:plus, nemotron-mini:4b ⭐")

        if self.system_start_time:
            uptime = (datetime.now() - self.system_start_time).total_seconds() / 3600
            print(f"   System Uptime: {uptime:.2f} hours")

        print(f"\n🤖 YOUR EXACT MODELS PERFORMANCE:")
        print(f"{'Model Name':<25} {'Size':<8} {'Family':<12} {'Balance':<12} {'Return':<8} {'Trades':<7} {'Win Rate':<9} {'Style':<25}")
        print("-" * 120)

        # Enhanced performance tracking
        agent_performance = []
        for agent_id, agent in self.active_agents.items():
            portfolio = self.agent_portfolios[agent_id]

            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )

            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            total_value += portfolio_value
            total_initial += agent.initial_balance
            total_trades += agent.trades_executed
            total_pnl += agent.total_pnl

            agent_performance.append({
                'agent': agent,
                'portfolio_value': portfolio_value,
                'return': agent_return
            })

        # Sort by performance
        agent_performance.sort(key=lambda x: x['return'], reverse=True)

        # Display agents with enhanced info
        for i, perf in enumerate(agent_performance):
            agent = perf['agent']

            # Enhanced status indicators
            if perf['return'] > 5:
                status_emoji = "🟢"
            elif perf['return'] > 0:
                status_emoji = "🟡"
            elif perf['return'] > -5:
                status_emoji = "🟠"
            else:
                status_emoji = "🔴"

            # Rank indicators
            rank_emoji = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else "  "

            # Model size indicator
            size_emoji = "🐋" if agent.model_size_gb >= 15 else "🐟" if agent.model_size_gb >= 10 else "🦐"

            # New model indicator
            new_emoji = "⭐" if agent.model_name in ['phi4-reasoning:plus', 'nemotron-mini:4b'] else "  "

            print(f"{agent.model_name:<25} {agent.model_size_gb:>6.1f}GB {agent.model_family:<12} "
                  f"${perf['portfolio_value']:>10,.0f} {perf['return']:>+6.1f}% {agent.trades_executed:>6d} "
                  f"{agent.win_rate:>7.1%} {agent.trading_style:<25} {status_emoji} {rank_emoji} {size_emoji} {new_emoji}")

        # Enhanced system totals
        system_return = (total_value - total_initial) / total_initial * 100 if total_initial > 0 else 0

        print("-" * 120)
        print(f"{'SYSTEM TOTALS':<25} {'ALL':<8} {'ALL':<12} ${total_value:>10,.0f} "
              f"{system_return:>+6.1f}% {total_trades:>6d} {'N/A':>7} {'ALL STRATEGIES':<25}")

        # Enhanced model family performance
        print(f"\n📈 MODEL FAMILY PERFORMANCE:")
        family_stats = {}
        for agent_id, agent in self.active_agents.items():
            family = agent.model_family
            if family not in family_stats:
                family_stats[family] = {'count': 0, 'total_return': 0, 'total_trades': 0, 'total_size': 0}

            portfolio = self.agent_portfolios[agent_id]
            positions_value = sum(
                pos['quantity'] * self.current_prices.get(symbol, pos['avg_price'])
                for symbol, pos in portfolio['positions'].items()
            )
            portfolio_value = portfolio['cash'] + positions_value
            agent_return = (portfolio_value - agent.initial_balance) / agent.initial_balance * 100

            family_stats[family]['count'] += 1
            family_stats[family]['total_return'] += agent_return
            family_stats[family]['total_trades'] += agent.trades_executed
            family_stats[family]['total_size'] += agent.model_size_gb

        for family, stats in sorted(family_stats.items(), key=lambda x: x[1]['total_return'], reverse=True):
            avg_return = stats['total_return'] / stats['count'] if stats['count'] > 0 else 0
            avg_size = stats['total_size'] / stats['count'] if stats['count'] > 0 else 0

            # Family emoji
            family_emoji = "🆕" if family in ['phi4', 'nemotron'] else "🔥" if avg_return > 2 else "📈" if avg_return > 0 else "📉"

            print(f"   {family.upper():<12} {family_emoji} | Models: {stats['count']:2d} | Avg Size: {avg_size:4.1f}GB | "
                  f"Avg Return: {avg_return:+6.1f}% | Trades: {stats['total_trades']:3d}")

        # Enhanced current market
        if self.current_prices:
            print(f"\n📊 CURRENT MARKET CONDITIONS:")
            for symbol, price in self.current_prices.items():
                if symbol in self.price_history and len(self.price_history[symbol]) >= 2:
                    prev_price = self.price_history[symbol][-2]
                    change = (price - prev_price) / prev_price * 100

                    # Enhanced trend indicators
                    if change > 2:
                        trend_emoji = "🚀"
                    elif change > 0:
                        trend_emoji = "📈"
                    elif change < -2:
                        trend_emoji = "💥"
                    else:
                        trend_emoji = "📉"

                    volatility = abs(change) / 100
                    vol_indicator = "🔥" if volatility > 0.03 else "⚡" if volatility > 0.015 else "🟢"

                    print(f"   {symbol:10} | ${price:>10.4f} | {change:>+6.2f}% {trend_emoji} | Vol: {volatility:.3f} {vol_indicator}")

        print("=" * 120)

    async def run_updated_maximum_realism_system(self, duration_minutes: int = 90):
        """Run updated maximum realism system with your exact 13 models"""
        logger.info(f"🚀 Starting UPDATED MAXIMUM REALISM SYSTEM for {duration_minutes} minutes")
        logger.info(f"   Your Exact Models: {len(self.your_exact_models)}")
        logger.info(f"   New Models: phi4-reasoning:plus (11GB), nemotron-mini:4b (2.7GB)")

        self.session = aiohttp.ClientSession()
        self.system_start_time = datetime.now()

        try:
            # Initialize agents with your exact models
            self.initialize_updated_agents()

            # Main enhanced trading loop
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0

            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Enhanced trading cycle {cycle_count}")

                # Generate enhanced realistic market
                market_data = self.simulate_enhanced_realistic_market()

                # Execute enhanced trading session
                session_results = await self.execute_enhanced_trading_session(market_data)

                # Display enhanced status every 2 cycles
                if cycle_count % 2 == 0:
                    self.display_enhanced_status()

                # Enhanced cycle summary
                logger.info(f"   📊 Cycle {cycle_count} summary: {session_results['trades_executed']} trades, "
                          f"avg confidence: {session_results.get('avg_confidence', 0):.1f}%, "
                          f"avg response time: {session_results.get('avg_response_time', 0):.0f}ms")

                # Wait before next cycle
                await asyncio.sleep(300)  # 5 minutes between cycles

            # Final enhanced status
            print(f"\n🏁 UPDATED MAXIMUM REALISM SYSTEM COMPLETED")
            print(f"Total cycles: {cycle_count}")
            print(f"Total trades executed: {self.total_trades}")
            print(f"Total runtime: {(datetime.now() - self.system_start_time).total_seconds() / 3600:.2f} hours")

            self.display_enhanced_status()

        except KeyboardInterrupt:
            logger.info("🛑 System stopped by user")
        finally:
            await self.session.close()

async def main():
    """Main function for updated maximum realism system"""
    print("🚀 UPDATED MAXIMUM REALISM SYSTEM")
    print("=" * 80)
    print("Features:")
    print("• Your EXACT 13 Ollama models (including 2 brand new ones)")
    print("• Enhanced realistic market simulation")
    print("• Real AI decision making with your models")
    print("• Actual trade execution with P&L tracking")
    print("• Model size-based performance optimization")
    print("• Enhanced family performance comparison")
    print("• New model highlighting and tracking")
    print("=" * 80)

    system = UpdatedMaximumRealismSystem()

    print(f"\n🤖 YOUR EXACT 13 MODELS:")
    for model in system.your_exact_models:
        new_indicator = "⭐ NEW!" if model['name'] in ['phi4-reasoning:plus', 'nemotron-mini:4b'] else ""
        print(f"   {model['name']:<25} | {model['size_gb']:>6.1f}GB | {model['family']:<12} | {model['style']} {new_indicator}")

    print(f"\n🎯 Choose duration:")
    print(f"1. Quick test (30 minutes)")
    print(f"2. Standard run (90 minutes)")
    print(f"3. Extended run (3 hours)")
    print(f"4. Custom duration")

    try:
        choice = input("Enter choice (1-4, default=2): ").strip()
        if choice == "1":
            duration = 30
        elif choice == "3":
            duration = 180
        elif choice == "4":
            duration = int(input("Enter duration in minutes: "))
        else:
            duration = 90
    except:
        duration = 90

    print(f"\n🚀 Starting updated system with your exact models for {duration} minutes...")
    print(f"Press Ctrl+C to stop early")

    # Run updated system
    await system.run_updated_maximum_realism_system(duration_minutes=duration)

    print(f"\n✅ Updated maximum realism system completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
