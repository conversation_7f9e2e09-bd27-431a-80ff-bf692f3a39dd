#!/usr/bin/env python3
"""
🚀 AUTONOMOUS ENHANCED TRADING
Autonomous trading system with enhanced models and real data
"""

import asyncio
import sqlite3
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enhanced_model_deployment import EnhancedModelDeployment
from enhanced_data_pipeline import EnhancedDataPipeline

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AutonomousEnhancedTrading")

@dataclass
class EnhancedTrade:
    timestamp: str
    symbol: str
    action: str  # BUY, SELL, HOLD
    quantity: float
    price: float
    confidence: float
    prediction: float
    signal_strength: str
    reasoning: str
    agent_id: str
    model_used: str

@dataclass
class EnhancedPortfolio:
    cash_balance: float
    positions: Dict[str, Dict[str, float]]
    total_value: float
    unrealized_pnl: float
    realized_pnl: float
    total_trades: int
    win_rate: float
    sharpe_ratio: float

class EnhancedAutonomousAgent:
    """Enhanced autonomous trading agent with advanced models"""
    
    def __init__(self, agent_id: str, initial_balance: float = 5000.0, strategy: str = "balanced"):
        self.agent_id = agent_id
        self.strategy = strategy
        self.initial_balance = initial_balance
        
        # Portfolio management
        self.portfolio = EnhancedPortfolio(
            cash_balance=initial_balance,
            positions={},
            total_value=initial_balance,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            total_trades=0,
            win_rate=0.0,
            sharpe_ratio=0.0
        )
        
        # Trading parameters based on strategy
        self.trading_params = self._get_strategy_params(strategy)
        
        # Trade history
        self.trade_history = []
        self.performance_history = []
        
        # Model deployment
        self.model_deployment = EnhancedModelDeployment()

    def _get_strategy_params(self, strategy: str) -> Dict[str, float]:
        """Get trading parameters based on strategy"""
        strategies = {
            "conservative": {
                "max_position_size": 0.05,  # 5% max per position
                "min_confidence": 0.8,      # High confidence required
                "risk_tolerance": 0.01,     # 1% max risk per trade
                "rebalance_threshold": 0.02 # 2% rebalance threshold
            },
            "balanced": {
                "max_position_size": 0.1,   # 10% max per position
                "min_confidence": 0.6,      # Medium confidence required
                "risk_tolerance": 0.02,     # 2% max risk per trade
                "rebalance_threshold": 0.05 # 5% rebalance threshold
            },
            "aggressive": {
                "max_position_size": 0.2,   # 20% max per position
                "min_confidence": 0.4,      # Lower confidence accepted
                "risk_tolerance": 0.05,     # 5% max risk per trade
                "rebalance_threshold": 0.1  # 10% rebalance threshold
            }
        }
        return strategies.get(strategy, strategies["balanced"])

    async def analyze_market_and_predict(self) -> List[Dict[str, Any]]:
        """Analyze market using enhanced models and generate predictions"""
        try:
            # Generate predictions using enhanced models
            predictions = self.model_deployment.generate_real_time_predictions()
            
            if not predictions:
                logger.warning(f"{self.agent_id}: No predictions available")
                return []
            
            # Filter predictions based on strategy
            filtered_predictions = []
            
            for pred in predictions:
                confidence = pred['ensemble_confidence']
                signal = pred['signal']
                
                # Apply strategy filters
                if confidence >= self.trading_params['min_confidence']:
                    # Add strategy-specific analysis
                    enhanced_pred = pred.copy()
                    enhanced_pred['strategy_score'] = self._calculate_strategy_score(pred)
                    enhanced_pred['position_size'] = self._calculate_position_size(pred)
                    
                    filtered_predictions.append(enhanced_pred)
            
            logger.info(f"{self.agent_id}: {len(filtered_predictions)} predictions passed strategy filter")
            return filtered_predictions
            
        except Exception as e:
            logger.error(f"{self.agent_id}: Market analysis error: {e}")
            return []

    def _calculate_strategy_score(self, prediction: Dict[str, Any]) -> float:
        """Calculate strategy-specific score for prediction"""
        base_score = prediction['ensemble_confidence']
        prediction_magnitude = abs(prediction['ensemble_prediction'])
        signal_strength = prediction['signal']['strength']
        
        # Adjust score based on strategy
        if self.strategy == "conservative":
            # Prefer high confidence, lower volatility
            score = base_score * 0.7 + (1 - prediction_magnitude) * 0.3
        elif self.strategy == "aggressive":
            # Prefer high magnitude predictions
            score = base_score * 0.5 + prediction_magnitude * 0.5
        else:  # balanced
            score = base_score * 0.6 + prediction_magnitude * 0.4
        
        # Boost for strong signals
        if signal_strength == "STRONG":
            score *= 1.2
        elif signal_strength == "WEAK":
            score *= 0.8
        
        return min(1.0, score)

    def _calculate_position_size(self, prediction: Dict[str, Any]) -> float:
        """Calculate position size based on prediction and strategy"""
        strategy_score = prediction.get('strategy_score', 0.5)
        confidence = prediction['ensemble_confidence']
        
        # Base position size from strategy
        base_size = self.trading_params['max_position_size']
        
        # Adjust based on confidence and strategy score
        size_multiplier = (confidence * 0.6 + strategy_score * 0.4)
        
        # Calculate final position size
        position_size = base_size * size_multiplier
        
        # Ensure we have enough cash
        available_cash_ratio = self.portfolio.cash_balance / self.portfolio.total_value
        max_affordable_size = available_cash_ratio * 0.9  # Keep 10% cash buffer
        
        return min(position_size, max_affordable_size)

    def make_trading_decisions(self, predictions: List[Dict[str, Any]]) -> List[EnhancedTrade]:
        """Make trading decisions based on predictions"""
        decisions = []
        
        for pred in predictions:
            symbol = pred['symbol']
            signal = pred['signal']
            action = signal['action']
            
            if action == 'HOLD':
                continue
            
            # Calculate trade parameters
            position_size = pred['position_size']
            current_price = self._get_current_price(symbol)
            
            if current_price is None:
                continue
            
            # Calculate quantity
            if action == 'BUY':
                trade_value = self.portfolio.total_value * position_size
                quantity = trade_value / current_price
                
                # Check if we have enough cash
                if trade_value > self.portfolio.cash_balance:
                    continue
                    
            elif action == 'SELL':
                # Check if we have position to sell
                current_position = self.portfolio.positions.get(symbol, {}).get('quantity', 0)
                if current_position <= 0:
                    continue
                
                # Sell portion based on signal strength
                if signal['strength'] == 'STRONG':
                    sell_ratio = 0.5  # Sell 50%
                elif signal['strength'] == 'WEAK':
                    sell_ratio = 0.25  # Sell 25%
                else:
                    sell_ratio = 0.3   # Default 30%
                
                quantity = current_position * sell_ratio
            else:
                continue
            
            # Create trade decision
            trade = EnhancedTrade(
                timestamp=datetime.now().isoformat(),
                symbol=symbol,
                action=action,
                quantity=quantity,
                price=current_price,
                confidence=pred['ensemble_confidence'],
                prediction=pred['ensemble_prediction'],
                signal_strength=signal['strength'],
                reasoning=signal['reason'],
                agent_id=self.agent_id,
                model_used="enhanced_ensemble"
            )
            
            decisions.append(trade)
        
        logger.info(f"{self.agent_id}: Generated {len(decisions)} trading decisions")
        return decisions

    def execute_trade(self, trade: EnhancedTrade) -> bool:
        """Execute a trading decision"""
        try:
            # Simulate realistic execution with slippage and fees
            slippage = np.random.uniform(0.0001, 0.001)  # 0.01-0.1% slippage
            fee_rate = 0.001  # 0.1% trading fee
            
            if trade.action == 'BUY':
                # Apply slippage (price goes up when buying)
                execution_price = trade.price * (1 + slippage)
                trade_value = trade.quantity * execution_price
                total_cost = trade_value * (1 + fee_rate)
                
                if total_cost <= self.portfolio.cash_balance:
                    # Update cash
                    self.portfolio.cash_balance -= total_cost
                    
                    # Update position
                    if trade.symbol not in self.portfolio.positions:
                        self.portfolio.positions[trade.symbol] = {'quantity': 0, 'avg_price': 0}
                    
                    pos = self.portfolio.positions[trade.symbol]
                    total_quantity = pos['quantity'] + trade.quantity
                    total_cost_basis = (pos['quantity'] * pos['avg_price']) + trade_value
                    
                    self.portfolio.positions[trade.symbol] = {
                        'quantity': total_quantity,
                        'avg_price': total_cost_basis / total_quantity if total_quantity > 0 else 0
                    }
                    
                    # Update trade with actual execution price
                    trade.price = execution_price
                    
                    logger.info(f"{self.agent_id}: BUY {trade.quantity:.6f} {trade.symbol} @ ${execution_price:.4f}")
                    return True
                    
            elif trade.action == 'SELL':
                if trade.symbol in self.portfolio.positions:
                    pos = self.portfolio.positions[trade.symbol]
                    
                    if pos['quantity'] >= trade.quantity:
                        # Apply slippage (price goes down when selling)
                        execution_price = trade.price * (1 - slippage)
                        trade_value = trade.quantity * execution_price
                        net_proceeds = trade_value * (1 - fee_rate)
                        
                        # Calculate realized P&L
                        cost_basis = trade.quantity * pos['avg_price']
                        realized_pnl = net_proceeds - cost_basis
                        self.portfolio.realized_pnl += realized_pnl
                        
                        # Update cash and position
                        self.portfolio.cash_balance += net_proceeds
                        pos['quantity'] -= trade.quantity
                        
                        # Remove position if quantity is negligible
                        if pos['quantity'] < 0.000001:
                            del self.portfolio.positions[trade.symbol]
                        
                        # Update trade with actual execution price
                        trade.price = execution_price
                        
                        logger.info(f"{self.agent_id}: SELL {trade.quantity:.6f} {trade.symbol} @ ${execution_price:.4f} (P&L: ${realized_pnl:.2f})")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"{self.agent_id}: Trade execution error: {e}")
            return False

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol (simplified)"""
        # In real implementation, this would fetch from live data
        # For demo, use simulated prices
        base_prices = {
            'BTCUSDT': 45000, 'ETHUSDT': 2500, 'ADAUSDT': 0.45, 
            'SOLUSDT': 95, 'DOTUSDT': 6.5, 'LINKUSDT': 15,
            'AVAXUSDT': 35, 'MATICUSDT': 0.8, 'UNIUSDT': 7, 'LTCUSDT': 70
        }
        
        if symbol in base_prices:
            # Add some random variation
            base_price = base_prices[symbol]
            variation = np.random.uniform(-0.02, 0.02)  # ±2% variation
            return base_price * (1 + variation)
        
        return None

    def update_portfolio_value(self):
        """Update portfolio value and metrics"""
        positions_value = 0
        unrealized_pnl = 0
        
        for symbol, position in self.portfolio.positions.items():
            current_price = self._get_current_price(symbol)
            if current_price:
                position_value = position['quantity'] * current_price
                positions_value += position_value
                
                # Calculate unrealized P&L
                cost_basis = position['quantity'] * position['avg_price']
                unrealized_pnl += (position_value - cost_basis)
        
        self.portfolio.total_value = self.portfolio.cash_balance + positions_value
        self.portfolio.unrealized_pnl = unrealized_pnl
        
        # Update performance metrics
        self._update_performance_metrics()

    def _update_performance_metrics(self):
        """Update performance metrics"""
        if len(self.trade_history) > 0:
            # Calculate win rate
            profitable_trades = sum(1 for trade in self.trade_history 
                                  if self._is_trade_profitable(trade))
            self.portfolio.win_rate = profitable_trades / len(self.trade_history)
            
            # Calculate Sharpe ratio (simplified)
            returns = self._calculate_returns()
            if len(returns) > 1:
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                self.portfolio.sharpe_ratio = mean_return / std_return if std_return > 0 else 0
        
        self.portfolio.total_trades = len(self.trade_history)

    def _is_trade_profitable(self, trade: EnhancedTrade) -> bool:
        """Check if a trade was profitable (simplified)"""
        # This is a simplified check - in reality would need to track actual outcomes
        return trade.prediction > 0 if trade.action == 'BUY' else trade.prediction < 0

    def _calculate_returns(self) -> List[float]:
        """Calculate portfolio returns over time"""
        if len(self.performance_history) < 2:
            return []
        
        returns = []
        for i in range(1, len(self.performance_history)):
            prev_value = self.performance_history[i-1]['total_value']
            curr_value = self.performance_history[i]['total_value']
            if prev_value > 0:
                returns.append((curr_value - prev_value) / prev_value)
        
        return returns

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total_return = (self.portfolio.total_value - self.initial_balance) / self.initial_balance
        
        return {
            'agent_id': self.agent_id,
            'strategy': self.strategy,
            'initial_balance': self.initial_balance,
            'current_value': self.portfolio.total_value,
            'total_return': total_return,
            'realized_pnl': self.portfolio.realized_pnl,
            'unrealized_pnl': self.portfolio.unrealized_pnl,
            'cash_balance': self.portfolio.cash_balance,
            'active_positions': len(self.portfolio.positions),
            'total_trades': self.portfolio.total_trades,
            'win_rate': self.portfolio.win_rate,
            'sharpe_ratio': self.portfolio.sharpe_ratio
        }

class AutonomousEnhancedTradingSystem:
    """System managing multiple enhanced autonomous trading agents"""
    
    def __init__(self):
        self.agents = {}
        self.system_db = "autonomous_enhanced_trading.db"
        self.data_pipeline = EnhancedDataPipeline()
        self._initialize_database()

    def _initialize_database(self):
        """Initialize database for enhanced autonomous trading"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS enhanced_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                agent_id TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                confidence REAL,
                prediction REAL,
                signal_strength TEXT,
                reasoning TEXT,
                model_used TEXT,
                executed BOOLEAN
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_performance (
                timestamp TEXT,
                agent_id TEXT,
                portfolio_value REAL,
                total_return REAL,
                realized_pnl REAL,
                unrealized_pnl REAL,
                win_rate REAL,
                sharpe_ratio REAL,
                active_positions INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()

    def add_enhanced_agent(self, agent_id: str, initial_balance: float = 5000.0, strategy: str = "balanced"):
        """Add enhanced autonomous agent"""
        agent = EnhancedAutonomousAgent(agent_id, initial_balance, strategy)
        self.agents[agent_id] = agent
        logger.info(f"🤖 Added enhanced agent: {agent_id} ({strategy} strategy)")

    async def run_enhanced_trading_cycle(self):
        """Run one enhanced trading cycle"""
        logger.info("🔄 Running enhanced trading cycle...")
        
        all_trades = []
        
        for agent in self.agents.values():
            try:
                # Update portfolio values
                agent.update_portfolio_value()
                
                # Analyze market and generate predictions
                predictions = await agent.analyze_market_and_predict()
                
                if predictions:
                    # Make trading decisions
                    decisions = agent.make_trading_decisions(predictions)
                    
                    # Execute trades
                    for trade in decisions:
                        executed = agent.execute_trade(trade)
                        
                        if executed:
                            agent.trade_history.append(trade)
                            all_trades.append(trade)
                            
                            # Save to database
                            self._save_trade(trade, executed)
                
                # Save performance metrics
                performance = agent.get_performance_summary()
                agent.performance_history.append(performance)
                self._save_performance(performance)
                
            except Exception as e:
                logger.error(f"❌ Error in trading cycle for {agent.agent_id}: {e}")
        
        logger.info(f"✅ Enhanced trading cycle complete: {len(all_trades)} trades executed")
        return all_trades

    def _save_trade(self, trade: EnhancedTrade, executed: bool):
        """Save trade to database"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO enhanced_trades 
            (timestamp, agent_id, symbol, action, quantity, price, confidence, 
             prediction, signal_strength, reasoning, model_used, executed)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade.timestamp, trade.agent_id, trade.symbol, trade.action,
            trade.quantity, trade.price, trade.confidence, trade.prediction,
            trade.signal_strength, trade.reasoning, trade.model_used, executed
        ))
        
        conn.commit()
        conn.close()

    def _save_performance(self, performance: Dict[str, Any]):
        """Save performance metrics to database"""
        conn = sqlite3.connect(self.system_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO agent_performance 
            (timestamp, agent_id, portfolio_value, total_return, realized_pnl, 
             unrealized_pnl, win_rate, sharpe_ratio, active_positions)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(), performance['agent_id'],
            performance['current_value'], performance['total_return'],
            performance['realized_pnl'], performance['unrealized_pnl'],
            performance['win_rate'], performance['sharpe_ratio'],
            performance['active_positions']
        ))
        
        conn.commit()
        conn.close()

    def display_system_status(self):
        """Display enhanced system status"""
        print("\n🚀 AUTONOMOUS ENHANCED TRADING SYSTEM STATUS")
        print("=" * 70)
        
        total_value = 0
        total_initial = 0
        
        for agent_id, agent in self.agents.items():
            performance = agent.get_performance_summary()
            total_value += performance['current_value']
            total_initial += performance['initial_balance']
            
            print(f"\n🤖 {agent_id.upper()} ({performance['strategy']})")
            print(f"   Portfolio Value: ${performance['current_value']:,.2f}")
            print(f"   Total Return: {performance['total_return']:+.2%}")
            print(f"   Realized P&L: ${performance['realized_pnl']:+,.2f}")
            print(f"   Unrealized P&L: ${performance['unrealized_pnl']:+,.2f}")
            print(f"   Win Rate: {performance['win_rate']:.1%}")
            print(f"   Sharpe Ratio: {performance['sharpe_ratio']:.3f}")
            print(f"   Active Positions: {performance['active_positions']}")
            print(f"   Total Trades: {performance['total_trades']}")
        
        system_return = (total_value - total_initial) / total_initial if total_initial > 0 else 0
        
        print(f"\n📊 SYSTEM TOTALS:")
        print(f"   Total Portfolio Value: ${total_value:,.2f}")
        print(f"   System Return: {system_return:+.2%}")
        print(f"   Active Agents: {len(self.agents)}")

    async def run_autonomous_enhanced_trading(self, duration_minutes: int = 30):
        """Run autonomous enhanced trading system"""
        logger.info(f"🚀 Starting autonomous enhanced trading for {duration_minutes} minutes")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        cycle_count = 0
        
        while datetime.now() < end_time:
            cycle_count += 1
            logger.info(f"🔄 Enhanced trading cycle {cycle_count}")
            
            # Run enhanced trading cycle
            trades = await self.run_enhanced_trading_cycle()
            
            # Display status every 3 cycles
            if cycle_count % 3 == 0:
                self.display_system_status()
            
            # Wait before next cycle
            await asyncio.sleep(90)  # 1.5 minutes between cycles
        
        # Final status
        print(f"\n🏁 AUTONOMOUS ENHANCED TRADING COMPLETED")
        print(f"Total cycles: {cycle_count}")
        self.display_system_status()

async def main():
    """Main autonomous enhanced trading demonstration"""
    print("🚀 AUTONOMOUS ENHANCED TRADING SYSTEM")
    print("=" * 60)
    
    # Create enhanced trading system
    system = AutonomousEnhancedTradingSystem()
    
    # Add enhanced agents with different strategies
    system.add_enhanced_agent("enhanced_conservative", 10000.0, "conservative")
    system.add_enhanced_agent("enhanced_balanced", 10000.0, "balanced")
    system.add_enhanced_agent("enhanced_aggressive", 10000.0, "aggressive")
    
    print(f"✅ Created {len(system.agents)} enhanced autonomous agents")
    
    # Run autonomous enhanced trading
    await system.run_autonomous_enhanced_trading(duration_minutes=5)
    
    print(f"\n🎯 Autonomous enhanced trading demonstration complete!")
    print(f"Database: {system.system_db}")

if __name__ == "__main__":
    asyncio.run(main())
