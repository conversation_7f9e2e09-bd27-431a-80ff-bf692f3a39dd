#!/usr/bin/env python3
"""
🔍 DETECT NEW OLLAMA MODELS
Simple script to detect and list your new Ollama models
"""

import asyncio
import aiohttp
import subprocess
import json
import logging
from typing import List, Dict, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DetectNewOllamaModels")

class OllamaModelDetector:
    """Detect and analyze Ollama models"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.session = None
        self.detected_models = []

    async def detect_models_via_api(self) -> List[Dict[str, Any]]:
        """Detect models via Ollama API"""
        try:
            url = f"{self.ollama_url}/api/tags"
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    models = []
                    for model in data.get('models', []):
                        models.append({
                            'name': model['name'],
                            'size': model.get('size', 0),
                            'modified_at': model.get('modified_at', ''),
                            'source': 'api'
                        })
                    logger.info(f"✅ API detected {len(models)} models")
                    return models
                else:
                    logger.error(f"❌ API error: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"❌ API detection failed: {e}")
            return []

    def detect_models_via_cli(self) -> List[Dict[str, Any]]:
        """Detect models via CLI"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                models = []
                lines = result.stdout.strip().split('\n')
                
                # Skip header line
                if len(lines) > 1:
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 1:
                                models.append({
                                    'name': parts[0],
                                    'size': parts[1] if len(parts) > 1 else 'unknown',
                                    'modified_at': ' '.join(parts[2:]) if len(parts) > 2 else '',
                                    'source': 'cli'
                                })
                
                logger.info(f"✅ CLI detected {len(models)} models")
                return models
            else:
                logger.error(f"❌ CLI error: {result.stderr}")
                return []
        except Exception as e:
            logger.error(f"❌ CLI detection failed: {e}")
            return []

    def analyze_model_info(self, model_name: str) -> Dict[str, str]:
        """Analyze model information"""
        model_lower = model_name.lower()
        
        # Determine size category
        if any(size in model_lower for size in ['70b', '90b', '72b']):
            size_category = 'large'
        elif any(size in model_lower for size in ['32b', '35b', '24b', '27b', '30b']):
            size_category = 'medium'
        elif any(size in model_lower for size in ['7b', '8b', '10b', '14b', '11b', '13b']):
            size_category = 'small'
        else:
            size_category = 'unknown'
        
        # Determine model family/type
        if 'llama' in model_lower:
            family = 'llama'
            specialty = 'general_intelligence'
        elif 'qwen' in model_lower:
            family = 'qwen'
            specialty = 'multilingual_reasoning'
        elif 'gemma' in model_lower:
            family = 'gemma'
            specialty = 'google_reasoning'
        elif 'mistral' in model_lower:
            family = 'mistral'
            specialty = 'european_efficiency'
        elif 'phi' in model_lower:
            family = 'phi'
            specialty = 'efficient_reasoning'
        elif 'deepseek' in model_lower:
            family = 'deepseek'
            specialty = 'deep_reasoning'
        elif 'marco' in model_lower or 'o1' in model_lower:
            family = 'marco'
            specialty = 'analytical_reasoning'
        elif 'cogito' in model_lower:
            family = 'cogito'
            specialty = 'philosophical_thinking'
        elif 'command' in model_lower:
            family = 'command'
            specialty = 'command_control'
        elif 'falcon' in model_lower:
            family = 'falcon'
            specialty = 'speed_optimization'
        elif 'granite' in model_lower:
            family = 'granite'
            specialty = 'enterprise_stability'
        elif 'nemotron' in model_lower:
            family = 'nemotron'
            specialty = 'advanced_reasoning'
        else:
            family = 'unknown'
            specialty = 'general_purpose'
        
        return {
            'size_category': size_category,
            'family': family,
            'specialty': specialty
        }

    def display_detected_models(self, models: List[Dict[str, Any]]):
        """Display detected models with analysis"""
        print(f"\n🤖 DETECTED OLLAMA MODELS")
        print("=" * 80)
        
        if not models:
            print("❌ No models detected!")
            return
        
        print(f"Total models found: {len(models)}")
        print()
        
        # Group by size category
        size_groups = {'large': [], 'medium': [], 'small': [], 'unknown': []}
        
        for model in models:
            analysis = self.analyze_model_info(model['name'])
            model['analysis'] = analysis
            size_groups[analysis['size_category']].append(model)
        
        # Display by size category
        for size_cat, size_models in size_groups.items():
            if size_models:
                print(f"📊 {size_cat.upper()} MODELS ({len(size_models)}):")
                for model in size_models:
                    analysis = model['analysis']
                    print(f"   🤖 {model['name']:<25} | {analysis['family']:<12} | {analysis['specialty']}")
                print()
        
        # Identify new/interesting models
        interesting_models = []
        for model in models:
            name = model['name'].lower()
            if any(keyword in name for keyword in ['llama3.3', 'qwen2.5', 'phi4', 'nemotron', 'deepseek-r1']):
                interesting_models.append(model)
        
        if interesting_models:
            print(f"🆕 NEW/INTERESTING MODELS DETECTED ({len(interesting_models)}):")
            for model in interesting_models:
                analysis = model['analysis']
                print(f"   ⭐ {model['name']:<25} | {analysis['specialty']}")
            print()

    async def test_model_availability(self, model_name: str) -> bool:
        """Test if a model is available and responsive"""
        try:
            url = f"{self.ollama_url}/api/generate"
            payload = {
                "model": model_name,
                "prompt": "Hello, respond with just 'OK'",
                "stream": False,
                "options": {"max_tokens": 5}
            }
            
            async with self.session.post(url, json=payload, timeout=30) as response:
                if response.status == 200:
                    result = await response.json()
                    return 'response' in result
                else:
                    return False
        except:
            return False

    async def run_detection(self):
        """Run complete model detection"""
        print("🔍 DETECTING YOUR OLLAMA MODELS...")
        print("=" * 50)
        
        self.session = aiohttp.ClientSession()
        
        try:
            # Try API detection first
            models = await self.detect_models_via_api()
            
            # Fallback to CLI if API fails
            if not models:
                print("🔄 API detection failed, trying CLI...")
                models = self.detect_models_via_cli()
            
            # Display results
            self.display_detected_models(models)
            
            # Test a few models for availability
            if models:
                print("🧪 TESTING MODEL AVAILABILITY:")
                test_models = models[:3]  # Test first 3 models
                
                for model in test_models:
                    print(f"   Testing {model['name']}...", end=" ")
                    available = await self.test_model_availability(model['name'])
                    status = "✅ Available" if available else "❌ Not responding"
                    print(status)
                print()
            
            self.detected_models = models
            return models
            
        finally:
            await self.session.close()

    def generate_integration_code(self, models: List[Dict[str, Any]]) -> str:
        """Generate integration code for detected models"""
        if not models:
            return "# No models detected"
        
        code = "# 🤖 DETECTED OLLAMA MODELS INTEGRATION\n\n"
        code += "ollama_agents = [\n"
        
        for model in models:
            analysis = model.get('analysis', {})
            size_cat = analysis.get('size_category', 'unknown')
            specialty = analysis.get('specialty', 'general_purpose')
            
            # Determine balance based on size
            if size_cat == 'large':
                balance = "30000.0"
            elif size_cat == 'medium':
                balance = "20000.0"
            else:
                balance = "15000.0"
            
            agent_id = model['name'].replace(':', '_').replace('.', '_').replace('-', '_') + '_agent'
            
            code += f"    # {model['name']} - {specialty}\n"
            code += f"    OllamaAgent('{model['name']}', '{agent_id}', {balance}, '{specialty}'),\n"
        
        code += "]\n"
        return code

async def main():
    """Main detection function"""
    detector = OllamaModelDetector()
    
    # Run detection
    models = await detector.run_detection()
    
    if models:
        print("🚀 INTEGRATION CODE GENERATED:")
        print("=" * 50)
        integration_code = detector.generate_integration_code(models)
        print(integration_code)
        
        # Save to file
        with open('detected_models_integration.py', 'w') as f:
            f.write(integration_code)
        
        print("✅ Integration code saved to 'detected_models_integration.py'")
    else:
        print("❌ No models detected. Make sure Ollama is running.")

if __name__ == "__main__":
    asyncio.run(main())
