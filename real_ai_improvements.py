#!/usr/bin/env python3
"""
🧠 REAL AI IMPROVEMENTS FOR TRADING
Actual model architectures and techniques that work
"""

import numpy as np
import pandas as pd
import sqlite3
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib
import logging
from datetime import datetime, timedelta
import asyncio

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AIImprovements")

class RealTradingAI:
    """Real AI improvements for trading systems"""
    
    def __init__(self, db_path="real_market_data.db"):
        self.db_path = db_path
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        
    def load_real_data(self, days_back=30):
        """Load real market data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get recent price data
            query = '''
                SELECT timestamp, symbol, price, volume, high_24h, low_24h, change_24h
                FROM price_data 
                WHERE timestamp > datetime('now', '-{} days')
                ORDER BY timestamp DESC
            '''.format(days_back)
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if len(df) == 0:
                logger.warning("No data found in database")
                return None
            
            # Convert timestamp
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            logger.info(f"✅ Loaded {len(df)} real market records")
            return df
            
        except Exception as e:
            logger.error(f"❌ Data loading error: {e}")
            return None

    def create_features(self, df):
        """Create real trading features from market data"""
        features_df = df.copy()
        
        # Sort by symbol and timestamp
        features_df = features_df.sort_values(['symbol', 'timestamp'])
        
        # Technical indicators
        for symbol in features_df['symbol'].unique():
            mask = features_df['symbol'] == symbol
            symbol_data = features_df[mask].copy()
            
            if len(symbol_data) < 10:
                continue
                
            # Price-based features
            symbol_data['price_sma_5'] = symbol_data['price'].rolling(5).mean()
            symbol_data['price_sma_20'] = symbol_data['price'].rolling(20).mean()
            symbol_data['price_std_5'] = symbol_data['price'].rolling(5).std()
            
            # Volume features
            symbol_data['volume_sma_5'] = symbol_data['volume'].rolling(5).mean()
            symbol_data['volume_ratio'] = symbol_data['volume'] / symbol_data['volume_sma_5']
            
            # Volatility features
            symbol_data['volatility'] = (symbol_data['high_24h'] - symbol_data['low_24h']) / symbol_data['price']
            
            # Momentum features
            symbol_data['price_momentum_5'] = symbol_data['price'].pct_change(5)
            symbol_data['price_momentum_20'] = symbol_data['price'].pct_change(20)
            
            # RSI-like indicator
            price_changes = symbol_data['price'].diff()
            gains = price_changes.where(price_changes > 0, 0)
            losses = -price_changes.where(price_changes < 0, 0)
            avg_gains = gains.rolling(14).mean()
            avg_losses = losses.rolling(14).mean()
            rs = avg_gains / avg_losses
            symbol_data['rsi'] = 100 - (100 / (1 + rs))
            
            # Update main dataframe
            features_df.loc[mask] = symbol_data
        
        # Remove rows with NaN values
        features_df = features_df.dropna()
        
        # Feature columns for training
        self.feature_columns = [
            'price', 'volume', 'high_24h', 'low_24h', 'change_24h',
            'price_sma_5', 'price_sma_20', 'price_std_5',
            'volume_sma_5', 'volume_ratio', 'volatility',
            'price_momentum_5', 'price_momentum_20', 'rsi'
        ]
        
        logger.info(f"✅ Created {len(self.feature_columns)} features from {len(features_df)} records")
        return features_df

    def prepare_training_data(self, df, prediction_horizon=1):
        """Prepare data for training with future price targets"""
        # Sort by symbol and timestamp
        df = df.sort_values(['symbol', 'timestamp'])
        
        X_list = []
        y_list = []
        
        for symbol in df['symbol'].unique():
            symbol_data = df[df['symbol'] == symbol].copy()
            
            if len(symbol_data) < prediction_horizon + 5:
                continue
            
            # Features (X)
            X_symbol = symbol_data[self.feature_columns].values[:-prediction_horizon]
            
            # Targets (y) - future price change
            current_prices = symbol_data['price'].values[:-prediction_horizon]
            future_prices = symbol_data['price'].values[prediction_horizon:]
            y_symbol = (future_prices - current_prices) / current_prices  # Percentage change
            
            X_list.append(X_symbol)
            y_list.append(y_symbol)
        
        if not X_list:
            return None, None
        
        X = np.vstack(X_list)
        y = np.hstack(y_list)
        
        logger.info(f"✅ Prepared training data: {X.shape[0]} samples, {X.shape[1]} features")
        return X, y

    def train_ensemble_model(self, X, y):
        """Train ensemble of real ML models"""
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train multiple models
        models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
        }
        
        results = {}
        
        for name, model in models.items():
            logger.info(f"🔧 Training {name}...")
            
            # Train model
            if name == 'random_forest':
                model.fit(X_train, y_train)  # RF doesn't need scaling
                y_pred = model.predict(X_test)
            else:
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
            
            # Evaluate
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            
            # Cross-validation
            if name == 'random_forest':
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='neg_mean_squared_error')
            else:
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='neg_mean_squared_error')
            
            results[name] = {
                'model': model,
                'mse': mse,
                'mae': mae,
                'cv_score': -cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            logger.info(f"✅ {name}: MSE={mse:.6f}, MAE={mae:.6f}, CV={-cv_scores.mean():.6f}±{cv_scores.std():.6f}")
        
        # Save best model
        best_model_name = min(results.keys(), key=lambda k: results[k]['mse'])
        best_model = results[best_model_name]['model']
        
        self.models['price_predictor'] = best_model
        self.scalers['price_predictor'] = scaler
        
        # Save to disk
        joblib.dump(best_model, f'best_model_{best_model_name}.pkl')
        joblib.dump(scaler, 'feature_scaler.pkl')
        
        logger.info(f"🏆 Best model: {best_model_name}")
        return results

    def create_trading_signals(self, df):
        """Create real trading signals from predictions"""
        if 'price_predictor' not in self.models:
            logger.error("No trained model available")
            return None
        
        model = self.models['price_predictor']
        scaler = self.scalers['price_predictor']
        
        signals = []
        
        for symbol in df['symbol'].unique():
            symbol_data = df[df['symbol'] == symbol].copy()
            
            if len(symbol_data) < 5:
                continue
            
            # Get latest features
            latest_features = symbol_data[self.feature_columns].iloc[-1:].values
            
            # Scale if needed (for gradient boosting)
            if hasattr(model, 'learning_rate'):  # GradientBoostingRegressor
                latest_features = scaler.transform(latest_features)
            
            # Predict price change
            predicted_change = model.predict(latest_features)[0]
            
            # Generate signal
            confidence = min(abs(predicted_change) * 10, 1.0)  # Scale to 0-1
            
            if predicted_change > 0.01:  # > 1% predicted increase
                signal = 'BUY'
            elif predicted_change < -0.01:  # > 1% predicted decrease
                signal = 'SELL'
            else:
                signal = 'HOLD'
            
            signals.append({
                'symbol': symbol,
                'signal': signal,
                'predicted_change': predicted_change,
                'confidence': confidence,
                'current_price': symbol_data['price'].iloc[-1],
                'timestamp': datetime.now().isoformat()
            })
        
        logger.info(f"✅ Generated {len(signals)} trading signals")
        return signals

    def backtest_strategy(self, df, initial_balance=10000):
        """Backtest the trading strategy on historical data"""
        # Sort by timestamp
        df = df.sort_values('timestamp')
        
        # Group by symbol and create signals for each time period
        balance = initial_balance
        positions = {}
        trades = []
        
        # Simple backtest - buy/sell based on predictions
        for i in range(len(df) - 20, len(df)):  # Last 20 records
            current_data = df.iloc[:i+1]
            signals = self.create_trading_signals(current_data)
            
            if not signals:
                continue
            
            for signal_data in signals:
                symbol = signal_data['symbol']
                signal = signal_data['signal']
                price = signal_data['current_price']
                confidence = signal_data['confidence']
                
                if signal == 'BUY' and confidence > 0.6:
                    # Buy with 10% of balance
                    invest_amount = balance * 0.1
                    quantity = invest_amount / price
                    
                    if symbol not in positions:
                        positions[symbol] = {'quantity': 0, 'avg_price': 0}
                    
                    # Update position
                    total_quantity = positions[symbol]['quantity'] + quantity
                    total_cost = (positions[symbol]['quantity'] * positions[symbol]['avg_price']) + invest_amount
                    positions[symbol] = {
                        'quantity': total_quantity,
                        'avg_price': total_cost / total_quantity if total_quantity > 0 else 0
                    }
                    
                    balance -= invest_amount
                    trades.append({
                        'timestamp': signal_data['timestamp'],
                        'symbol': symbol,
                        'action': 'BUY',
                        'quantity': quantity,
                        'price': price,
                        'confidence': confidence
                    })
                
                elif signal == 'SELL' and symbol in positions and positions[symbol]['quantity'] > 0:
                    # Sell 50% of position
                    sell_quantity = positions[symbol]['quantity'] * 0.5
                    sell_value = sell_quantity * price
                    
                    positions[symbol]['quantity'] -= sell_quantity
                    balance += sell_value
                    
                    trades.append({
                        'timestamp': signal_data['timestamp'],
                        'symbol': symbol,
                        'action': 'SELL',
                        'quantity': sell_quantity,
                        'price': price,
                        'confidence': confidence
                    })
        
        # Calculate final portfolio value
        final_value = balance
        for symbol, position in positions.items():
            if position['quantity'] > 0:
                # Get latest price
                latest_price = df[df['symbol'] == symbol]['price'].iloc[-1]
                final_value += position['quantity'] * latest_price
        
        total_return = (final_value - initial_balance) / initial_balance
        
        backtest_results = {
            'initial_balance': initial_balance,
            'final_value': final_value,
            'total_return': total_return,
            'total_trades': len(trades),
            'trades': trades[-10:],  # Last 10 trades
            'final_positions': positions
        }
        
        logger.info(f"📊 Backtest Results:")
        logger.info(f"   Initial: ${initial_balance:,.2f}")
        logger.info(f"   Final: ${final_value:,.2f}")
        logger.info(f"   Return: {total_return:.2%}")
        logger.info(f"   Trades: {len(trades)}")
        
        return backtest_results

async def main():
    """Main AI improvement demonstration"""
    print("🧠 REAL AI IMPROVEMENTS FOR TRADING")
    print("=" * 50)
    
    ai = RealTradingAI()
    
    # Load real data
    print("📊 Loading real market data...")
    df = ai.load_real_data(days_back=7)
    
    if df is None or len(df) < 100:
        print("❌ Insufficient data. Run real_data_collector.py first!")
        return
    
    # Create features
    print("🔧 Creating trading features...")
    features_df = ai.create_features(df)
    
    if features_df is None or len(features_df) < 50:
        print("❌ Insufficient feature data")
        return
    
    # Prepare training data
    print("📚 Preparing training data...")
    X, y = ai.prepare_training_data(features_df)
    
    if X is None:
        print("❌ Could not prepare training data")
        return
    
    # Train models
    print("🤖 Training AI models...")
    results = ai.train_ensemble_model(X, y)
    
    # Generate current signals
    print("📡 Generating trading signals...")
    signals = ai.create_trading_signals(features_df)
    
    if signals:
        print("\n🎯 CURRENT TRADING SIGNALS:")
        for signal in signals[:5]:  # Top 5
            print(f"  {signal['symbol']}: {signal['signal']} "
                  f"(Confidence: {signal['confidence']:.2f}, "
                  f"Predicted: {signal['predicted_change']:+.2%})")
    
    # Backtest
    print("\n📊 Running backtest...")
    backtest = ai.backtest_strategy(features_df)
    
    print(f"\n✅ AI Improvement Complete!")
    print(f"Model Performance: {min(r['mse'] for r in results.values()):.6f} MSE")
    print(f"Backtest Return: {backtest['total_return']:+.2%}")

if __name__ == "__main__":
    asyncio.run(main())
