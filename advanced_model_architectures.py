#!/usr/bin/env python3
"""
🧠 ADVANCED MODEL ARCHITECTURES
Real improvements that actually make models better
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import (
    RandomForestRegressor, GradientBoostingRegressor, 
    ExtraTreesRegressor, VotingRegressor
)
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from sklearn.model_selection import (
    GridSearchCV, RandomizedSearchCV, cross_val_score,
    TimeSeriesSplit, validation_curve
)
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import logging
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AdvancedModels")

class AdvancedModelArchitectures:
    """Real advanced model architectures that improve performance"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.best_params = {}
        
    def create_ensemble_models(self) -> Dict[str, Any]:
        """Create ensemble of different model types"""
        
        # Base models with different strengths
        models = {
            # Tree-based models (good for non-linear patterns)
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1
            ),
            
            'extra_trees': ExtraTreesRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1
            ),
            
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=8,
                min_samples_split=5,
                min_samples_leaf=2,
                subsample=0.8,
                random_state=42
            ),
            
            # Linear models (good for linear relationships)
            'ridge': Ridge(alpha=1.0),
            'lasso': Lasso(alpha=0.1),
            'elastic_net': ElasticNet(alpha=0.1, l1_ratio=0.5),
            
            # Non-linear models
            'svr': SVR(kernel='rbf', C=1.0, gamma='scale'),
            'mlp': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            )
        }
        
        # Voting ensemble (combines predictions)
        voting_ensemble = VotingRegressor([
            ('rf', models['random_forest']),
            ('gb', models['gradient_boosting']),
            ('ridge', models['ridge'])
        ])
        
        models['voting_ensemble'] = voting_ensemble
        
        logger.info(f"✅ Created {len(models)} advanced models")
        return models

    def advanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """Advanced feature engineering techniques"""
        
        features_df = df.copy()
        
        # Sort by symbol and timestamp for time series features
        features_df = features_df.sort_values(['symbol', 'timestamp'])
        
        for symbol in features_df['symbol'].unique():
            mask = features_df['symbol'] == symbol
            symbol_data = features_df[mask].copy()
            
            if len(symbol_data) < 50:
                continue
            
            # 1. Multiple timeframe moving averages
            for window in [5, 10, 20, 50]:
                symbol_data[f'sma_{window}'] = symbol_data['price'].rolling(window).mean()
                symbol_data[f'ema_{window}'] = symbol_data['price'].ewm(span=window).mean()
                symbol_data[f'std_{window}'] = symbol_data['price'].rolling(window).std()
            
            # 2. Bollinger Bands
            symbol_data['bb_upper'] = symbol_data['sma_20'] + (symbol_data['std_20'] * 2)
            symbol_data['bb_lower'] = symbol_data['sma_20'] - (symbol_data['std_20'] * 2)
            symbol_data['bb_position'] = (symbol_data['price'] - symbol_data['bb_lower']) / (symbol_data['bb_upper'] - symbol_data['bb_lower'])
            
            # 3. RSI (Relative Strength Index)
            delta = symbol_data['price'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(14).mean()
            avg_loss = loss.rolling(14).mean()
            rs = avg_gain / avg_loss
            symbol_data['rsi'] = 100 - (100 / (1 + rs))
            
            # 4. MACD (Moving Average Convergence Divergence)
            ema_12 = symbol_data['price'].ewm(span=12).mean()
            ema_26 = symbol_data['price'].ewm(span=26).mean()
            symbol_data['macd'] = ema_12 - ema_26
            symbol_data['macd_signal'] = symbol_data['macd'].ewm(span=9).mean()
            symbol_data['macd_histogram'] = symbol_data['macd'] - symbol_data['macd_signal']
            
            # 5. Momentum indicators
            for period in [5, 10, 20]:
                symbol_data[f'momentum_{period}'] = symbol_data['price'].pct_change(period)
                symbol_data[f'roc_{period}'] = (symbol_data['price'] / symbol_data['price'].shift(period) - 1) * 100
            
            # 6. Volume indicators
            symbol_data['volume_sma_20'] = symbol_data['volume'].rolling(20).mean()
            symbol_data['volume_ratio'] = symbol_data['volume'] / symbol_data['volume_sma_20']
            symbol_data['price_volume'] = symbol_data['price'] * symbol_data['volume']
            
            # 7. Volatility indicators
            symbol_data['volatility_10'] = symbol_data['price'].rolling(10).std() / symbol_data['price'].rolling(10).mean()
            symbol_data['atr'] = symbol_data['std_14']  # Simplified ATR
            
            # 8. Lag features (previous values)
            for lag in [1, 2, 3, 5]:
                symbol_data[f'price_lag_{lag}'] = symbol_data['price'].shift(lag)
                symbol_data[f'volume_lag_{lag}'] = symbol_data['volume'].shift(lag)
                symbol_data[f'rsi_lag_{lag}'] = symbol_data['rsi'].shift(lag)
            
            # 9. Statistical features
            symbol_data['price_zscore'] = (symbol_data['price'] - symbol_data['sma_20']) / symbol_data['std_20']
            symbol_data['volume_zscore'] = (symbol_data['volume'] - symbol_data['volume_sma_20']) / symbol_data['volume'].rolling(20).std()
            
            # 10. Interaction features
            symbol_data['rsi_macd'] = symbol_data['rsi'] * symbol_data['macd']
            symbol_data['price_volume_interaction'] = symbol_data['momentum_5'] * symbol_data['volume_ratio']
            
            # Update main dataframe
            features_df.loc[mask] = symbol_data
        
        # Remove rows with too many NaN values
        features_df = features_df.dropna(thresh=len(features_df.columns) * 0.7)
        
        logger.info(f"✅ Advanced feature engineering: {features_df.shape[1]} features created")
        return features_df

    def advanced_preprocessing(self, X_train: np.ndarray, X_test: np.ndarray, method: str = 'robust') -> Tuple[np.ndarray, np.ndarray, Any]:
        """Advanced preprocessing techniques"""
        
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'robust':
            scaler = RobustScaler()  # Less sensitive to outliers
        elif method == 'quantile':
            scaler = QuantileTransformer(output_distribution='normal')
        else:
            scaler = StandardScaler()
        
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        logger.info(f"✅ Applied {method} scaling")
        return X_train_scaled, X_test_scaled, scaler

    def feature_selection(self, X_train: np.ndarray, y_train: np.ndarray, feature_names: List[str], method: str = 'rfe') -> Tuple[np.ndarray, Any, List[str]]:
        """Advanced feature selection"""
        
        if method == 'univariate':
            # Select K best features based on univariate statistical tests
            selector = SelectKBest(score_func=f_regression, k=min(20, X_train.shape[1]))
            X_selected = selector.fit_transform(X_train, y_train)
            selected_features = [feature_names[i] for i in selector.get_support(indices=True)]
            
        elif method == 'rfe':
            # Recursive Feature Elimination
            estimator = RandomForestRegressor(n_estimators=50, random_state=42)
            selector = RFE(estimator, n_features_to_select=min(20, X_train.shape[1]))
            X_selected = selector.fit_transform(X_train, y_train)
            selected_features = [feature_names[i] for i in selector.get_support(indices=True)]
            
        else:
            # No selection
            selector = None
            X_selected = X_train
            selected_features = feature_names
        
        logger.info(f"✅ Feature selection: {len(selected_features)} features selected")
        return X_selected, selector, selected_features

    def hyperparameter_optimization(self, model, X_train: np.ndarray, y_train: np.ndarray, model_name: str) -> Tuple[Any, Dict]:
        """Advanced hyperparameter optimization"""
        
        # Define parameter grids for different models
        param_grids = {
            'random_forest': {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 15, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2', None]
            },
            'gradient_boosting': {
                'n_estimators': [100, 200, 300],
                'learning_rate': [0.05, 0.1, 0.15],
                'max_depth': [6, 8, 10],
                'min_samples_split': [2, 5, 10],
                'subsample': [0.8, 0.9, 1.0]
            },
            'ridge': {
                'alpha': [0.1, 1.0, 10.0, 100.0]
            },
            'svr': {
                'C': [0.1, 1.0, 10.0],
                'gamma': ['scale', 'auto', 0.001, 0.01],
                'epsilon': [0.01, 0.1, 0.2]
            }
        }
        
        if model_name in param_grids:
            # Use TimeSeriesSplit for time series data
            cv = TimeSeriesSplit(n_splits=5)
            
            # Randomized search for efficiency
            search = RandomizedSearchCV(
                model,
                param_grids[model_name],
                n_iter=20,
                cv=cv,
                scoring='neg_mean_squared_error',
                n_jobs=-1,
                random_state=42
            )
            
            search.fit(X_train, y_train)
            
            logger.info(f"✅ Hyperparameter optimization for {model_name}")
            logger.info(f"   Best score: {-search.best_score_:.6f}")
            
            return search.best_estimator_, search.best_params_
        else:
            # No optimization for this model
            model.fit(X_train, y_train)
            return model, {}

    def advanced_validation(self, model, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """Advanced model validation techniques"""
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        # Multiple metrics
        scoring_metrics = {
            'mse': 'neg_mean_squared_error',
            'mae': 'neg_mean_absolute_error',
            'r2': 'r2'
        }
        
        results = {}
        for metric_name, metric in scoring_metrics.items():
            scores = cross_val_score(model, X, y, cv=tscv, scoring=metric, n_jobs=-1)
            results[f'{metric_name}_mean'] = scores.mean()
            results[f'{metric_name}_std'] = scores.std()
        
        # Convert negative scores back to positive
        results['mse_mean'] = -results['mse_mean']
        results['mae_mean'] = -results['mae_mean']
        
        logger.info(f"✅ Advanced validation completed")
        logger.info(f"   MSE: {results['mse_mean']:.6f} ± {results['mse_std']:.6f}")
        logger.info(f"   MAE: {results['mae_mean']:.6f} ± {results['mae_std']:.6f}")
        logger.info(f"   R²: {results['r2_mean']:.6f} ± {results['r2_std']:.6f}")
        
        return results

    def train_advanced_ensemble(self, X_train: np.ndarray, X_test: np.ndarray, 
                               y_train: np.ndarray, y_test: np.ndarray, 
                               feature_names: List[str]) -> Dict[str, Any]:
        """Train advanced ensemble with all improvements"""
        
        logger.info("🚀 Training advanced ensemble with all improvements")
        
        # 1. Advanced preprocessing
        X_train_scaled, X_test_scaled, scaler = self.advanced_preprocessing(X_train, X_test, 'robust')
        
        # 2. Feature selection
        X_train_selected, selector, selected_features = self.feature_selection(
            X_train_scaled, y_train, feature_names, 'rfe'
        )
        X_test_selected = selector.transform(X_test_scaled) if selector else X_test_scaled
        
        # 3. Create advanced models
        models = self.create_ensemble_models()
        
        # 4. Train and optimize each model
        trained_models = {}
        model_results = {}
        
        for name, model in models.items():
            logger.info(f"🔧 Training {name}...")
            
            # Hyperparameter optimization
            optimized_model, best_params = self.hyperparameter_optimization(
                model, X_train_selected, y_train, name
            )
            
            # Validation
            validation_results = self.advanced_validation(optimized_model, X_train_selected, y_train)
            
            # Test performance
            y_pred = optimized_model.predict(X_test_selected)
            test_mse = mean_squared_error(y_test, y_pred)
            test_mae = mean_absolute_error(y_test, y_pred)
            test_r2 = r2_score(y_test, y_pred)
            
            trained_models[name] = optimized_model
            model_results[name] = {
                'validation': validation_results,
                'test_mse': test_mse,
                'test_mae': test_mae,
                'test_r2': test_r2,
                'best_params': best_params
            }
            
            logger.info(f"   Test MSE: {test_mse:.6f}")
            logger.info(f"   Test R²: {test_r2:.6f}")
        
        # 5. Select best model
        best_model_name = min(model_results.keys(), key=lambda k: model_results[k]['test_mse'])
        best_model = trained_models[best_model_name]
        
        # 6. Save everything
        self.models = trained_models
        self.scalers['main'] = scaler
        self.feature_selectors['main'] = selector
        
        # Save to disk
        joblib.dump(best_model, f'best_advanced_model_{best_model_name}.pkl')
        joblib.dump(scaler, 'advanced_scaler.pkl')
        if selector:
            joblib.dump(selector, 'advanced_feature_selector.pkl')
        
        results = {
            'trained_models': trained_models,
            'model_results': model_results,
            'best_model_name': best_model_name,
            'best_model': best_model,
            'scaler': scaler,
            'feature_selector': selector,
            'selected_features': selected_features,
            'preprocessing_method': 'robust',
            'feature_selection_method': 'rfe'
        }
        
        logger.info(f"🏆 Best model: {best_model_name}")
        logger.info(f"   Final test MSE: {model_results[best_model_name]['test_mse']:.6f}")
        logger.info(f"   Final test R²: {model_results[best_model_name]['test_r2']:.6f}")
        
        return results

def main():
    """Demonstration of advanced model architectures"""
    print("🧠 ADVANCED MODEL ARCHITECTURES DEMO")
    print("=" * 50)
    
    # Create sample data for demonstration
    np.random.seed(42)
    n_samples = 1000
    n_features = 30
    
    # Generate synthetic time series data
    X = np.random.randn(n_samples, n_features)
    # Add some non-linear relationships
    y = (X[:, 0] * X[:, 1] + 
         np.sin(X[:, 2]) + 
         X[:, 3]**2 + 
         np.random.randn(n_samples) * 0.1)
    
    feature_names = [f'feature_{i}' for i in range(n_features)]
    
    # Split data
    split_idx = int(0.8 * n_samples)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"📊 Training data: {X_train.shape}")
    print(f"📊 Test data: {X_test.shape}")
    
    # Train advanced ensemble
    advanced_models = AdvancedModelArchitectures()
    results = advanced_models.train_advanced_ensemble(
        X_train, X_test, y_train, y_test, feature_names
    )
    
    print(f"\n🏆 RESULTS SUMMARY:")
    print(f"Best model: {results['best_model_name']}")
    print(f"Selected features: {len(results['selected_features'])}")
    print(f"Models trained: {len(results['trained_models'])}")
    
    print(f"\n📊 MODEL COMPARISON:")
    for name, result in results['model_results'].items():
        print(f"  {name:20} | MSE: {result['test_mse']:.6f} | R²: {result['test_r2']:.6f}")
    
    print(f"\n✅ Advanced model training completed!")

if __name__ == "__main__":
    main()
