#!/usr/bin/env python3
"""
🚀 WORKING REALISTIC SYSTEM
Combines proven components into working realistic implementation
"""

import asyncio
import aiohttp
import sqlite3
import pandas as pd
import numpy as np
import json
import joblib
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import RobustScaler
from sklearn.feature_selection import RFE
from sklearn.metrics import mean_squared_error, r2_score

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WorkingRealisticSystem")

class WorkingRealisticSystem:
    """Working realistic system using proven components"""
    
    def __init__(self):
        self.db_path = "working_realistic_system.db"
        self.session = None
        
        # Trading state
        self.portfolio = {
            'cash': 10000.0,
            'positions': {},
            'total_value': 10000.0,
            'trades': [],
            'performance': []
        }
        
        # Model components
        self.model = None
        self.scaler = None
        self.feature_selector = None
        
        # Crypto pairs
        self.crypto_pairs = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
        
        self._initialize_database()

    def _initialize_database(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Market data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                price REAL,
                volume REAL,
                change_24h REAL,
                source TEXT
            )
        ''')
        
        # Trading signals
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_signals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                signal TEXT,
                confidence REAL,
                prediction REAL
            )
        ''')
        
        # Trades
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                symbol TEXT,
                action TEXT,
                quantity REAL,
                price REAL,
                value REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"✅ Database initialized: {self.db_path}")

    async def collect_real_data(self):
        """Collect real market data"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': 'bitcoin,ethereum,cardano,solana,polkadot',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    
                    symbol_map = {
                        'bitcoin': 'BTCUSDT',
                        'ethereum': 'ETHUSDT', 
                        'cardano': 'ADAUSDT',
                        'solana': 'SOLUSDT',
                        'polkadot': 'DOTUSDT'
                    }
                    
                    records_added = 0
                    for coin_id, coin_data in data.items():
                        if coin_id in symbol_map:
                            symbol = symbol_map[coin_id]
                            
                            cursor.execute('''
                                INSERT INTO market_data 
                                (timestamp, symbol, price, volume, change_24h, source)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                datetime.now().isoformat(),
                                symbol,
                                coin_data['usd'],
                                coin_data.get('usd_24h_vol', 0),
                                coin_data.get('usd_24h_change', 0),
                                'coingecko'
                            ))
                            records_added += 1
                    
                    conn.commit()
                    conn.close()
                    
                    logger.info(f"✅ Collected {records_added} real market records")
                    return records_added
                else:
                    logger.error(f"❌ API error: {response.status}")
                    return 0
                    
        except Exception as e:
            logger.error(f"❌ Data collection error: {e}")
            return 0

    def create_synthetic_training_data(self):
        """Create synthetic training data for model training"""
        logger.info("🔧 Creating synthetic training data...")
        
        # Generate realistic synthetic market data
        np.random.seed(42)
        n_samples = 500
        n_features = 10
        
        # Create realistic price movements
        base_prices = {'BTCUSDT': 45000, 'ETHUSDT': 2500, 'ADAUSDT': 0.45, 'SOLUSDT': 95, 'DOTUSDT': 6.5}
        
        training_data = []
        
        for symbol in self.crypto_pairs:
            base_price = base_prices[symbol]
            
            for i in range(n_samples // len(self.crypto_pairs)):
                # Generate realistic features
                price_change = np.random.normal(0, 0.02)  # 2% volatility
                volume_change = np.random.normal(0, 0.1)   # 10% volume volatility
                
                features = {
                    'price_momentum': price_change,
                    'volume_ratio': 1 + volume_change,
                    'volatility': abs(np.random.normal(0.02, 0.01)),
                    'rsi': np.random.uniform(20, 80),
                    'macd': np.random.normal(0, 0.001),
                    'bb_position': np.random.uniform(0.1, 0.9),
                    'price_sma_ratio': 1 + np.random.normal(0, 0.01),
                    'volume_sma_ratio': 1 + np.random.normal(0, 0.05),
                    'change_24h': np.random.normal(0, 3),
                    'trend_strength': np.random.uniform(0, 1)
                }
                
                # Target: future return (correlated with features)
                target_return = (
                    features['price_momentum'] * 0.3 +
                    (features['rsi'] - 50) / 500 +
                    features['macd'] * 100 +
                    np.random.normal(0, 0.01)
                )
                
                training_data.append({
                    'features': list(features.values()),
                    'target': target_return,
                    'symbol': symbol
                })
        
        logger.info(f"✅ Created {len(training_data)} synthetic training samples")
        return training_data

    def train_working_model(self):
        """Train working model with synthetic data"""
        try:
            # Get synthetic training data
            training_data = self.create_synthetic_training_data()
            
            if len(training_data) < 50:
                logger.error("Insufficient training data")
                return False
            
            # Prepare data
            X = np.array([sample['features'] for sample in training_data])
            y = np.array([sample['target'] for sample in training_data])
            
            # Split data
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Preprocessing
            self.scaler = RobustScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Feature selection
            self.feature_selector = RFE(
                RandomForestRegressor(n_estimators=50, random_state=42), 
                n_features_to_select=min(8, X.shape[1])
            )
            X_train_selected = self.feature_selector.fit_transform(X_train_scaled, y_train)
            X_test_selected = self.feature_selector.transform(X_test_scaled)
            
            # Train model
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                random_state=42,
                n_jobs=-1
            )
            
            self.model.fit(X_train_selected, y_train)
            
            # Evaluate
            y_pred_train = self.model.predict(X_train_selected)
            y_pred_test = self.model.predict(X_test_selected)
            
            train_mse = mean_squared_error(y_train, y_pred_train)
            test_mse = mean_squared_error(y_test, y_pred_test)
            test_r2 = r2_score(y_test, y_pred_test)
            
            logger.info(f"✅ Working model trained successfully")
            logger.info(f"   Training MSE: {train_mse:.6f}")
            logger.info(f"   Test MSE: {test_mse:.6f}")
            logger.info(f"   Test R²: {test_r2:.6f}")
            logger.info(f"   Features selected: {X_train_selected.shape[1]}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Model training error: {e}")
            return False

    def generate_working_signals(self):
        """Generate working trading signals"""
        try:
            if not self.model:
                logger.warning("No trained model available")
                return []
            
            conn = sqlite3.connect(self.db_path)
            
            # Get latest market data
            df = pd.read_sql_query('''
                SELECT symbol, price, volume, change_24h, timestamp
                FROM market_data 
                WHERE timestamp = (SELECT MAX(timestamp) FROM market_data)
            ''', conn)
            
            signals = []
            
            for _, row in df.iterrows():
                symbol = row['symbol']
                
                # Create features from current market data
                features = [
                    row['change_24h'] / 100,  # price_momentum
                    1.0,                      # volume_ratio (default)
                    abs(row['change_24h']) / 100,  # volatility
                    50.0,                     # rsi (default)
                    0.0,                      # macd (default)
                    0.5,                      # bb_position (default)
                    1.0,                      # price_sma_ratio (default)
                    1.0,                      # volume_sma_ratio (default)
                    row['change_24h'],        # change_24h
                    0.5                       # trend_strength (default)
                ]
                
                # Preprocess
                features_array = np.array(features).reshape(1, -1)
                features_scaled = self.scaler.transform(features_array)
                features_selected = self.feature_selector.transform(features_scaled)
                
                # Predict
                prediction = self.model.predict(features_selected)[0]
                
                # Calculate confidence
                confidence = min(1.0, abs(prediction) * 20 + 0.4)
                
                # Generate signal
                if prediction > 0.005 and confidence > 0.6:  # > 0.5% with confidence
                    signal = "BUY"
                elif prediction < -0.005 and confidence > 0.6:  # < -0.5% with confidence
                    signal = "SELL"
                else:
                    signal = "HOLD"
                
                signal_data = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'signal': signal,
                    'confidence': confidence,
                    'prediction': prediction
                }
                
                signals.append(signal_data)
                
                # Save to database
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO trading_signals 
                    (timestamp, symbol, signal, confidence, prediction)
                    VALUES (?, ?, ?, ?, ?)
                ''', (signal_data['timestamp'], signal_data['symbol'], signal_data['signal'],
                      signal_data['confidence'], signal_data['prediction']))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Generated {len(signals)} working trading signals")
            return signals
            
        except Exception as e:
            logger.error(f"❌ Signal generation error: {e}")
            return []

    def execute_working_trades(self, signals: List[Dict]):
        """Execute working trades"""
        executed_trades = []
        
        for signal in signals:
            if signal['signal'] == 'HOLD':
                continue
            
            symbol = signal['symbol']
            action = signal['signal']
            confidence = signal['confidence']
            
            # Get current price
            current_price = self._get_current_price(symbol)
            if not current_price:
                continue
            
            # Calculate position size
            max_position_value = self.portfolio['cash'] * 0.15  # Max 15% per trade
            position_value = max_position_value * confidence
            
            if action == 'BUY' and position_value > 50:  # Min $50 trade
                quantity = position_value / current_price
                
                if position_value <= self.portfolio['cash']:
                    # Execute buy
                    self.portfolio['cash'] -= position_value
                    
                    if symbol not in self.portfolio['positions']:
                        self.portfolio['positions'][symbol] = {'quantity': 0, 'avg_price': 0}
                    
                    pos = self.portfolio['positions'][symbol]
                    total_quantity = pos['quantity'] + quantity
                    total_cost = (pos['quantity'] * pos['avg_price']) + position_value
                    
                    self.portfolio['positions'][symbol] = {
                        'quantity': total_quantity,
                        'avg_price': total_cost / total_quantity if total_quantity > 0 else 0
                    }
                    
                    trade = {
                        'timestamp': datetime.now().isoformat(),
                        'symbol': symbol,
                        'action': action,
                        'quantity': quantity,
                        'price': current_price,
                        'value': position_value
                    }
                    
                    executed_trades.append(trade)
                    self.portfolio['trades'].append(trade)
                    
                    logger.info(f"🟢 BUY {quantity:.6f} {symbol} @ ${current_price:.4f} (${position_value:.2f})")
            
            elif action == 'SELL' and symbol in self.portfolio['positions']:
                pos = self.portfolio['positions'][symbol]
                
                if pos['quantity'] > 0:
                    # Sell portion
                    sell_ratio = min(0.4, confidence)  # Max 40% of position
                    sell_quantity = pos['quantity'] * sell_ratio
                    sell_value = sell_quantity * current_price
                    
                    # Execute sell
                    self.portfolio['cash'] += sell_value
                    pos['quantity'] -= sell_quantity
                    
                    if pos['quantity'] < 0.000001:
                        del self.portfolio['positions'][symbol]
                    
                    trade = {
                        'timestamp': datetime.now().isoformat(),
                        'symbol': symbol,
                        'action': action,
                        'quantity': sell_quantity,
                        'price': current_price,
                        'value': sell_value
                    }
                    
                    executed_trades.append(trade)
                    self.portfolio['trades'].append(trade)
                    
                    logger.info(f"🔴 SELL {sell_quantity:.6f} {symbol} @ ${current_price:.4f} (${sell_value:.2f})")
        
        # Save trades
        if executed_trades:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for trade in executed_trades:
                cursor.execute('''
                    INSERT INTO trades 
                    (timestamp, symbol, action, quantity, price, value)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (trade['timestamp'], trade['symbol'], trade['action'],
                      trade['quantity'], trade['price'], trade['value']))
            
            conn.commit()
            conn.close()
        
        return executed_trades

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = '''
                SELECT price 
                FROM market_data 
                WHERE symbol = ? 
                ORDER BY timestamp DESC 
                LIMIT 1
            '''
            
            result = pd.read_sql_query(query, conn, params=(symbol,))
            conn.close()
            
            if len(result) > 0:
                return float(result.iloc[0]['price'])
            
            return None
            
        except Exception as e:
            logger.error(f"Price lookup error: {e}")
            return None

    def update_portfolio_value(self):
        """Update portfolio value"""
        positions_value = 0
        
        for symbol, position in self.portfolio['positions'].items():
            current_price = self._get_current_price(symbol)
            if current_price:
                positions_value += position['quantity'] * current_price
        
        self.portfolio['total_value'] = self.portfolio['cash'] + positions_value
        
        # Calculate performance
        total_return = (self.portfolio['total_value'] - 10000) / 10000
        
        performance = {
            'timestamp': datetime.now().isoformat(),
            'total_value': self.portfolio['total_value'],
            'cash': self.portfolio['cash'],
            'positions_value': positions_value,
            'total_return': total_return,
            'num_trades': len(self.portfolio['trades']),
            'num_positions': len(self.portfolio['positions'])
        }
        
        self.portfolio['performance'].append(performance)
        return performance

    def get_system_status(self):
        """Get system status"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            market_data_count = pd.read_sql_query("SELECT COUNT(*) as count FROM market_data", conn).iloc[0]['count']
            signals_count = pd.read_sql_query("SELECT COUNT(*) as count FROM trading_signals", conn).iloc[0]['count']
            trades_count = pd.read_sql_query("SELECT COUNT(*) as count FROM trades", conn).iloc[0]['count']
            
            conn.close()
            
            performance = self.update_portfolio_value()
            
            status = {
                'timestamp': datetime.now().isoformat(),
                'market_data_records': market_data_count,
                'trading_signals': signals_count,
                'executed_trades': trades_count,
                'model_trained': self.model is not None,
                'portfolio_value': performance['total_value'],
                'total_return': performance['total_return'],
                'cash_balance': performance['cash'],
                'active_positions': performance['num_positions'],
                'system_health': 'OPERATIONAL' if self.model else 'TRAINING_REQUIRED'
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ Status error: {e}")
            return {}

    async def run_working_system(self, duration_minutes: int = 10):
        """Run working realistic system"""
        logger.info(f"🚀 Starting working realistic system for {duration_minutes} minutes")
        
        self.session = aiohttp.ClientSession()
        
        try:
            # Phase 1: Train model with synthetic data
            logger.info("🤖 Phase 1: Training working model...")
            training_success = self.train_working_model()
            
            if not training_success:
                logger.error("❌ Model training failed")
                return {}
            
            # Phase 2: Collect real data
            logger.info("📊 Phase 2: Collecting real market data...")
            await self.collect_real_data()
            
            # Phase 3: Trading loop
            logger.info("💰 Phase 3: Starting working trading...")
            
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            cycle_count = 0
            
            while datetime.now() < end_time:
                cycle_count += 1
                logger.info(f"🔄 Trading cycle {cycle_count}")
                
                # Collect fresh data
                await self.collect_real_data()
                
                # Generate signals
                signals = self.generate_working_signals()
                
                # Execute trades
                if signals:
                    trades = self.execute_working_trades(signals)
                    
                    if trades:
                        logger.info(f"💰 Executed {len(trades)} trades")
                    
                    # Show signals
                    logger.info("📈 Current signals:")
                    for signal in signals:
                        logger.info(f"   {signal['symbol']}: {signal['signal']} "
                                  f"(Confidence: {signal['confidence']:.2f}, "
                                  f"Prediction: {signal['prediction']:+.3f})")
                
                # Update portfolio
                performance = self.update_portfolio_value()
                logger.info(f"💼 Portfolio: ${performance['total_value']:.2f} "
                          f"(Return: {performance['total_return']:+.2%})")
                
                await asyncio.sleep(45)  # 45 second intervals
            
            # Final status
            final_status = self.get_system_status()
            
            logger.info("✅ Working realistic system completed!")
            logger.info(f"📊 Final status: {final_status}")
            
            return final_status
            
        finally:
            await self.session.close()

async def main():
    """Main working realistic system demonstration"""
    print("🚀 WORKING REALISTIC SYSTEM")
    print("=" * 50)
    print("Proven models + Real data + Working trading")
    print("=" * 50)
    
    system = WorkingRealisticSystem()
    
    # Run working system
    results = await system.run_working_system(duration_minutes=5)
    
    print(f"\n🚀 WORKING REALISTIC SYSTEM RESULTS:")
    print(f"Market data: {results.get('market_data_records', 0)} records")
    print(f"Trading signals: {results.get('trading_signals', 0)} generated")
    print(f"Executed trades: {results.get('executed_trades', 0)} trades")
    print(f"Model trained: {results.get('model_trained', False)}")
    print(f"Portfolio value: ${results.get('portfolio_value', 0):.2f}")
    print(f"Total return: {results.get('total_return', 0):+.2%}")
    print(f"Active positions: {results.get('active_positions', 0)}")
    print(f"System health: {results.get('system_health', 'UNKNOWN')}")
    
    print(f"\n✅ Working realistic system demonstration completed!")
    print(f"Database: {system.db_path}")

if __name__ == "__main__":
    asyncio.run(main())
