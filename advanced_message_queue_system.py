"""
Advanced Message Queue System for Noryon V2
High-performance async message processing with Redis Streams, priority queues, and fault tolerance
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
from concurrent.futures import ThreadPoolExecutor
import aioredis
import hashlib

logger = logging.getLogger(__name__)

class MessagePriority(Enum):
    CRITICAL = 1  # Market alerts, risk triggers
    HIGH = 2      # Trading signals, portfolio updates  
    NORMAL = 3    # Analysis requests, data processing
    LOW = 4       # Background tasks, cleanup

class MessageStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    DEAD_LETTER = "dead_letter"

@dataclass
class QueueMessage:
    """Message structure for queue processing"""
    id: str
    topic: str
    payload: Dict[str, Any]
    priority: MessagePriority
    timestamp: datetime
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 30
    headers: Dict[str, str] = None
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {}

@dataclass
class ProcessingResult:
    """Result of message processing"""
    message_id: str
    success: bool
    result_data: Any = None
    error_message: str = None
    processing_time: float = 0.0
    retry_recommended: bool = False

class AdvancedMessageQueue:
    """High-performance message queue system with Redis Streams"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[aioredis.Redis] = None
        
        # Queue configuration
        self.queue_prefix = "noryon:queue"
        self.processing_prefix = "noryon:processing"
        self.dead_letter_prefix = "noryon:deadletter"
        self.metrics_prefix = "noryon:metrics"
        
        # Performance settings
        self.max_concurrent_workers = 20
        self.batch_size = 50
        self.poll_interval = 0.1  # 100ms polling
        self.processing_timeout = 300  # 5 minutes max processing
        
        # Worker management
        self.workers: Dict[str, asyncio.Task] = {}
        self.worker_semaphore = asyncio.Semaphore(self.max_concurrent_workers)
        self.message_handlers: Dict[str, Callable] = {}
        self.running = False
        
        # Performance metrics
        self.processed_count = 0
        self.failed_count = 0
        self.start_time = None
        
        # Priority queues (higher priority = lower number)
        self.priority_queues = {
            MessagePriority.CRITICAL: f"{self.queue_prefix}:critical",
            MessagePriority.HIGH: f"{self.queue_prefix}:high", 
            MessagePriority.NORMAL: f"{self.queue_prefix}:normal",
            MessagePriority.LOW: f"{self.queue_prefix}:low"
        }
        
        logger.info("[INIT] Advanced Message Queue System initialized")
    
    async def initialize(self):
        """Initialize Redis connection and queue structures"""
        try:
            self.redis_client = aioredis.from_url(self.redis_url, decode_responses=True)
            await self.redis_client.ping()
            
            # Create consumer groups for each priority queue
            for priority, queue_name in self.priority_queues.items():
                try:
                    await self.redis_client.xgroup_create(
                        queue_name, "processors", id="0", mkstream=True
                    )
                except aioredis.ResponseError as e:
                    if "BUSYGROUP" not in str(e):
                        raise
            
            self.start_time = datetime.now(timezone.utc)
            logger.info("✅ Message queue system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize message queue: {e}")
            raise
    
    async def enqueue(self, message: QueueMessage) -> str:
        """Enqueue a message with priority handling"""
        try:
            queue_name = self.priority_queues[message.priority]
            
            # Serialize message
            message_data = {
                "id": message.id,
                "topic": message.topic,
                "payload": json.dumps(message.payload),
                "priority": message.priority.value,
                "timestamp": message.timestamp.isoformat(),
                "retry_count": message.retry_count,
                "max_retries": message.max_retries,
                "timeout_seconds": message.timeout_seconds,
                "headers": json.dumps(message.headers)
            }
            
            # Add to Redis Stream
            stream_id = await self.redis_client.xadd(queue_name, message_data)
            
            # Update metrics
            await self._update_queue_metrics("enqueued", message.topic, message.priority)
            
            logger.debug(f"📨 Enqueued message {message.id} to {queue_name}")
            return stream_id
            
        except Exception as e:
            logger.error(f"❌ Failed to enqueue message {message.id}: {e}")
            raise
    
    async def enqueue_bulk(self, messages: List[QueueMessage]) -> List[str]:
        """Bulk enqueue for high throughput"""
        try:
            # Group messages by priority
            priority_groups = {}
            for msg in messages:
                priority = msg.priority
                if priority not in priority_groups:
                    priority_groups[priority] = []
                priority_groups[priority].append(msg)
            
            # Process each priority group
            stream_ids = []
            for priority, msg_group in priority_groups.items():
                queue_name = self.priority_queues[priority]
                
                # Use pipeline for bulk operations
                pipe = self.redis_client.pipeline()
                for msg in msg_group:
                    message_data = {
                        "id": msg.id,
                        "topic": msg.topic, 
                        "payload": json.dumps(msg.payload),
                        "priority": msg.priority.value,
                        "timestamp": msg.timestamp.isoformat(),
                        "retry_count": msg.retry_count,
                        "max_retries": msg.max_retries,
                        "timeout_seconds": msg.timeout_seconds,
                        "headers": json.dumps(msg.headers)
                    }
                    pipe.xadd(queue_name, message_data)
                
                batch_ids = await pipe.execute()
                stream_ids.extend(batch_ids)
            
            logger.info(f"📦 Bulk enqueued {len(messages)} messages")
            return stream_ids
            
        except Exception as e:
            logger.error(f"❌ Bulk enqueue failed: {e}")
            raise
    
    def register_handler(self, topic: str, handler: Callable):
        """Register a message handler for a specific topic"""
        self.message_handlers[topic] = handler
        logger.info(f"🔧 Registered handler for topic: {topic}")
    
    async def start_processing(self):
        """Start message processing workers"""
        if self.running:
            logger.warning("⚠️ Message processing already running")
            return
        
        self.running = True
        logger.info(f"🚀 Starting {self.max_concurrent_workers} message processing workers")
        
        # Start worker for each priority level
        for priority in MessagePriority:
            worker_name = f"worker_{priority.name.lower()}"
            self.workers[worker_name] = asyncio.create_task(
                self._priority_worker(priority)
            )
        
        # Start monitoring tasks
        self.workers["metrics_collector"] = asyncio.create_task(self._collect_metrics())
        self.workers["dead_letter_processor"] = asyncio.create_task(self._process_dead_letters())
        
        logger.info("✅ All message processing workers started")
    
    async def stop_processing(self):
        """Stop all message processing workers"""
        if not self.running:
            return
        
        self.running = False
        logger.info("🛑 Stopping message processing workers...")
        
        # Cancel all workers
        for worker_name, task in self.workers.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        self.workers.clear()
        logger.info("✅ All message processing workers stopped")
    
    async def _priority_worker(self, priority: MessagePriority):
        """Worker that processes messages from a specific priority queue"""
        queue_name = self.priority_queues[priority]
        worker_id = f"worker_{priority.name}_{int(time.time())}"
        
        logger.info(f"👷 Starting {priority.name} priority worker: {worker_id}")
        
        while self.running:
            try:
                async with self.worker_semaphore:
                    # Read messages from stream
                    messages = await self.redis_client.xreadgroup(
                        "processors",
                        worker_id,
                        {queue_name: ">"},
                        count=self.batch_size,
                        block=int(self.poll_interval * 1000)
                    )
                    
                    if messages:
                        await self._process_message_batch(messages[0][1], queue_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Priority worker {priority.name} error: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"🏁 {priority.name} priority worker stopped: {worker_id}")
    
    async def _process_message_batch(self, message_batch: List, queue_name: str):
        """Process a batch of messages"""
        processing_tasks = []
        
        for stream_id, fields in message_batch:
            # Parse message
            try:
                message = QueueMessage(
                    id=fields["id"],
                    topic=fields["topic"],
                    payload=json.loads(fields["payload"]),
                    priority=MessagePriority(int(fields["priority"])),
                    timestamp=datetime.fromisoformat(fields["timestamp"]),
                    retry_count=int(fields["retry_count"]),
                    max_retries=int(fields["max_retries"]),
                    timeout_seconds=int(fields["timeout_seconds"]),
                    headers=json.loads(fields["headers"])
                )
                
                # Create processing task
                task = asyncio.create_task(
                    self._process_single_message(message, stream_id, queue_name)
                )
                processing_tasks.append(task)
                
            except Exception as e:
                logger.error(f"❌ Failed to parse message {stream_id}: {e}")
                await self._acknowledge_message(queue_name, stream_id)
        
        # Process all messages in batch concurrently
        if processing_tasks:
            await asyncio.gather(*processing_tasks, return_exceptions=True)
    
    async def _process_single_message(self, message: QueueMessage, stream_id: str, queue_name: str):
        """Process a single message"""
        start_time = time.time()
        
        try:
            # Get handler for topic
            handler = self.message_handlers.get(message.topic)
            if not handler:
                raise ValueError(f"No handler registered for topic: {message.topic}")
            
            # Set processing timeout
            try:
                result = await asyncio.wait_for(
                    handler(message.payload),
                    timeout=message.timeout_seconds
                )
                
                processing_time = time.time() - start_time
                
                # Success - acknowledge message
                await self._acknowledge_message(queue_name, stream_id)
                self.processed_count += 1
                
                # Store result if needed
                await self._store_processing_result(ProcessingResult(
                    message_id=message.id,
                    success=True,
                    result_data=result,
                    processing_time=processing_time
                ))
                
                logger.debug(f"✅ Processed message {message.id} in {processing_time:.2f}s")
                
            except asyncio.TimeoutError:
                raise Exception(f"Message processing timeout ({message.timeout_seconds}s)")
                
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Handle failure
            await self._handle_message_failure(
                message, stream_id, queue_name, str(e), processing_time
            )
    
    async def _handle_message_failure(self, message: QueueMessage, stream_id: str, 
                                    queue_name: str, error: str, processing_time: float):
        """Handle message processing failure with retry logic"""
        try:
            message.retry_count += 1
            
            if message.retry_count <= message.max_retries:
                # Retry - re-enqueue with exponential backoff
                await asyncio.sleep(2 ** message.retry_count)  # 2s, 4s, 8s...
                await self.enqueue(message)
                logger.warning(f"🔄 Retrying message {message.id} (attempt {message.retry_count})")
            else:
                # Move to dead letter queue
                await self._move_to_dead_letter(message, error)
                logger.error(f"💀 Message {message.id} moved to dead letter queue after {message.retry_count} retries")
            
            # Store failure result
            await self._store_processing_result(ProcessingResult(
                message_id=message.id,
                success=False,
                error_message=error,
                processing_time=processing_time,
                retry_recommended=message.retry_count <= message.max_retries
            ))
            
            # Acknowledge original message
            await self._acknowledge_message(queue_name, stream_id)
            self.failed_count += 1
            
        except Exception as e:
            logger.error(f"❌ Failed to handle message failure: {e}")
    
    async def _move_to_dead_letter(self, message: QueueMessage, error: str):
        """Move failed message to dead letter queue"""
        try:
            dead_letter_data = {
                **asdict(message),
                "error": error,
                "failed_at": datetime.now(timezone.utc).isoformat(),
                "original_queue": self.priority_queues[message.priority]
            }
            
            await self.redis_client.xadd(
                f"{self.dead_letter_prefix}:{message.topic}",
                dead_letter_data
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to move message to dead letter queue: {e}")
    
    async def _acknowledge_message(self, queue_name: str, stream_id: str):
        """Acknowledge message processing completion"""
        try:
            await self.redis_client.xack(queue_name, "processors", stream_id)
            await self.redis_client.xdel(queue_name, stream_id)
        except Exception as e:
            logger.error(f"❌ Failed to acknowledge message {stream_id}: {e}")
    
    async def _store_processing_result(self, result: ProcessingResult):
        """Store processing result for analytics"""
        try:
            result_key = f"{self.metrics_prefix}:results:{result.message_id}"
            await self.redis_client.setex(
                result_key,
                3600,  # 1 hour TTL
                json.dumps(asdict(result), default=str)
            )
        except Exception as e:
            logger.error(f"❌ Failed to store processing result: {e}")
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics"""
        try:
            stats = {
                "processed_count": self.processed_count,
                "failed_count": self.failed_count,
                "success_rate": self.processed_count / max(self.processed_count + self.failed_count, 1),
                "uptime_seconds": (datetime.now(timezone.utc) - self.start_time).total_seconds() if self.start_time else 0,
                "throughput_per_minute": 0,
                "queue_lengths": {},
                "worker_count": len(self.workers),
                "running": self.running
            }
            
            # Calculate throughput
            if stats["uptime_seconds"] > 0:
                stats["throughput_per_minute"] = (self.processed_count * 60) / stats["uptime_seconds"]
            
            # Get queue lengths
            for priority, queue_name in self.priority_queues.items():
                try:
                    length = await self.redis_client.xlen(queue_name)
                    stats["queue_lengths"][priority.name] = length
                except:
                    stats["queue_lengths"][priority.name] = 0
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get queue stats: {e}")
            return {}
    
    async def _collect_metrics(self):
        """Background task to collect and store metrics"""
        while self.running:
            try:
                stats = await self.get_queue_stats()
                
                # Store metrics in Redis
                metrics_key = f"{self.metrics_prefix}:stats"
                await self.redis_client.setex(
                    metrics_key,
                    300,  # 5 minute TTL
                    json.dumps(stats, default=str)
                )
                
                # Log performance every 5 minutes
                if int(time.time()) % 300 == 0:
                    logger.info(f"📊 Queue Stats: {stats['processed_count']} processed, "
                              f"{stats['success_rate']:.1%} success rate, "
                              f"{stats['throughput_per_minute']:.1f} msg/min")
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Metrics collection error: {e}")
                await asyncio.sleep(60)
    
    async def _process_dead_letters(self):
        """Background task to process dead letter queues"""
        while self.running:
            try:
                # Check all dead letter queues
                pattern = f"{self.dead_letter_prefix}:*"
                dead_letter_queues = await self.redis_client.keys(pattern)
                
                for queue in dead_letter_queues:
                    # Get messages older than 1 hour
                    old_messages = await self.redis_client.xrange(
                        queue,
                        min="-",
                        max=f"{int(time.time() * 1000) - 3600000}-0"  # 1 hour ago
                    )
                    
                    if old_messages:
                        logger.info(f"🧹 Found {len(old_messages)} old dead letter messages in {queue}")
                        # Could implement dead letter cleanup or alerting here
                
                await asyncio.sleep(3600)  # Check every hour
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Dead letter processing error: {e}")
                await asyncio.sleep(3600)
    
    async def _update_queue_metrics(self, operation: str, topic: str, priority: MessagePriority):
        """Update queue operation metrics"""
        try:
            metric_key = f"{self.metrics_prefix}:operations:{operation}:{topic}"
            await self.redis_client.incr(metric_key)
            await self.redis_client.expire(metric_key, 86400)  # 24 hour TTL
        except Exception as e:
            logger.error(f"❌ Failed to update queue metrics: {e}")

# Message Queue Factory Functions

async def create_message_queue(redis_url: str = "redis://localhost:6379") -> AdvancedMessageQueue:
    """Factory function to create and initialize message queue"""
    queue = AdvancedMessageQueue(redis_url)
    await queue.initialize()
    return queue

def create_message(topic: str, payload: Dict[str, Any], 
                  priority: MessagePriority = MessagePriority.NORMAL,
                  timeout_seconds: int = 30) -> QueueMessage:
    """Factory function to create a queue message"""
    return QueueMessage(
        id=str(uuid.uuid4()),
        topic=topic,
        payload=payload,
        priority=priority,
        timestamp=datetime.now(timezone.utc),
        timeout_seconds=timeout_seconds
    )

# Example usage
if __name__ == "__main__":
    async def example_handler(payload: Dict[str, Any]):
        """Example message handler"""
        await asyncio.sleep(0.1)  # Simulate processing
        return {"status": "processed", "result": payload.get("value", 0) * 2}
    
    async def main():
        # Create and setup queue
        queue = await create_message_queue()
        queue.register_handler("test_topic", example_handler)
        
        # Start processing
        await queue.start_processing()
        
        # Enqueue test messages
        messages = [
            create_message("test_topic", {"value": i}, MessagePriority.HIGH)
            for i in range(100)
        ]
        
        await queue.enqueue_bulk(messages)
        
        # Wait and check stats
        await asyncio.sleep(5)
        stats = await queue.get_queue_stats()
        print(f"Processed: {stats['processed_count']}, Success Rate: {stats['success_rate']:.1%}")
        
        await queue.stop_processing()
    
    asyncio.run(main()) 