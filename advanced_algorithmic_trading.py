#!/usr/bin/env python3
"""
Advanced Algorithmic Trading Engine
Professional-grade algorithmic trading strategies with adaptive parameters and machine learning
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
from abc import ABC, abstractmethod

logger = logging.getLogger("AdvancedAlgorithmicTrading")


class AlgorithmType(Enum):
    """Algorithm types."""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    ARBITRAGE = "arbitrage"
    MARKET_MAKING = "market_making"
    STATISTICAL_ARBITRAGE = "statistical_arbitrage"
    PAIRS_TRADING = "pairs_trading"
    TREND_FOLLOWING = "trend_following"
    GRID_TRADING = "grid_trading"
    DCA = "dollar_cost_averaging"
    ML_ADAPTIVE = "ml_adaptive"


class SignalStrength(Enum):
    """Signal strength levels."""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5


@dataclass
class TradingSignal:
    """Trading signal data structure."""
    algorithm: AlgorithmType
    symbol: str
    action: str  # BUY, SELL, HOLD
    strength: SignalStrength
    confidence: float
    price_target: Optional[float]
    stop_loss: Optional[float]
    position_size: float
    reasoning: str
    timestamp: datetime
    metadata: Dict[str, Any]


@dataclass
class AlgorithmPerformance:
    """Algorithm performance metrics."""
    algorithm: AlgorithmType
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration: float
    profit_factor: float
    last_updated: datetime


class BaseAlgorithm(ABC):
    """Base class for trading algorithms."""
    
    def __init__(self, name: str, algorithm_type: AlgorithmType):
        self.name = name
        self.algorithm_type = algorithm_type
        self.parameters = {}
        self.performance_history = []
        self.active = True
        
    @abstractmethod
    def generate_signal(self, market_data: Dict[str, Any]) -> TradingSignal:
        """Generate trading signal based on market data."""
        pass
    
    @abstractmethod
    def update_parameters(self, performance_data: Dict[str, Any]):
        """Update algorithm parameters based on performance."""
        pass


class MomentumAlgorithm(BaseAlgorithm):
    """Advanced momentum trading algorithm."""
    
    def __init__(self):
        super().__init__("Advanced Momentum", AlgorithmType.MOMENTUM)
        self.parameters = {
            "lookback_period": 20,
            "momentum_threshold": 0.02,
            "volume_threshold": 1.5,
            "rsi_upper": 70,
            "rsi_lower": 30,
            "position_size_base": 0.02
        }
    
    def generate_signal(self, market_data: Dict[str, Any]) -> TradingSignal:
        """Generate momentum-based trading signal."""
        try:
            symbol = market_data.get("symbol", "UNKNOWN")
            price = market_data.get("price", 0)
            price_history = market_data.get("price_history", [])
            volume_ratio = market_data.get("volume_ratio", 1.0)
            rsi = market_data.get("rsi", 50)
            
            if len(price_history) < self.parameters["lookback_period"]:
                return self._no_signal(symbol)
            
            # Calculate momentum
            lookback = self.parameters["lookback_period"]
            momentum = (price - price_history[-lookback]) / price_history[-lookback]
            
            # Calculate price velocity (rate of change)
            short_momentum = (price - price_history[-5]) / price_history[-5] if len(price_history) >= 5 else 0
            
            # Determine signal
            action = "HOLD"
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasoning = "No clear momentum signal"
            
            # Strong upward momentum
            if (momentum > self.parameters["momentum_threshold"] and 
                volume_ratio > self.parameters["volume_threshold"] and
                rsi < self.parameters["rsi_upper"] and
                short_momentum > 0):
                
                action = "BUY"
                strength = SignalStrength.STRONG if momentum > 0.05 else SignalStrength.MODERATE
                confidence = min(0.9, 0.5 + momentum * 10)
                reasoning = f"Strong upward momentum: {momentum:.2%}, high volume"
            
            # Strong downward momentum
            elif (momentum < -self.parameters["momentum_threshold"] and 
                  volume_ratio > self.parameters["volume_threshold"] and
                  rsi > self.parameters["rsi_lower"] and
                  short_momentum < 0):
                
                action = "SELL"
                strength = SignalStrength.STRONG if momentum < -0.05 else SignalStrength.MODERATE
                confidence = min(0.9, 0.5 + abs(momentum) * 10)
                reasoning = f"Strong downward momentum: {momentum:.2%}, high volume"
            
            # Calculate position size based on signal strength and confidence
            base_size = self.parameters["position_size_base"]
            position_size = base_size * (strength.value / 5) * confidence
            
            # Set targets
            price_target = None
            stop_loss = None
            
            if action == "BUY":
                price_target = price * (1 + abs(momentum) * 2)  # 2x momentum as target
                stop_loss = price * (1 - abs(momentum))  # 1x momentum as stop
            elif action == "SELL":
                price_target = price * (1 - abs(momentum) * 2)
                stop_loss = price * (1 + abs(momentum))
            
            return TradingSignal(
                algorithm=self.algorithm_type,
                symbol=symbol,
                action=action,
                strength=strength,
                confidence=round(confidence, 4),
                price_target=price_target,
                stop_loss=stop_loss,
                position_size=round(position_size, 6),
                reasoning=reasoning,
                timestamp=datetime.now(timezone.utc),
                metadata={
                    "momentum": round(momentum, 6),
                    "short_momentum": round(short_momentum, 6),
                    "volume_ratio": round(volume_ratio, 4),
                    "rsi": round(rsi, 2)
                }
            )
            
        except Exception as e:
            logger.error(f"Momentum algorithm error: {e}")
            return self._no_signal(market_data.get("symbol", "UNKNOWN"))
    
    def update_parameters(self, performance_data: Dict[str, Any]):
        """Update momentum algorithm parameters."""
        win_rate = performance_data.get("win_rate", 0.5)
        
        # Adaptive parameter adjustment
        if win_rate < 0.4:  # Poor performance
            self.parameters["momentum_threshold"] *= 1.1  # Require stronger momentum
            self.parameters["volume_threshold"] *= 1.05   # Require higher volume
        elif win_rate > 0.6:  # Good performance
            self.parameters["momentum_threshold"] *= 0.95  # Allow weaker momentum
            self.parameters["volume_threshold"] *= 0.98    # Allow lower volume
    
    def _no_signal(self, symbol: str) -> TradingSignal:
        """Generate no-signal response."""
        return TradingSignal(
            algorithm=self.algorithm_type,
            symbol=symbol,
            action="HOLD",
            strength=SignalStrength.VERY_WEAK,
            confidence=0.0,
            price_target=None,
            stop_loss=None,
            position_size=0.0,
            reasoning="Insufficient data or no clear signal",
            timestamp=datetime.now(timezone.utc),
            metadata={}
        )


class MeanReversionAlgorithm(BaseAlgorithm):
    """Advanced mean reversion trading algorithm."""
    
    def __init__(self):
        super().__init__("Advanced Mean Reversion", AlgorithmType.MEAN_REVERSION)
        self.parameters = {
            "lookback_period": 50,
            "bollinger_std": 2.0,
            "rsi_oversold": 30,
            "rsi_overbought": 70,
            "volume_threshold": 0.8,
            "position_size_base": 0.015
        }
    
    def generate_signal(self, market_data: Dict[str, Any]) -> TradingSignal:
        """Generate mean reversion trading signal."""
        try:
            symbol = market_data.get("symbol", "UNKNOWN")
            price = market_data.get("price", 0)
            price_history = market_data.get("price_history", [])
            volume_ratio = market_data.get("volume_ratio", 1.0)
            rsi = market_data.get("rsi", 50)
            
            if len(price_history) < self.parameters["lookback_period"]:
                return self._no_signal(symbol)
            
            # Calculate Bollinger Bands
            lookback = self.parameters["lookback_period"]
            recent_prices = price_history[-lookback:]
            mean_price = np.mean(recent_prices)
            std_price = np.std(recent_prices)
            
            bb_upper = mean_price + (self.parameters["bollinger_std"] * std_price)
            bb_lower = mean_price - (self.parameters["bollinger_std"] * std_price)
            
            # Calculate distance from mean
            distance_from_mean = (price - mean_price) / mean_price
            
            # Determine signal
            action = "HOLD"
            strength = SignalStrength.WEAK
            confidence = 0.5
            reasoning = "No clear mean reversion signal"
            
            # Oversold condition (buy signal)
            if (price < bb_lower and 
                rsi < self.parameters["rsi_oversold"] and
                distance_from_mean < -0.02):  # At least 2% below mean
                
                action = "BUY"
                strength = SignalStrength.STRONG if rsi < 25 else SignalStrength.MODERATE
                confidence = min(0.9, 0.6 + abs(distance_from_mean) * 5)
                reasoning = f"Oversold: Price {distance_from_mean:.2%} below mean, RSI {rsi:.1f}"
            
            # Overbought condition (sell signal)
            elif (price > bb_upper and 
                  rsi > self.parameters["rsi_overbought"] and
                  distance_from_mean > 0.02):  # At least 2% above mean
                
                action = "SELL"
                strength = SignalStrength.STRONG if rsi > 75 else SignalStrength.MODERATE
                confidence = min(0.9, 0.6 + abs(distance_from_mean) * 5)
                reasoning = f"Overbought: Price {distance_from_mean:.2%} above mean, RSI {rsi:.1f}"
            
            # Calculate position size
            base_size = self.parameters["position_size_base"]
            position_size = base_size * (strength.value / 5) * confidence
            
            # Set targets (mean reversion targets)
            price_target = None
            stop_loss = None
            
            if action == "BUY":
                price_target = mean_price  # Target is the mean
                stop_loss = price * 0.95   # 5% stop loss
            elif action == "SELL":
                price_target = mean_price  # Target is the mean
                stop_loss = price * 1.05   # 5% stop loss
            
            return TradingSignal(
                algorithm=self.algorithm_type,
                symbol=symbol,
                action=action,
                strength=strength,
                confidence=round(confidence, 4),
                price_target=price_target,
                stop_loss=stop_loss,
                position_size=round(position_size, 6),
                reasoning=reasoning,
                timestamp=datetime.now(timezone.utc),
                metadata={
                    "distance_from_mean": round(distance_from_mean, 6),
                    "bollinger_upper": round(bb_upper, 2),
                    "bollinger_lower": round(bb_lower, 2),
                    "mean_price": round(mean_price, 2),
                    "rsi": round(rsi, 2)
                }
            )
            
        except Exception as e:
            logger.error(f"Mean reversion algorithm error: {e}")
            return self._no_signal(market_data.get("symbol", "UNKNOWN"))
    
    def update_parameters(self, performance_data: Dict[str, Any]):
        """Update mean reversion algorithm parameters."""
        win_rate = performance_data.get("win_rate", 0.5)
        
        if win_rate < 0.4:
            self.parameters["bollinger_std"] *= 1.1  # Wider bands
            self.parameters["rsi_oversold"] -= 2     # More extreme RSI
            self.parameters["rsi_overbought"] += 2
        elif win_rate > 0.6:
            self.parameters["bollinger_std"] *= 0.95  # Tighter bands
            self.parameters["rsi_oversold"] += 1      # Less extreme RSI
            self.parameters["rsi_overbought"] -= 1
    
    def _no_signal(self, symbol: str) -> TradingSignal:
        """Generate no-signal response."""
        return TradingSignal(
            algorithm=self.algorithm_type,
            symbol=symbol,
            action="HOLD",
            strength=SignalStrength.VERY_WEAK,
            confidence=0.0,
            price_target=None,
            stop_loss=None,
            position_size=0.0,
            reasoning="Insufficient data or no clear signal",
            timestamp=datetime.now(timezone.utc),
            metadata={}
        )


class GridTradingAlgorithm(BaseAlgorithm):
    """Advanced grid trading algorithm."""
    
    def __init__(self):
        super().__init__("Advanced Grid Trading", AlgorithmType.GRID_TRADING)
        self.parameters = {
            "grid_spacing": 0.01,  # 1% grid spacing
            "num_grids": 10,
            "base_position_size": 0.01,
            "volatility_adjustment": True,
            "trend_filter": True
        }
        self.grid_levels = []
        self.grid_positions = {}
    
    def generate_signal(self, market_data: Dict[str, Any]) -> TradingSignal:
        """Generate grid trading signal."""
        try:
            symbol = market_data.get("symbol", "UNKNOWN")
            price = market_data.get("price", 0)
            volatility = market_data.get("volatility", 0.02)
            
            # Initialize grid if not exists
            if not self.grid_levels:
                self._initialize_grid(price, volatility)
            
            # Check if price hit any grid level
            action = "HOLD"
            strength = SignalStrength.MODERATE
            confidence = 0.7
            reasoning = "Grid trading signal"
            
            # Find nearest grid levels
            lower_grid = None
            upper_grid = None
            
            for level in self.grid_levels:
                if level < price:
                    lower_grid = level
                elif level > price and upper_grid is None:
                    upper_grid = level
                    break
            
            # Check for grid triggers
            position_size = self.parameters["base_position_size"]
            
            # Buy at lower grid levels
            if lower_grid and abs(price - lower_grid) / price < 0.005:  # Within 0.5%
                action = "BUY"
                reasoning = f"Grid buy signal at level {lower_grid:.2f}"
            
            # Sell at upper grid levels
            elif upper_grid and abs(price - upper_grid) / price < 0.005:  # Within 0.5%
                action = "SELL"
                reasoning = f"Grid sell signal at level {upper_grid:.2f}"
            
            return TradingSignal(
                algorithm=self.algorithm_type,
                symbol=symbol,
                action=action,
                strength=strength,
                confidence=confidence,
                price_target=upper_grid if action == "BUY" else lower_grid,
                stop_loss=None,  # Grid trading typically doesn't use stop losses
                position_size=position_size,
                reasoning=reasoning,
                timestamp=datetime.now(timezone.utc),
                metadata={
                    "grid_levels": self.grid_levels,
                    "lower_grid": lower_grid,
                    "upper_grid": upper_grid,
                    "current_price": price
                }
            )
            
        except Exception as e:
            logger.error(f"Grid trading algorithm error: {e}")
            return self._no_signal(market_data.get("symbol", "UNKNOWN"))
    
    def _initialize_grid(self, center_price: float, volatility: float):
        """Initialize grid levels around current price."""
        # Adjust grid spacing based on volatility
        if self.parameters["volatility_adjustment"]:
            adjusted_spacing = self.parameters["grid_spacing"] * (1 + volatility)
        else:
            adjusted_spacing = self.parameters["grid_spacing"]
        
        num_grids = self.parameters["num_grids"]
        
        # Create grid levels above and below current price
        self.grid_levels = []
        
        for i in range(-num_grids//2, num_grids//2 + 1):
            level = center_price * (1 + i * adjusted_spacing)
            self.grid_levels.append(level)
        
        self.grid_levels.sort()
        logger.info(f"Grid initialized with {len(self.grid_levels)} levels around {center_price:.2f}")
    
    def update_parameters(self, performance_data: Dict[str, Any]):
        """Update grid trading parameters."""
        win_rate = performance_data.get("win_rate", 0.5)
        
        if win_rate < 0.4:
            self.parameters["grid_spacing"] *= 1.1  # Wider spacing
        elif win_rate > 0.6:
            self.parameters["grid_spacing"] *= 0.95  # Tighter spacing
    
    def _no_signal(self, symbol: str) -> TradingSignal:
        """Generate no-signal response."""
        return TradingSignal(
            algorithm=self.algorithm_type,
            symbol=symbol,
            action="HOLD",
            strength=SignalStrength.VERY_WEAK,
            confidence=0.0,
            price_target=None,
            stop_loss=None,
            position_size=0.0,
            reasoning="No grid signal",
            timestamp=datetime.now(timezone.utc),
            metadata={}
        )


class AdvancedAlgorithmicTrading:
    """Advanced algorithmic trading engine."""
    
    def __init__(self):
        self.algorithms = {}
        self.signal_history = {}
        self.performance_metrics = {}
        
        # Initialize algorithms
        self._initialize_algorithms()
    
    def _initialize_algorithms(self):
        """Initialize all trading algorithms."""
        self.algorithms = {
            AlgorithmType.MOMENTUM: MomentumAlgorithm(),
            AlgorithmType.MEAN_REVERSION: MeanReversionAlgorithm(),
            AlgorithmType.GRID_TRADING: GridTradingAlgorithm()
        }
        
        logger.info(f"✅ Initialized {len(self.algorithms)} trading algorithms")
    
    def generate_all_signals(self, market_data: Dict[str, Any]) -> Dict[AlgorithmType, TradingSignal]:
        """Generate signals from all active algorithms."""
        signals = {}
        
        for algo_type, algorithm in self.algorithms.items():
            if algorithm.active:
                try:
                    signal = algorithm.generate_signal(market_data)
                    signals[algo_type] = signal
                    
                    # Store signal history
                    symbol = market_data.get("symbol", "UNKNOWN")
                    if symbol not in self.signal_history:
                        self.signal_history[symbol] = {}
                    if algo_type not in self.signal_history[symbol]:
                        self.signal_history[symbol][algo_type] = []
                    
                    self.signal_history[symbol][algo_type].append(signal)
                    
                    # Keep last 1000 signals
                    if len(self.signal_history[symbol][algo_type]) > 1000:
                        self.signal_history[symbol][algo_type] = self.signal_history[symbol][algo_type][-1000:]
                        
                except Exception as e:
                    logger.error(f"Algorithm {algo_type.value} error: {e}")
        
        return signals
    
    def get_consensus_signal(self, signals: Dict[AlgorithmType, TradingSignal]) -> TradingSignal:
        """Generate consensus signal from multiple algorithms."""
        if not signals:
            return self._default_signal()
        
        # Weight signals by confidence and strength
        buy_score = 0
        sell_score = 0
        total_weight = 0
        
        for signal in signals.values():
            weight = signal.confidence * (signal.strength.value / 5)
            
            if signal.action == "BUY":
                buy_score += weight
            elif signal.action == "SELL":
                sell_score += weight
            
            total_weight += weight
        
        # Determine consensus action
        if total_weight == 0:
            action = "HOLD"
            confidence = 0.0
            strength = SignalStrength.VERY_WEAK
        elif buy_score > sell_score * 1.2:  # 20% threshold
            action = "BUY"
            confidence = min(buy_score / total_weight, 1.0)
            strength = self._score_to_strength(buy_score / total_weight)
        elif sell_score > buy_score * 1.2:
            action = "SELL"
            confidence = min(sell_score / total_weight, 1.0)
            strength = self._score_to_strength(sell_score / total_weight)
        else:
            action = "HOLD"
            confidence = 0.5
            strength = SignalStrength.WEAK
        
        # Calculate average position size
        avg_position_size = np.mean([s.position_size for s in signals.values() if s.position_size > 0])
        if np.isnan(avg_position_size):
            avg_position_size = 0.0
        
        # Get first signal's symbol
        symbol = list(signals.values())[0].symbol if signals else "UNKNOWN"
        
        return TradingSignal(
            algorithm=AlgorithmType.ML_ADAPTIVE,  # Consensus algorithm
            symbol=symbol,
            action=action,
            strength=strength,
            confidence=round(confidence, 4),
            price_target=None,
            stop_loss=None,
            position_size=round(avg_position_size, 6),
            reasoning=f"Consensus from {len(signals)} algorithms",
            timestamp=datetime.now(timezone.utc),
            metadata={
                "buy_score": round(buy_score, 4),
                "sell_score": round(sell_score, 4),
                "total_weight": round(total_weight, 4),
                "algorithms_count": len(signals)
            }
        )
    
    def _score_to_strength(self, score: float) -> SignalStrength:
        """Convert score to signal strength."""
        if score >= 0.8:
            return SignalStrength.VERY_STRONG
        elif score >= 0.6:
            return SignalStrength.STRONG
        elif score >= 0.4:
            return SignalStrength.MODERATE
        elif score >= 0.2:
            return SignalStrength.WEAK
        else:
            return SignalStrength.VERY_WEAK
    
    def _default_signal(self) -> TradingSignal:
        """Default signal for error cases."""
        return TradingSignal(
            algorithm=AlgorithmType.ML_ADAPTIVE,
            symbol="UNKNOWN",
            action="HOLD",
            strength=SignalStrength.VERY_WEAK,
            confidence=0.0,
            price_target=None,
            stop_loss=None,
            position_size=0.0,
            reasoning="No signals available",
            timestamp=datetime.now(timezone.utc),
            metadata={}
        )
    
    def get_algorithm_performance(self) -> Dict[AlgorithmType, Dict[str, Any]]:
        """Get performance metrics for all algorithms."""
        performance = {}
        
        for algo_type, algorithm in self.algorithms.items():
            # Simplified performance calculation
            performance[algo_type] = {
                "algorithm_name": algorithm.name,
                "active": algorithm.active,
                "parameters": algorithm.parameters,
                "signals_generated": len(algorithm.performance_history),
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
        
        return performance


# Test the Advanced Algorithmic Trading Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🤖 Testing Advanced Algorithmic Trading Engine")
    print("=" * 70)
    
    # Initialize algorithmic trading engine
    algo_engine = AdvancedAlgorithmicTrading()
    
    # Sample market data
    market_data = {
        "symbol": "BTCUSDT",
        "price": 44500,
        "price_history": [44000 + i * 10 for i in range(100)],  # Uptrend
        "volume_ratio": 1.8,
        "rsi": 65,
        "volatility": 0.25
    }
    
    # Generate signals from all algorithms
    print("📊 Generating signals from all algorithms...")
    signals = algo_engine.generate_all_signals(market_data)
    
    for algo_type, signal in signals.items():
        print(f"✅ {algo_type.value}: {signal.action} (Strength: {signal.strength.name}, Confidence: {signal.confidence:.2f})")
        print(f"   Reasoning: {signal.reasoning}")
    
    # Generate consensus signal
    print("\n🎯 Generating consensus signal...")
    consensus = algo_engine.get_consensus_signal(signals)
    print(f"✅ Consensus: {consensus.action} (Strength: {consensus.strength.name}, Confidence: {consensus.confidence:.2f})")
    print(f"   Position Size: {consensus.position_size:.4f}")
    print(f"   Reasoning: {consensus.reasoning}")
    
    # Get algorithm performance
    print("\n📈 Algorithm performance...")
    performance = algo_engine.get_algorithm_performance()
    for algo_type, perf in performance.items():
        print(f"✅ {perf['algorithm_name']}: Active={perf['active']}")
    
    print("\n✅ Advanced Algorithmic Trading Engine test completed!")
