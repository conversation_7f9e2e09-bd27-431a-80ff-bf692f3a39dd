"""
Advanced Portfolio Manager Agent - Qwen2.5:32b Model
Sophisticated portfolio management with dynamic allocation, risk optimization,
performance attribution, and multi-strategy coordination.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import logging
from scipy.optimize import minimize
from sklearn.covariance import LedoitWolf
import warnings
warnings.filterwarnings('ignore')

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, Portfolio, PortfolioAllocation
from src.utils.portfolio_optimizer import PortfolioOptimizer
from src.utils.risk_budgeting import RiskBudgetingEngine
from src.utils.performance_analytics import PerformanceAnalytics
from src.utils.rebalancing_engine import RebalancingEngine


class AllocationStrategy(Enum):
    EQUAL_WEIGHT = "equal_weight"
    MARKET_CAP_WEIGHT = "market_cap_weight"
    RISK_PARITY = "risk_parity"
    MEAN_VARIANCE = "mean_variance"
    BLACK_LITTERMAN = "black_litterman"
    HIERARCHICAL_RISK_PARITY = "hierarchical_risk_parity"
    DYNAMIC_ALLOCATION = "dynamic_allocation"
    FACTOR_BASED = "factor_based"
    AI_OPTIMIZED = "ai_optimized"


class RebalancingTrigger(Enum):
    TIME_BASED = "time_based"
    THRESHOLD_BASED = "threshold_based"
    VOLATILITY_BASED = "volatility_based"
    MOMENTUM_BASED = "momentum_based"
    AI_SIGNAL = "ai_signal"


@dataclass
class PortfolioPosition:
    symbol: str
    quantity: float
    market_value: float
    weight: float
    target_weight: float
    drift: float
    unrealized_pnl: float
    realized_pnl: float
    cost_basis: float
    last_updated: datetime


@dataclass
class PortfolioMetrics:
    portfolio_id: str
    total_value: float
    total_return: float
    daily_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    information_ratio: float
    tracking_error: float
    beta: float
    alpha: float
    var_95: float
    var_99: float
    expected_shortfall: float
    correlation_matrix: Dict[str, Dict[str, float]]
    risk_attribution: Dict[str, float]
    performance_attribution: Dict[str, float]
    timestamp: datetime


@dataclass
class RebalancingRecommendation:
    portfolio_id: str
    trigger_reason: str
    current_allocation: Dict[str, float]
    target_allocation: Dict[str, float]
    trades_required: List[Dict[str, Any]]
    expected_impact: Dict[str, float]
    execution_cost: float
    risk_impact: float
    confidence: float
    urgency: int
    ai_rationale: str
    created_at: datetime


class AdvancedPortfolioManager(BaseAgent):
    """
    Advanced Portfolio Manager using Qwen2.5:32b for sophisticated portfolio management.
    
    Features:
    - Multi-strategy portfolio optimization
    - Dynamic asset allocation with AI
    - Risk budgeting and parity strategies
    - Performance attribution analysis
    - Intelligent rebalancing algorithms
    - Factor-based portfolio construction
    - ESG integration and constraints
    - Multi-timeframe optimization
    - Transaction cost optimization
    - Liquidity management
    - Stress testing and scenario analysis
    - Real-time risk monitoring
    - Alpha generation and capture
    - Benchmark tracking and outperformance
    - Portfolio analytics and reporting
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_portfolio_manager"
        self.model_name = "qwen2.5:32b"
        
        # Portfolio management components
        self.portfolio_optimizer = PortfolioOptimizer()
        self.risk_budgeting = RiskBudgetingEngine()
        self.performance_analytics = PerformanceAnalytics()
        self.rebalancing_engine = RebalancingEngine()
        
        # Portfolio data storage
        self.portfolios = {}
        self.portfolio_metrics = {}
        self.allocation_history = {}
        self.rebalancing_history = {}
        self.performance_history = {}
        
        # Portfolio configuration
        self.default_config = {
            "allocation_strategy": AllocationStrategy.AI_OPTIMIZED,
            "rebalancing_frequency": "weekly",
            "rebalancing_threshold": 0.05,  # 5% drift threshold
            "max_position_weight": 0.25,    # 25% max single position
            "min_position_weight": 0.01,    # 1% min position
            "transaction_cost": 0.001,      # 0.1% transaction cost
            "risk_budget": 0.15,            # 15% annual volatility target
            "benchmark": "CRYPTO_INDEX",
            "optimization_frequency": "daily"
        }
        
        # Risk parameters
        self.risk_limits = {
            "max_portfolio_var": 0.03,      # 3% daily VaR
            "max_concentration": 0.30,      # 30% max sector concentration
            "max_leverage": 2.0,            # 2x max leverage
            "min_liquidity_ratio": 0.10,    # 10% min liquid assets
            "max_correlation": 0.80,        # 80% max correlation
            "max_tracking_error": 0.05      # 5% max tracking error
        }
        
        # Optimization constraints
        self.optimization_constraints = {
            "long_only": True,
            "max_turnover": 0.50,           # 50% max monthly turnover
            "min_diversification": 5,       # Min 5 positions
            "max_positions": 20,            # Max 20 positions
            "sector_limits": {},            # Sector-specific limits
            "esg_score_min": 0.0           # ESG minimum score
        }
        
        # Performance benchmarks
        self.benchmarks = {
            "CRYPTO_INDEX": {"return": 0.0, "volatility": 0.0, "sharpe": 0.0},
            "BTC": {"return": 0.0, "volatility": 0.0, "sharpe": 0.0},
            "ETH": {"return": 0.0, "volatility": 0.0, "sharpe": 0.0}
        }
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced portfolio manager components."""
        self.logger.info("💼 Initializing Advanced Portfolio Manager with Qwen2.5:32b")
        
        # Initialize portfolio management components
        await self.portfolio_optimizer.initialize()
        await self.risk_budgeting.initialize()
        await self.performance_analytics.initialize()
        await self.rebalancing_engine.initialize()
        
        # Load portfolio data
        await self._load_portfolio_data()
        
        # Initialize optimization models
        await self._initialize_optimization_models()
        
        self.logger.info("✅ Advanced Portfolio Manager initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced portfolio management tasks."""
        return [
            asyncio.create_task(self._portfolio_monitoring()),
            asyncio.create_task(self._dynamic_allocation()),
            asyncio.create_task(self._risk_monitoring()),
            asyncio.create_task(self._performance_analysis()),
            asyncio.create_task(self._rebalancing_analysis()),
            asyncio.create_task(self._optimization_engine()),
            asyncio.create_task(self._factor_analysis()),
            asyncio.create_task(self._stress_testing()),
            asyncio.create_task(self._alpha_generation()),
            asyncio.create_task(self._portfolio_reporting())
        ]

    async def _portfolio_monitoring(self):
        """Continuous portfolio monitoring and metrics calculation."""
        while self.running:
            try:
                for portfolio_id, portfolio in self.portfolios.items():
                    # Update portfolio positions
                    await self._update_portfolio_positions(portfolio_id)
                    
                    # Calculate portfolio metrics
                    metrics = await self._calculate_portfolio_metrics(portfolio_id)
                    
                    # Store metrics
                    self.portfolio_metrics[portfolio_id] = metrics
                    
                    # Check risk limits
                    violations = await self._check_risk_limits(portfolio_id, metrics)
                    
                    # Handle violations
                    for violation in violations:
                        await self._handle_risk_violation(portfolio_id, violation)
                
                await asyncio.sleep(30)  # Every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Portfolio monitoring error: {e}")
                await asyncio.sleep(10)

    async def _dynamic_allocation(self):
        """Dynamic asset allocation using AI and market conditions."""
        while self.running:
            try:
                for portfolio_id, portfolio in self.portfolios.items():
                    # Get current market conditions
                    market_conditions = await self._get_market_conditions()
                    
                    # Get agent recommendations
                    agent_recommendations = await self._get_agent_recommendations()
                    
                    # Generate AI-optimized allocation
                    optimal_allocation = await self._generate_ai_allocation(
                        portfolio_id, market_conditions, agent_recommendations
                    )
                    
                    # Evaluate allocation change
                    if await self._should_update_allocation(portfolio_id, optimal_allocation):
                        await self._update_portfolio_allocation(portfolio_id, optimal_allocation)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Dynamic allocation error: {e}")
                await asyncio.sleep(60)

    async def _generate_ai_allocation(self, portfolio_id: str, market_conditions: Dict, agent_recommendations: Dict) -> Dict[str, float]:
        """Generate AI-optimized portfolio allocation."""
        
        # Get current portfolio state
        current_portfolio = self.portfolios[portfolio_id]
        current_metrics = self.portfolio_metrics.get(portfolio_id, {})
        
        # Prepare AI context
        ai_context = {
            "portfolio_id": portfolio_id,
            "current_allocation": await self._get_current_allocation(portfolio_id),
            "current_metrics": current_metrics,
            "market_conditions": market_conditions,
            "agent_recommendations": agent_recommendations,
            "risk_limits": self.risk_limits,
            "optimization_constraints": self.optimization_constraints,
            "performance_history": await self._get_performance_history(portfolio_id)
        }
        
        ai_allocation_analysis = await ai_service.generate_response(
            "portfolio_manager",
            f"""
            As an expert portfolio manager using Qwen2.5:32b, optimize the allocation for portfolio {portfolio_id}:
            
            Current Allocation: {ai_context['current_allocation']}
            Current Metrics: {ai_context['current_metrics']}
            Market Conditions: {ai_context['market_conditions']}
            Agent Recommendations: {ai_context['agent_recommendations']}
            Risk Limits: {ai_context['risk_limits']}
            Constraints: {ai_context['optimization_constraints']}
            Performance History: {ai_context['performance_history']}
            
            Provide sophisticated allocation optimization including:
            1. Optimal asset weights with rationale
            2. Risk-return optimization analysis
            3. Factor exposure optimization
            4. Diversification enhancement
            5. Alpha generation opportunities
            6. Risk budgeting allocation
            7. Transaction cost considerations
            8. Liquidity management
            9. Market timing adjustments
            10. Stress test resilience
            
            Consider:
            - Current market regime and outlook
            - Agent consensus and conflicts
            - Risk-adjusted return maximization
            - Downside protection
            - Correlation structure changes
            - Momentum and mean reversion factors
            
            Provide specific allocation percentages and implementation guidance.
            """,
            ai_context
        )
        
        # Parse AI allocation recommendations
        allocation = await self._parse_allocation_recommendations(ai_allocation_analysis)
        
        # Validate and normalize allocation
        validated_allocation = await self._validate_allocation(allocation)
        
        return validated_allocation

    async def _rebalancing_analysis(self):
        """Analyze rebalancing needs and generate recommendations."""
        while self.running:
            try:
                for portfolio_id, portfolio in self.portfolios.items():
                    # Check rebalancing triggers
                    triggers = await self._check_rebalancing_triggers(portfolio_id)
                    
                    if triggers:
                        # Generate rebalancing recommendation
                        recommendation = await self._generate_rebalancing_recommendation(
                            portfolio_id, triggers
                        )
                        
                        # Evaluate recommendation
                        if await self._should_execute_rebalancing(recommendation):
                            await self._execute_rebalancing(recommendation)
                
                await asyncio.sleep(600)  # Every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Rebalancing analysis error: {e}")
                await asyncio.sleep(120)

    async def _generate_rebalancing_recommendation(self, portfolio_id: str, triggers: List[str]) -> RebalancingRecommendation:
        """Generate intelligent rebalancing recommendation."""
        
        current_allocation = await self._get_current_allocation(portfolio_id)
        target_allocation = await self._get_target_allocation(portfolio_id)
        
        # Calculate required trades
        trades_required = await self._calculate_rebalancing_trades(
            portfolio_id, current_allocation, target_allocation
        )
        
        # Estimate execution costs
        execution_cost = await self._estimate_execution_cost(trades_required)
        
        # Assess risk impact
        risk_impact = await self._assess_rebalancing_risk_impact(
            portfolio_id, current_allocation, target_allocation
        )
        
        # Get AI analysis for rebalancing
        ai_context = {
            "portfolio_id": portfolio_id,
            "triggers": triggers,
            "current_allocation": current_allocation,
            "target_allocation": target_allocation,
            "trades_required": trades_required,
            "execution_cost": execution_cost,
            "risk_impact": risk_impact,
            "market_conditions": await self._get_market_conditions()
        }
        
        ai_rebalancing_analysis = await ai_service.generate_response(
            "portfolio_manager",
            f"""
            As a portfolio manager, analyze this rebalancing opportunity for portfolio {portfolio_id}:
            
            Triggers: {triggers}
            Current Allocation: {current_allocation}
            Target Allocation: {target_allocation}
            Required Trades: {trades_required}
            Execution Cost: {execution_cost}
            Risk Impact: {risk_impact}
            Market Conditions: {ai_context['market_conditions']}
            
            Provide comprehensive rebalancing analysis including:
            1. Rebalancing recommendation (execute/defer/modify)
            2. Optimal execution timing and strategy
            3. Cost-benefit analysis
            4. Risk impact assessment
            5. Market timing considerations
            6. Alternative rebalancing approaches
            7. Urgency and priority assessment
            8. Implementation steps
            
            Consider transaction costs, market impact, and timing.
            Focus on value-added rebalancing decisions.
            """,
            ai_context
        )
        
        # Parse AI analysis
        analysis_data = await self._parse_rebalancing_analysis(ai_rebalancing_analysis)
        
        recommendation = RebalancingRecommendation(
            portfolio_id=portfolio_id,
            trigger_reason=", ".join(triggers),
            current_allocation=current_allocation,
            target_allocation=target_allocation,
            trades_required=trades_required,
            expected_impact=analysis_data.get("expected_impact", {}),
            execution_cost=execution_cost,
            risk_impact=risk_impact,
            confidence=analysis_data.get("confidence", 0.5),
            urgency=analysis_data.get("urgency", 5),
            ai_rationale=ai_rebalancing_analysis,
            created_at=datetime.utcnow()
        )
        
        return recommendation

    async def _calculate_portfolio_metrics(self, portfolio_id: str) -> PortfolioMetrics:
        """Calculate comprehensive portfolio metrics."""
        
        portfolio = self.portfolios[portfolio_id]
        positions = await self._get_portfolio_positions(portfolio_id)
        
        # Calculate basic metrics
        total_value = sum(pos.market_value for pos in positions.values())
        
        # Get historical returns
        returns = await self._get_portfolio_returns(portfolio_id, days=252)
        
        if len(returns) > 0:
            # Performance metrics
            total_return = (total_value / portfolio.get("initial_value", total_value)) - 1
            daily_return = returns[-1] if len(returns) > 0 else 0.0
            volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0.0
            
            # Risk-adjusted metrics
            risk_free_rate = 0.02  # 2% risk-free rate
            excess_returns = np.array(returns) - (risk_free_rate / 252)
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0.0
            
            # Downside metrics
            negative_returns = [r for r in returns if r < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 1 else 0.0
            sortino_ratio = np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0.0
            
            # Drawdown calculation
            cumulative_returns = np.cumprod(1 + np.array(returns))
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0.0
            
            # Calmar ratio
            calmar_ratio = (total_return / abs(max_drawdown)) if max_drawdown != 0 else 0.0
            
            # VaR calculations
            var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0.0
            var_99 = np.percentile(returns, 1) if len(returns) > 0 else 0.0
            
            # Expected Shortfall
            tail_losses = [r for r in returns if r <= var_95]
            expected_shortfall = np.mean(tail_losses) if len(tail_losses) > 0 else 0.0
        else:
            # Default values when no return history
            total_return = daily_return = volatility = 0.0
            sharpe_ratio = sortino_ratio = max_drawdown = calmar_ratio = 0.0
            var_95 = var_99 = expected_shortfall = 0.0
        
        # Calculate correlation matrix
        correlation_matrix = await self._calculate_correlation_matrix(portfolio_id)
        
        # Risk and performance attribution
        risk_attribution = await self._calculate_risk_attribution(portfolio_id)
        performance_attribution = await self._calculate_performance_attribution(portfolio_id)
        
        metrics = PortfolioMetrics(
            portfolio_id=portfolio_id,
            total_value=total_value,
            total_return=total_return,
            daily_return=daily_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            information_ratio=0.0,  # Would calculate vs benchmark
            tracking_error=0.0,     # Would calculate vs benchmark
            beta=1.0,               # Would calculate vs benchmark
            alpha=0.0,              # Would calculate vs benchmark
            var_95=var_95,
            var_99=var_99,
            expected_shortfall=expected_shortfall,
            correlation_matrix=correlation_matrix,
            risk_attribution=risk_attribution,
            performance_attribution=performance_attribution,
            timestamp=datetime.utcnow()
        )
        
        return metrics

    async def _check_rebalancing_triggers(self, portfolio_id: str) -> List[str]:
        """Check various rebalancing triggers."""
        
        triggers = []
        
        # Threshold-based trigger
        current_allocation = await self._get_current_allocation(portfolio_id)
        target_allocation = await self._get_target_allocation(portfolio_id)
        
        max_drift = 0.0
        for symbol in current_allocation:
            drift = abs(current_allocation.get(symbol, 0) - target_allocation.get(symbol, 0))
            max_drift = max(max_drift, drift)
        
        if max_drift > self.default_config["rebalancing_threshold"]:
            triggers.append("threshold_drift")
        
        # Time-based trigger
        last_rebalance = await self._get_last_rebalance_date(portfolio_id)
        if last_rebalance:
            days_since_rebalance = (datetime.utcnow() - last_rebalance).days
            if days_since_rebalance >= 7:  # Weekly rebalancing
                triggers.append("time_based")
        
        # Volatility-based trigger
        current_metrics = self.portfolio_metrics.get(portfolio_id, {})
        if hasattr(current_metrics, 'volatility') and current_metrics.volatility > 0.25:  # 25% volatility
            triggers.append("high_volatility")
        
        # Risk limit trigger
        if hasattr(current_metrics, 'var_95') and abs(current_metrics.var_95) > self.risk_limits["max_portfolio_var"]:
            triggers.append("risk_limit")
        
        return triggers

    async def _cleanup_agent(self):
        """Cleanup portfolio manager resources."""
        self.logger.info("🧹 Cleaning up Advanced Portfolio Manager resources")
        
        # Generate final portfolio reports
        await self._generate_final_reports()
        
        # Save portfolio data
        await self._save_portfolio_data()
        
        # Clear memory structures
        self.portfolios.clear()
        self.portfolio_metrics.clear()
        self.allocation_history.clear()
        self.rebalancing_history.clear()
        self.performance_history.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "portfolio_update":
            await self._process_portfolio_update(message.content)
        elif message.message_type == "allocation_request":
            await self._process_allocation_request(message.content)
        elif message.message_type == "rebalancing_request":
            await self._process_rebalancing_request(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic portfolio analysis."""
        while self.running:
            try:
                # Generate portfolio reports
                await self._generate_portfolio_reports()
                
                # Update optimization models
                await self._update_optimization_models()
                
                # Analyze performance attribution
                await self._analyze_performance_attribution()
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Periodic portfolio analysis error: {e}")
                await asyncio.sleep(300)
