"""
Integration Test Configuration for NORYON V2

Shared fixtures and configuration for comprehensive integration testing.
"""

import pytest
import asyncio
import json
import os
import tempfile
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# Import test utilities
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def integration_config():
    """Configuration for integration tests."""
    return {
        "test_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"],
        "test_duration": 30,  # seconds
        "performance_thresholds": {
            "api_response_time": 0.1,  # 100ms
            "ai_analysis_time": 0.5,   # 500ms
            "database_query_time": 0.05,  # 50ms
            "throughput_min": 100,     # requests/second
            "memory_limit": 1000,      # MB
            "error_rate_max": 0.05     # 5%
        },
        "load_test_config": {
            "concurrent_users": 50,
            "requests_per_user": 20,
            "ramp_up_time": 10
        }
    }


@pytest.fixture
async def mock_database_stack():
    """Mock complete database stack (Redis, PostgreSQL, ClickHouse, MongoDB)."""
    
    # Mock Redis
    mock_redis = AsyncMock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.setex.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    mock_redis.ping.return_value = True
    mock_redis.close = AsyncMock()
    
    # Mock PostgreSQL
    mock_postgres_session = AsyncMock()
    mock_postgres_session.execute.return_value = None
    mock_postgres_session.fetch.return_value = []
    mock_postgres_session.fetchrow.return_value = None
    mock_postgres_session.commit.return_value = None
    mock_postgres_session.rollback.return_value = None
    mock_postgres_session.close.return_value = None
    
    # Mock ClickHouse
    mock_clickhouse = MagicMock()
    mock_clickhouse.execute.return_value = []
    mock_clickhouse.close = MagicMock()
    
    # Mock MongoDB
    mock_mongodb = AsyncMock()
    mock_mongodb_collection = AsyncMock()
    mock_mongodb_collection.find.return_value.to_list.return_value = []
    mock_mongodb_collection.insert_one.return_value.inserted_id = "test_id"
    mock_mongodb_collection.update_one.return_value.modified_count = 1
    mock_mongodb_collection.delete_one.return_value.deleted_count = 1
    mock_mongodb.__getitem__.return_value = mock_mongodb_collection
    
    return {
        "redis": mock_redis,
        "postgres": mock_postgres_session,
        "clickhouse": mock_clickhouse,
        "mongodb": mock_mongodb
    }


@pytest.fixture
async def mock_ai_service_stack():
    """Mock complete AI service stack with all models."""
    mock_ai_service = AsyncMock()
    
    # Mock different AI model responses
    ai_responses = {
        "market_watcher": "Market analysis: Strong bullish momentum detected",
        "strategy_researcher": "Strategy: BUY signal with 80% confidence",
        "technical_analyst": "Technical: RSI oversold, MACD bullish crossover",
        "risk_officer": "Risk: Low risk, position size acceptable",
        "news_analyst": "News: Positive sentiment, institutional adoption",
        "trade_executor": "Execution: Order placed successfully",
        "compliance_auditor": "Compliance: All regulations satisfied",
        "chief_analyst": "Analysis: Comprehensive bullish outlook",
        "portfolio_manager": "Portfolio: Rebalancing recommended"
    }
    
    def mock_ai_response(agent_type, prompt, context=None):
        return ai_responses.get(agent_type, f"Mock response for {agent_type}")
    
    mock_ai_service.generate_response.side_effect = mock_ai_response
    mock_ai_service.analyze_market_data.return_value = "Comprehensive market analysis"
    mock_ai_service.generate_trading_strategy.return_value = "Strategic trading recommendation"
    
    return mock_ai_service


@pytest.fixture
def mock_market_data_generator():
    """Generate realistic mock market data."""
    
    def generate_tick_data(symbol: str, base_price: float = 45000.0) -> Dict[str, Any]:
        import random
        
        price_change = random.uniform(-0.02, 0.02)  # ±2% change
        new_price = base_price * (1 + price_change)
        
        return {
            "symbol": symbol,
            "price": round(new_price, 2),
            "volume": random.uniform(100000, 2000000),
            "bid": round(new_price * 0.9999, 2),
            "ask": round(new_price * 1.0001, 2),
            "change_24h": random.uniform(-10, 10),
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def generate_candle_data(symbol: str, count: int = 100) -> List[Dict[str, Any]]:
        candles = []
        base_price = 45000.0
        
        for i in range(count):
            open_price = base_price
            close_price = open_price * (1 + random.uniform(-0.01, 0.01))
            high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.005))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.005))
            
            candles.append({
                "symbol": symbol,
                "timestamp": datetime.utcnow() - timedelta(minutes=i),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.uniform(1000, 10000)
            })
            
            base_price = close_price
        
        return candles
    
    return {
        "generate_tick": generate_tick_data,
        "generate_candles": generate_candle_data
    }


@pytest.fixture
async def mock_system_orchestrator():
    """Mock system orchestrator with all components."""
    
    # Mock agents
    mock_agents = {}
    agent_names = [
        "market_watcher", "strategy_researcher", "technical_analyst", 
        "risk_officer", "news_analyst", "trade_executor",
        "compliance_auditor", "chief_analyst", "portfolio_manager"
    ]
    
    for agent_name in agent_names:
        mock_agent = AsyncMock()
        mock_agent.agent_name = agent_name
        mock_agent.state = "ACTIVE"
        mock_agent.running = True
        mock_agent.initialize.return_value = None
        mock_agent.start.return_value = None
        mock_agent.stop.return_value = None
        mock_agents[agent_name] = mock_agent
    
    # Mock orchestrator
    mock_orchestrator = AsyncMock()
    mock_orchestrator.agents = mock_agents
    mock_orchestrator.running = False
    mock_orchestrator.message_queue = asyncio.Queue()
    mock_orchestrator.initialize.return_value = None
    mock_orchestrator.start.return_value = None
    mock_orchestrator.stop.return_value = None
    
    return mock_orchestrator


@pytest.fixture
def performance_monitor():
    """Performance monitoring utilities for tests."""
    
    class PerformanceMonitor:
        def __init__(self):
            self.metrics = {
                "response_times": [],
                "memory_usage": [],
                "cpu_usage": [],
                "error_counts": {},
                "throughput": []
            }
        
        def record_response_time(self, operation: str, duration: float):
            self.metrics["response_times"].append({
                "operation": operation,
                "duration": duration,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        def record_error(self, operation: str, error_type: str):
            if operation not in self.metrics["error_counts"]:
                self.metrics["error_counts"][operation] = {}
            if error_type not in self.metrics["error_counts"][operation]:
                self.metrics["error_counts"][operation][error_type] = 0
            self.metrics["error_counts"][operation][error_type] += 1
        
        def get_summary(self) -> Dict[str, Any]:
            response_times = [m["duration"] for m in self.metrics["response_times"]]
            
            return {
                "total_operations": len(response_times),
                "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "total_errors": sum(
                    sum(errors.values()) for errors in self.metrics["error_counts"].values()
                ),
                "error_rate": 0  # Calculate based on operations vs errors
            }
    
    return PerformanceMonitor()


@pytest.fixture
def test_data_factory():
    """Factory for generating test data."""
    
    class TestDataFactory:
        @staticmethod
        def create_portfolio_data(user_id: int = 1) -> Dict[str, Any]:
            return {
                "user_id": user_id,
                "name": f"Test Portfolio {user_id}",
                "initial_balance": 10000.0,
                "current_balance": 10000.0,
                "created_at": datetime.utcnow()
            }
        
        @staticmethod
        def create_order_data(portfolio_id: int = 1, symbol: str = "BTCUSDT") -> Dict[str, Any]:
            return {
                "portfolio_id": portfolio_id,
                "symbol": symbol,
                "order_type": "market",
                "side": "buy",
                "quantity": 0.1,
                "price": 45000.0,
                "status": "pending",
                "created_at": datetime.utcnow()
            }
        
        @staticmethod
        def create_trading_signal(symbol: str = "BTCUSDT") -> Dict[str, Any]:
            return {
                "symbol": symbol,
                "action": "BUY",
                "strength": 0.8,
                "confidence": 0.75,
                "price_target": 46000.0,
                "stop_loss": 44000.0,
                "strategy": "sma_crossover",
                "reasoning": "Strong bullish momentum detected",
                "created_at": datetime.utcnow()
            }
        
        @staticmethod
        def create_news_article(symbol: str = "BTCUSDT") -> Dict[str, Any]:
            return {
                "title": f"Breaking: {symbol} Shows Strong Performance",
                "content": "Market analysis shows positive trends...",
                "source": "CryptoNews",
                "published_at": datetime.utcnow(),
                "symbols": [symbol],
                "sentiment": "positive",
                "relevance_score": 0.8
            }
    
    return TestDataFactory()


@pytest.fixture
async def integration_test_environment(
    mock_database_stack,
    mock_ai_service_stack,
    mock_system_orchestrator,
    integration_config
):
    """Complete integration test environment setup."""
    
    # Patch all external dependencies
    patches = [
        patch('src.db.redis.get_client', return_value=mock_database_stack["redis"]),
        patch('src.db.clickhouse.get_client', return_value=mock_database_stack["clickhouse"]),
        patch('src.services.ai_service.ai_service', mock_ai_service_stack),
        patch('src.core.orchestrator.SystemOrchestrator', return_value=mock_system_orchestrator)
    ]
    
    # Start all patches
    for p in patches:
        p.start()
    
    yield {
        "database": mock_database_stack,
        "ai_service": mock_ai_service_stack,
        "orchestrator": mock_system_orchestrator,
        "config": integration_config
    }
    
    # Stop all patches
    for p in patches:
        p.stop()


@pytest.fixture
def temp_test_directory():
    """Create temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def test_logger():
    """Test logger for capturing test execution details."""
    import logging
    
    logger = logging.getLogger("integration_tests")
    logger.setLevel(logging.INFO)
    
    # Create handler if not exists
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


# Pytest configuration
def pytest_configure(config):
    """Configure pytest for integration tests."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection for integration tests."""
    for item in items:
        # Add integration marker to all tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add performance marker to performance tests
        if "performance" in item.name or "load" in item.name:
            item.add_marker(pytest.mark.performance)
        
        # Add slow marker to tests that might take longer
        if any(keyword in item.name for keyword in ["load", "stress", "sustained"]):
            item.add_marker(pytest.mark.slow)
