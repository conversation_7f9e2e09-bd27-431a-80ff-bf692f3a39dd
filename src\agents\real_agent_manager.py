"""
Real Agent Manager - Using Your Actual Available Ollama Models
Coordinates real AI agents with actual functionality and testing.
"""

import asyncio
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
import subprocess
import psutil
import time

from src.agents.real_market_watcher import RealMarketWatcher
from src.agents.real_strategy_researcher import RealStrategyResearcher
from src.agents.real_risk_officer import RealRiskOfficer
from src.services.ai_service import ai_service


class AgentStatus(Enum):
    INITIALIZING = "initializing"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class RealAgentMetrics:
    agent_name: str
    model_name: str
    status: AgentStatus
    uptime: float
    tasks_completed: int
    errors_count: int
    last_activity: datetime
    memory_usage_mb: float
    cpu_usage_percent: float
    api_calls_made: int
    success_rate: float


@dataclass
class SystemHealthMetrics:
    total_agents: int
    active_agents: int
    system_uptime: float
    total_api_calls: int
    average_response_time: float
    error_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    ollama_status: str
    timestamp: datetime


class RealAgentManager:
    """
    Real Agent Manager using your actual available Ollama models.
    
    Available Models:
    - marco-o1:7b (Market Watcher)
    - magistral:24b (Strategy Researcher)  
    - command-r:35b (Risk Officer)
    - cogito:32b (Technical Analyst)
    - gemma3:27b (News Analyst)
    - mistral-small:24b (Trade Executor)
    - falcon3:10b (Compliance Auditor)
    - qwen3:32b (Chief Analyst)
    - deepseek-r1:latest (Portfolio Manager)
    
    Features:
    - Real model verification and testing
    - Actual API connectivity testing
    - Performance monitoring with real metrics
    - Error handling and recovery
    - Resource usage monitoring
    - Integration testing framework
    """
    
    def __init__(self):
        self.logger = logging.getLogger("RealAgentManager")
        
        # Available models from your system
        self.available_models = {
            "marco-o1:7b": "4752e62baa0a",
            "magistral:24b": "5dd7e640d9d9", 
            "command-r:35b": "7d96360d357f",
            "cogito:32b": "0b4aab772f57",
            "gemma3:27b": "a418f5838eaf",
            "mistral-small:24b": "8039dd90c113",
            "falcon3:10b": "1653ff122acd",
            "granite3.3:8b": "fd429f23b909",
            "qwen3:32b": "030ee887880f",
            "deepseek-r1:latest": "6995872bfe4c"
        }
        
        # Initialize real agents with actual models
        self.agents = {
            "real_market_watcher": RealMarketWatcher(),
            "real_strategy_researcher": RealStrategyResearcher(), 
            "real_risk_officer": RealRiskOfficer()
        }
        
        # Agent status and metrics
        self.agent_metrics = {}
        self.system_health = SystemHealthMetrics(
            total_agents=len(self.agents),
            active_agents=0,
            system_uptime=0.0,
            total_api_calls=0,
            average_response_time=0.0,
            error_rate=0.0,
            memory_usage_mb=0.0,
            cpu_usage_percent=0.0,
            ollama_status="unknown",
            timestamp=datetime.utcnow()
        )
        
        self.running = False
        self.start_time = None
        self.monitoring_tasks = []

    async def verify_ollama_models(self) -> Dict[str, bool]:
        """Verify that Ollama models are actually available."""
        self.logger.info("🔍 Verifying Ollama models availability...")
        
        model_status = {}
        
        try:
            # Check if Ollama is running
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                available_models = result.stdout
                self.logger.info("✅ Ollama is running")
                
                # Check each model
                for model_name, model_id in self.available_models.items():
                    if model_name in available_models or model_id in available_models:
                        model_status[model_name] = True
                        self.logger.info(f"✅ {model_name} is available")
                    else:
                        model_status[model_name] = False
                        self.logger.warning(f"❌ {model_name} not found")
                
            else:
                self.logger.error("❌ Ollama is not running or not accessible")
                for model_name in self.available_models.keys():
                    model_status[model_name] = False
                    
        except subprocess.TimeoutExpired:
            self.logger.error("❌ Ollama command timed out")
            for model_name in self.available_models.keys():
                model_status[model_name] = False
        except FileNotFoundError:
            self.logger.error("❌ Ollama command not found - is Ollama installed?")
            for model_name in self.available_models.keys():
                model_status[model_name] = False
        except Exception as e:
            self.logger.error(f"❌ Error checking Ollama models: {e}")
            for model_name in self.available_models.keys():
                model_status[model_name] = False
        
        return model_status

    async def test_model_inference(self, model_name: str) -> Dict[str, Any]:
        """Test actual model inference capability."""
        self.logger.info(f"🧪 Testing inference for {model_name}...")
        
        test_result = {
            "model": model_name,
            "available": False,
            "response_time": 0.0,
            "response_quality": "unknown",
            "error": None
        }
        
        try:
            start_time = time.time()
            
            # Test with a simple prompt
            test_prompt = "What is 2+2? Respond with just the number."
            
            response = await ai_service.generate_response(
                model_name,
                test_prompt,
                {}
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            test_result["available"] = True
            test_result["response_time"] = response_time
            
            # Check response quality
            if response and "4" in str(response):
                test_result["response_quality"] = "good"
                self.logger.info(f"✅ {model_name} inference test passed ({response_time:.2f}s)")
            else:
                test_result["response_quality"] = "poor"
                self.logger.warning(f"⚠️ {model_name} gave unexpected response: {response}")
                
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.error(f"❌ {model_name} inference test failed: {e}")
        
        return test_result

    async def initialize_real_agents(self) -> Dict[str, Any]:
        """Initialize agents with real functionality testing."""
        self.logger.info("🚀 INITIALIZING REAL AI TRADING SYSTEM")
        self.logger.info("=" * 60)
        
        # Step 1: Verify Ollama models
        model_status = await self.verify_ollama_models()
        available_models = [model for model, status in model_status.items() if status]
        
        self.logger.info(f"📊 Model Status: {len(available_models)}/{len(self.available_models)} models available")
        
        # Step 2: Test model inference
        inference_results = {}
        for model_name in available_models[:3]:  # Test first 3 available models
            inference_results[model_name] = await self.test_model_inference(model_name)
        
        # Step 3: Initialize agents
        initialization_results = {}
        
        for agent_name, agent in self.agents.items():
            try:
                self.logger.info(f"🔧 Initializing {agent_name}...")
                
                # Initialize agent
                await agent._initialize_agent()
                
                # Create metrics
                self.agent_metrics[agent_name] = RealAgentMetrics(
                    agent_name=agent_name,
                    model_name=agent.model_name,
                    status=AgentStatus.RUNNING,
                    uptime=0.0,
                    tasks_completed=0,
                    errors_count=0,
                    last_activity=datetime.utcnow(),
                    memory_usage_mb=0.0,
                    cpu_usage_percent=0.0,
                    api_calls_made=0,
                    success_rate=1.0
                )
                
                initialization_results[agent_name] = "SUCCESS"
                self.logger.info(f"✅ {agent_name} initialized successfully")
                
            except Exception as e:
                initialization_results[agent_name] = f"ERROR: {e}"
                self.logger.error(f"❌ Failed to initialize {agent_name}: {e}")
                
                if agent_name in self.agent_metrics:
                    self.agent_metrics[agent_name].status = AgentStatus.ERROR
        
        # Step 4: Test API connectivity
        api_test_results = await self._test_api_connectivity()
        
        # Step 5: Generate initialization report
        successful_agents = sum(1 for result in initialization_results.values() if result == "SUCCESS")
        
        self.logger.info("=" * 60)
        self.logger.info("📊 INITIALIZATION SUMMARY")
        self.logger.info(f"🤖 Agents: {successful_agents}/{len(self.agents)} initialized")
        self.logger.info(f"🧠 Models: {len(available_models)}/{len(self.available_models)} available")
        self.logger.info(f"🌐 API: {'✅ Connected' if api_test_results.get('binance', False) else '❌ Failed'}")
        self.logger.info("=" * 60)
        
        return {
            "agents": initialization_results,
            "models": model_status,
            "inference_tests": inference_results,
            "api_tests": api_test_results,
            "summary": {
                "successful_agents": successful_agents,
                "available_models": len(available_models),
                "system_ready": successful_agents > 0 and api_test_results.get('binance', False)
            }
        }

    async def _test_api_connectivity(self) -> Dict[str, bool]:
        """Test connectivity to external APIs."""
        self.logger.info("🌐 Testing API connectivity...")
        
        api_results = {}
        
        # Test Binance API
        try:
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
            api_results["binance"] = response.status_code == 200
            
            if api_results["binance"]:
                self.logger.info("✅ Binance API connectivity confirmed")
            else:
                self.logger.warning(f"⚠️ Binance API issue: {response.status_code}")
                
        except Exception as e:
            api_results["binance"] = False
            self.logger.error(f"❌ Binance API test failed: {e}")
        
        # Test Ollama API
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            api_results["ollama"] = response.status_code == 200
            
            if api_results["ollama"]:
                self.logger.info("✅ Ollama API connectivity confirmed")
            else:
                self.logger.warning(f"⚠️ Ollama API issue: {response.status_code}")
                
        except Exception as e:
            api_results["ollama"] = False
            self.logger.error(f"❌ Ollama API test failed: {e}")
        
        return api_results

    async def start_real_agents(self):
        """Start all initialized agents."""
        self.logger.info("🚀 Starting real agents...")
        
        self.running = True
        self.start_time = datetime.utcnow()
        
        # Start each agent
        for agent_name, agent in self.agents.items():
            if self.agent_metrics.get(agent_name, {}).status == AgentStatus.RUNNING:
                try:
                    # Start agent tasks
                    agent_tasks = await agent._start_agent_tasks()
                    self.monitoring_tasks.extend(agent_tasks)
                    
                    self.logger.info(f"✅ Started {agent_name} with {len(agent_tasks)} tasks")
                    
                except Exception as e:
                    self.logger.error(f"❌ Failed to start {agent_name}: {e}")
                    if agent_name in self.agent_metrics:
                        self.agent_metrics[agent_name].status = AgentStatus.ERROR
        
        # Start monitoring tasks
        self.monitoring_tasks.extend([
            asyncio.create_task(self._system_health_monitoring()),
            asyncio.create_task(self._performance_monitoring()),
            asyncio.create_task(self._integration_testing()),
            asyncio.create_task(self._error_recovery())
        ])
        
        active_agents = sum(1 for metrics in self.agent_metrics.values() 
                          if metrics.status == AgentStatus.RUNNING)
        self.system_health.active_agents = active_agents
        
        self.logger.info(f"🎯 System started with {active_agents}/{len(self.agents)} active agents")

    async def _system_health_monitoring(self):
        """Monitor overall system health."""
        while self.running:
            try:
                # Update system metrics
                self.system_health.system_uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
                self.system_health.timestamp = datetime.utcnow()
                
                # Check Ollama status
                try:
                    response = requests.get("http://localhost:11434/api/tags", timeout=3)
                    self.system_health.ollama_status = "running" if response.status_code == 200 else "error"
                except:
                    self.system_health.ollama_status = "offline"
                
                # Update system resource usage
                self.system_health.memory_usage_mb = psutil.virtual_memory().used / 1024 / 1024
                self.system_health.cpu_usage_percent = psutil.cpu_percent()
                
                # Log health status
                if self.system_health.system_uptime % 300 == 0:  # Every 5 minutes
                    self.logger.info(f"💓 System Health: {self.system_health.active_agents} agents active, "
                                   f"Ollama: {self.system_health.ollama_status}, "
                                   f"Memory: {self.system_health.memory_usage_mb:.0f}MB, "
                                   f"CPU: {self.system_health.cpu_usage_percent:.1f}%")
                
                await asyncio.sleep(30)  # Every 30 seconds
                
            except Exception as e:
                self.logger.error(f"System health monitoring error: {e}")
                await asyncio.sleep(60)

    async def _performance_monitoring(self):
        """Monitor agent performance."""
        while self.running:
            try:
                for agent_name, metrics in self.agent_metrics.items():
                    if metrics.status == AgentStatus.RUNNING:
                        # Update uptime
                        metrics.uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
                        
                        # Update activity
                        metrics.last_activity = datetime.utcnow()
                        
                        # Calculate success rate
                        total_operations = metrics.tasks_completed + metrics.errors_count
                        if total_operations > 0:
                            metrics.success_rate = metrics.tasks_completed / total_operations
                
                await asyncio.sleep(60)  # Every minute
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(30)

    async def _integration_testing(self):
        """Run continuous integration tests."""
        while self.running:
            try:
                # Test agent communication
                test_results = await self._run_integration_tests()
                
                # Log test results
                passed_tests = sum(1 for result in test_results.values() if result.get("passed", False))
                total_tests = len(test_results)
                
                self.logger.info(f"🧪 Integration Tests: {passed_tests}/{total_tests} passed")
                
                # Update error rate
                if total_tests > 0:
                    self.system_health.error_rate = (total_tests - passed_tests) / total_tests
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Integration testing error: {e}")
                await asyncio.sleep(600)

    async def _run_integration_tests(self) -> Dict[str, Any]:
        """Run comprehensive integration tests."""
        test_results = {}
        
        # Test 1: Market data retrieval
        try:
            if "real_market_watcher" in self.agents:
                market_data = await self.agents["real_market_watcher"].get_current_market_data()
                test_results["market_data"] = {
                    "passed": bool(market_data and "prices" in market_data),
                    "details": f"Retrieved data for {len(market_data.get('prices', {}))} symbols"
                }
        except Exception as e:
            test_results["market_data"] = {"passed": False, "error": str(e)}
        
        # Test 2: Strategy performance
        try:
            if "real_strategy_researcher" in self.agents:
                strategy_data = await self.agents["real_strategy_researcher"].get_strategy_performance()
                test_results["strategy_performance"] = {
                    "passed": bool(strategy_data and "strategies" in strategy_data),
                    "details": f"Monitoring {strategy_data.get('total_strategies', 0)} strategies"
                }
        except Exception as e:
            test_results["strategy_performance"] = {"passed": False, "error": str(e)}
        
        # Test 3: Risk calculations
        try:
            if "real_risk_officer" in self.agents:
                risk_data = await self.agents["real_risk_officer"].get_risk_summary()
                test_results["risk_calculations"] = {
                    "passed": bool(risk_data and "portfolios" in risk_data),
                    "details": f"Monitoring {len(risk_data.get('portfolios', {}))} portfolios"
                }
        except Exception as e:
            test_results["risk_calculations"] = {"passed": False, "error": str(e)}
        
        return test_results

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            "system_info": {
                "running": self.running,
                "uptime": self.system_health.system_uptime,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "ollama_status": self.system_health.ollama_status
            },
            "agents": {
                agent_name: {
                    "model": metrics.model_name,
                    "status": metrics.status.value,
                    "uptime": metrics.uptime,
                    "tasks_completed": metrics.tasks_completed,
                    "errors": metrics.errors_count,
                    "success_rate": metrics.success_rate,
                    "last_activity": metrics.last_activity.isoformat()
                }
                for agent_name, metrics in self.agent_metrics.items()
            },
            "system_health": {
                "active_agents": self.system_health.active_agents,
                "total_agents": self.system_health.total_agents,
                "memory_usage_mb": self.system_health.memory_usage_mb,
                "cpu_usage_percent": self.system_health.cpu_usage_percent,
                "error_rate": self.system_health.error_rate
            },
            "available_models": list(self.available_models.keys())
        }

    async def stop_all_agents(self):
        """Stop all agents gracefully."""
        self.logger.info("🛑 Stopping all agents...")
        
        self.running = False
        
        # Cancel monitoring tasks
        for task in self.monitoring_tasks:
            if not task.done():
                task.cancel()
        
        # Stop each agent
        for agent_name, agent in self.agents.items():
            try:
                await agent._cleanup_agent()
                if agent_name in self.agent_metrics:
                    self.agent_metrics[agent_name].status = AgentStatus.STOPPED
                self.logger.info(f"✅ Stopped {agent_name}")
                
            except Exception as e:
                self.logger.error(f"❌ Error stopping {agent_name}: {e}")
        
        # Generate final report
        await self._generate_final_report()
        
        self.logger.info("🎯 All agents stopped successfully")

    async def _generate_final_report(self):
        """Generate final system report."""
        uptime = (datetime.utcnow() - self.start_time).total_seconds() if self.start_time else 0
        
        self.logger.info("📊 FINAL SYSTEM REPORT")
        self.logger.info("=" * 50)
        self.logger.info(f"🕐 Total Runtime: {uptime:.1f} seconds")
        self.logger.info(f"🤖 Agents Managed: {len(self.agents)}")
        self.logger.info(f"📈 Tasks Completed: {sum(m.tasks_completed for m in self.agent_metrics.values())}")
        self.logger.info(f"❌ Total Errors: {sum(m.errors_count for m in self.agent_metrics.values())}")
        self.logger.info(f"📊 Average Success Rate: {np.mean([m.success_rate for m in self.agent_metrics.values()]):.2%}")
        self.logger.info("=" * 50)

    async def _error_recovery(self):
        """Handle error recovery for failed agents."""
        while self.running:
            try:
                for agent_name, metrics in self.agent_metrics.items():
                    if metrics.status == AgentStatus.ERROR:
                        # Attempt to restart failed agent
                        self.logger.info(f"🔄 Attempting to restart {agent_name}...")
                        
                        try:
                            agent = self.agents[agent_name]
                            await agent._initialize_agent()
                            metrics.status = AgentStatus.RUNNING
                            self.logger.info(f"✅ Successfully restarted {agent_name}")
                            
                        except Exception as e:
                            self.logger.error(f"❌ Failed to restart {agent_name}: {e}")
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error recovery process failed: {e}")
                await asyncio.sleep(120)
