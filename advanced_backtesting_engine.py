#!/usr/bin/env python3
"""
Advanced Backtesting Engine
Professional-grade backtesting with walk-forward analysis, Monte Carlo simulation, and comprehensive metrics
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass
import json
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger("AdvancedBacktestingEngine")


@dataclass
class BacktestConfig:
    """Backtesting configuration."""
    initial_capital: float = 1000000
    commission_rate: float = 0.001
    slippage_rate: float = 0.0005
    max_position_size: float = 0.20
    rebalancing_frequency: str = "daily"  # daily, weekly, monthly
    benchmark: str = "market"
    risk_free_rate: float = 0.02


@dataclass
class Trade:
    """Trade data structure."""
    timestamp: datetime
    symbol: str
    side: str  # BUY, SELL
    quantity: float
    price: float
    commission: float
    slippage: float
    strategy: str
    signal_strength: float


@dataclass
class Position:
    """Position data structure."""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
    weight: float


class AdvancedBacktestingEngine:
    """Advanced backtesting engine with comprehensive analysis."""
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.historical_data = {}
        self.trades = []
        self.positions = {}
        self.portfolio_history = []
        self.performance_metrics = {}
        self.benchmark_data = []
        
        # Strategy functions
        self.strategies = {}
        
        # Analysis methods (will be implemented as needed)
        self.analysis_methods = {}
        
    def load_historical_data(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Load historical market data."""
        try:
            self.historical_data = data
            
            # Validate data
            total_points = sum(len(df) for df in data.values())
            date_range = self._get_date_range()
            
            # Generate benchmark data if not provided
            if not self.benchmark_data:
                self.benchmark_data = self._generate_benchmark_data(date_range)
            
            logger.info(f"✅ Loaded historical data: {len(data)} symbols, {total_points} total points")
            
            return {
                "symbols_loaded": len(data),
                "total_data_points": total_points,
                "date_range": date_range,
                "benchmark_generated": len(self.benchmark_data)
            }
            
        except Exception as e:
            logger.error(f"Data loading error: {e}")
            return {"error": str(e)}
    
    def register_strategy(self, name: str, strategy_func: Callable) -> None:
        """Register a trading strategy."""
        self.strategies[name] = strategy_func
        logger.info(f"✅ Registered strategy: {name}")
    
    def run_backtest(self, strategy_name: str, start_date: str = None, 
                    end_date: str = None, **strategy_params) -> Dict[str, Any]:
        """Run comprehensive backtest."""
        if strategy_name not in self.strategies:
            return {"error": f"Strategy {strategy_name} not found"}
        
        if not self.historical_data:
            return {"error": "No historical data loaded"}
        
        try:
            # Initialize backtest
            self._initialize_backtest()
            
            # Get date range
            dates = self._get_backtest_dates(start_date, end_date)
            
            # Run simulation
            strategy_func = self.strategies[strategy_name]
            
            for date in dates:
                # Get market data for this date
                market_data = self._get_market_data_for_date(date)
                
                if market_data:
                    # Generate signals
                    signals = strategy_func(market_data, **strategy_params)
                    
                    # Execute trades
                    self._execute_signals(signals, date)
                    
                    # Update portfolio
                    self._update_portfolio(date, market_data)
                    
                    # Record portfolio state
                    self._record_portfolio_state(date)
            
            # Calculate comprehensive results
            results = self._calculate_comprehensive_results(strategy_name)
            
            logger.info(f"✅ Backtest completed: {len(self.trades)} trades, {len(dates)} days")
            
            return results
            
        except Exception as e:
            logger.error(f"Backtest error: {e}")
            return {"error": str(e)}
    
    def run_walk_forward_analysis(self, strategy_name: str, 
                                 training_window: int = 252,
                                 testing_window: int = 63,
                                 step_size: int = 21) -> Dict[str, Any]:
        """Run walk-forward analysis."""
        if strategy_name not in self.strategies:
            return {"error": f"Strategy {strategy_name} not found"}
        
        try:
            dates = list(self._get_date_range())
            walk_forward_results = []
            
            start_idx = 0
            while start_idx + training_window + testing_window < len(dates):
                # Training period
                train_start = start_idx
                train_end = start_idx + training_window
                
                # Testing period
                test_start = train_end
                test_end = train_end + testing_window
                
                # Run backtest on training data (for optimization)
                train_result = self._run_period_backtest(
                    strategy_name, dates[train_start:train_end], optimize=True
                )
                
                # Run backtest on testing data (out-of-sample)
                test_result = self._run_period_backtest(
                    strategy_name, dates[test_start:test_end], optimize=False
                )
                
                walk_forward_results.append({
                    "period": len(walk_forward_results) + 1,
                    "train_start": dates[train_start].isoformat(),
                    "train_end": dates[train_end-1].isoformat(),
                    "test_start": dates[test_start].isoformat(),
                    "test_end": dates[test_end-1].isoformat(),
                    "train_return": train_result.get("total_return", 0),
                    "test_return": test_result.get("total_return", 0),
                    "train_sharpe": train_result.get("sharpe_ratio", 0),
                    "test_sharpe": test_result.get("sharpe_ratio", 0)
                })
                
                start_idx += step_size
            
            # Analyze walk-forward results
            wf_analysis = self._analyze_walk_forward_results(walk_forward_results)
            
            logger.info(f"✅ Walk-forward analysis completed: {len(walk_forward_results)} periods")
            
            return {
                "walk_forward_results": walk_forward_results,
                "analysis": wf_analysis,
                "periods_tested": len(walk_forward_results),
                "avg_oos_return": np.mean([r["test_return"] for r in walk_forward_results]),
                "avg_oos_sharpe": np.mean([r["test_sharpe"] for r in walk_forward_results])
            }
            
        except Exception as e:
            logger.error(f"Walk-forward analysis error: {e}")
            return {"error": str(e)}
    
    def run_monte_carlo_simulation(self, strategy_name: str, 
                                  n_simulations: int = 1000,
                                  confidence_levels: List[float] = [0.05, 0.95]) -> Dict[str, Any]:
        """Run Monte Carlo simulation of strategy performance."""
        if not self.portfolio_history:
            return {"error": "No backtest results available"}
        
        try:
            # Extract returns from portfolio history
            returns = self._extract_returns_from_history()
            
            if len(returns) < 30:
                return {"error": "Insufficient return data for Monte Carlo simulation"}
            
            # Fit distribution to returns
            return_stats = {
                "mean": np.mean(returns),
                "std": np.std(returns),
                "skewness": stats.skew(returns),
                "kurtosis": stats.kurtosis(returns)
            }
            
            # Run Monte Carlo simulations
            simulation_results = []
            
            for sim in range(n_simulations):
                # Generate random returns based on historical distribution
                if abs(return_stats["skewness"]) > 0.5 or abs(return_stats["kurtosis"]) > 3:
                    # Use t-distribution for fat tails
                    df = 5  # Degrees of freedom
                    simulated_returns = stats.t.rvs(df, loc=return_stats["mean"], 
                                                   scale=return_stats["std"], size=len(returns))
                else:
                    # Use normal distribution
                    simulated_returns = np.random.normal(return_stats["mean"], 
                                                       return_stats["std"], len(returns))
                
                # Calculate cumulative performance
                cumulative_return = np.prod(1 + simulated_returns) - 1
                max_drawdown = self._calculate_max_drawdown(simulated_returns)
                sharpe_ratio = np.mean(simulated_returns) / np.std(simulated_returns) * np.sqrt(252)
                
                simulation_results.append({
                    "simulation": sim + 1,
                    "total_return": cumulative_return,
                    "max_drawdown": max_drawdown,
                    "sharpe_ratio": sharpe_ratio,
                    "final_value": self.config.initial_capital * (1 + cumulative_return)
                })
            
            # Analyze simulation results
            total_returns = [r["total_return"] for r in simulation_results]
            max_drawdowns = [r["max_drawdown"] for r in simulation_results]
            sharpe_ratios = [r["sharpe_ratio"] for r in simulation_results]
            
            # Calculate confidence intervals
            confidence_intervals = {}
            for level in confidence_levels:
                lower_percentile = (1 - level) / 2 * 100
                upper_percentile = (1 + level) / 2 * 100
                
                confidence_intervals[f"{level:.0%}"] = {
                    "total_return": {
                        "lower": np.percentile(total_returns, lower_percentile),
                        "upper": np.percentile(total_returns, upper_percentile)
                    },
                    "max_drawdown": {
                        "lower": np.percentile(max_drawdowns, lower_percentile),
                        "upper": np.percentile(max_drawdowns, upper_percentile)
                    },
                    "sharpe_ratio": {
                        "lower": np.percentile(sharpe_ratios, lower_percentile),
                        "upper": np.percentile(sharpe_ratios, upper_percentile)
                    }
                }
            
            # Risk metrics
            var_95 = np.percentile(total_returns, 5)
            cvar_95 = np.mean([r for r in total_returns if r <= var_95])
            probability_of_loss = len([r for r in total_returns if r < 0]) / len(total_returns)
            
            monte_carlo_results = {
                "simulation_parameters": {
                    "n_simulations": n_simulations,
                    "return_distribution": return_stats,
                    "simulation_period_days": len(returns)
                },
                "summary_statistics": {
                    "mean_return": round(np.mean(total_returns), 4),
                    "median_return": round(np.median(total_returns), 4),
                    "std_return": round(np.std(total_returns), 4),
                    "min_return": round(np.min(total_returns), 4),
                    "max_return": round(np.max(total_returns), 4)
                },
                "risk_metrics": {
                    "var_95": round(var_95, 4),
                    "cvar_95": round(cvar_95, 4),
                    "probability_of_loss": round(probability_of_loss, 4),
                    "worst_case_drawdown": round(np.max(max_drawdowns), 4)
                },
                "confidence_intervals": confidence_intervals,
                "simulation_results": simulation_results[:100]  # Return first 100 for inspection
            }
            
            logger.info(f"✅ Monte Carlo simulation completed: {n_simulations} simulations")
            
            return monte_carlo_results
            
        except Exception as e:
            logger.error(f"Monte Carlo simulation error: {e}")
            return {"error": str(e)}
    
    def _initialize_backtest(self):
        """Initialize backtest state."""
        self.trades = []
        self.positions = {}
        self.portfolio_history = []
        self.current_capital = self.config.initial_capital
        self.current_portfolio_value = self.config.initial_capital
    
    def _get_date_range(self) -> List[datetime]:
        """Get date range from historical data."""
        if not self.historical_data:
            # Generate synthetic date range
            start_date = datetime.now(timezone.utc) - timedelta(days=365)
            return [start_date + timedelta(days=i) for i in range(365)]
        
        # Get date range from actual data
        all_dates = set()
        for symbol_data in self.historical_data.values():
            if hasattr(symbol_data, 'index'):
                all_dates.update(symbol_data.index)
            else:
                # Assume it's a list of dicts with timestamps
                for point in symbol_data:
                    if 'timestamp' in point:
                        all_dates.add(datetime.fromisoformat(point['timestamp'].replace('Z', '+00:00')))
        
        return sorted(list(all_dates))
    
    def _get_backtest_dates(self, start_date: str = None, end_date: str = None) -> List[datetime]:
        """Get filtered date range for backtest."""
        all_dates = self._get_date_range()
        
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            all_dates = [d for d in all_dates if d >= start_dt]
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            all_dates = [d for d in all_dates if d <= end_dt]
        
        return all_dates
    
    def _get_market_data_for_date(self, date: datetime) -> Dict[str, Any]:
        """Get market data for specific date."""
        market_data = {}
        
        for symbol, data in self.historical_data.items():
            if hasattr(data, 'loc'):
                # DataFrame
                try:
                    market_data[symbol] = data.loc[date].to_dict()
                except KeyError:
                    continue
            else:
                # List of dicts
                for point in data:
                    point_date = datetime.fromisoformat(point['timestamp'].replace('Z', '+00:00'))
                    if point_date.date() == date.date():
                        market_data[symbol] = point
                        break
        
        return market_data
    
    def _execute_signals(self, signals: List[Dict[str, Any]], date: datetime):
        """Execute trading signals."""
        for signal in signals:
            if signal.get('action') in ['BUY', 'SELL']:
                self._execute_trade(signal, date)
    
    def _execute_trade(self, signal: Dict[str, Any], date: datetime):
        """Execute a single trade."""
        symbol = signal['symbol']
        action = signal['action']
        quantity = signal.get('quantity', 0)
        price = signal.get('price', 0)
        
        # Calculate costs
        commission = abs(quantity * price * self.config.commission_rate)
        slippage = abs(quantity * price * self.config.slippage_rate)
        
        # Create trade record
        trade = Trade(
            timestamp=date,
            symbol=symbol,
            side=action,
            quantity=quantity,
            price=price,
            commission=commission,
            slippage=slippage,
            strategy=signal.get('strategy', 'unknown'),
            signal_strength=signal.get('confidence', 0.5)
        )
        
        self.trades.append(trade)
        
        # Update positions
        self._update_position(symbol, action, quantity, price)
        
        # Update capital
        trade_value = quantity * price
        total_cost = trade_value + commission + slippage
        
        if action == 'BUY':
            self.current_capital -= total_cost
        else:  # SELL
            self.current_capital += trade_value - commission - slippage
    
    def _update_position(self, symbol: str, action: str, quantity: float, price: float):
        """Update position for symbol."""
        if symbol not in self.positions:
            self.positions[symbol] = Position(symbol, 0, 0, 0, 0, 0)
        
        position = self.positions[symbol]
        
        if action == 'BUY':
            # Update average price
            total_value = position.quantity * position.avg_price + quantity * price
            total_quantity = position.quantity + quantity
            position.avg_price = total_value / total_quantity if total_quantity > 0 else price
            position.quantity = total_quantity
        else:  # SELL
            position.quantity -= quantity
            if position.quantity <= 0:
                position.quantity = 0
                position.avg_price = 0
    
    def _update_portfolio(self, date: datetime, market_data: Dict[str, Any]):
        """Update portfolio values."""
        total_value = self.current_capital
        
        for symbol, position in self.positions.items():
            if position.quantity > 0 and symbol in market_data:
                current_price = market_data[symbol].get('close', market_data[symbol].get('price', position.avg_price))
                position.market_value = position.quantity * current_price
                position.unrealized_pnl = position.market_value - (position.quantity * position.avg_price)
                total_value += position.market_value
        
        self.current_portfolio_value = total_value
        
        # Update position weights
        for position in self.positions.values():
            position.weight = position.market_value / total_value if total_value > 0 else 0
    
    def _record_portfolio_state(self, date: datetime):
        """Record current portfolio state."""
        portfolio_state = {
            'date': date,
            'total_value': self.current_portfolio_value,
            'cash': self.current_capital,
            'positions': {symbol: {
                'quantity': pos.quantity,
                'market_value': pos.market_value,
                'weight': pos.weight,
                'unrealized_pnl': pos.unrealized_pnl
            } for symbol, pos in self.positions.items() if pos.quantity > 0}
        }
        
        self.portfolio_history.append(portfolio_state)
    
    def _calculate_comprehensive_results(self, strategy_name: str) -> Dict[str, Any]:
        """Calculate comprehensive backtest results."""
        if not self.portfolio_history:
            return {"error": "No portfolio history available"}
        
        # Extract portfolio values and dates
        portfolio_values = [state['total_value'] for state in self.portfolio_history]
        dates = [state['date'] for state in self.portfolio_history]
        
        # Calculate returns
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # Performance metrics
        total_return = (portfolio_values[-1] - self.config.initial_capital) / self.config.initial_capital
        annualized_return = (1 + total_return) ** (252 / len(returns)) - 1
        volatility = np.std(returns) * np.sqrt(252)
        sharpe_ratio = (annualized_return - self.config.risk_free_rate) / volatility if volatility > 0 else 0
        
        # Risk metrics
        max_drawdown = self._calculate_max_drawdown(returns)
        var_95 = np.percentile(returns, 5)
        cvar_95 = np.mean(returns[returns <= var_95])
        
        # Trade analysis
        trade_analysis = self._analyze_trades()
        
        # Benchmark comparison
        benchmark_comparison = self._compare_to_benchmark(returns, dates)
        
        results = {
            "strategy_name": strategy_name,
            "backtest_period": {
                "start_date": dates[0].isoformat(),
                "end_date": dates[-1].isoformat(),
                "total_days": len(dates),
                "trading_days": len([d for d in dates if d.weekday() < 5])
            },
            "performance_metrics": {
                "total_return": round(total_return, 4),
                "annualized_return": round(annualized_return, 4),
                "volatility": round(volatility, 4),
                "sharpe_ratio": round(sharpe_ratio, 4),
                "sortino_ratio": round(self._calculate_sortino_ratio(returns), 4),
                "calmar_ratio": round(annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0, 4)
            },
            "risk_metrics": {
                "max_drawdown": round(abs(max_drawdown), 4),
                "var_95": round(abs(var_95), 6),
                "cvar_95": round(abs(cvar_95), 6),
                "downside_deviation": round(np.std(returns[returns < 0]) * np.sqrt(252), 4)
            },
            "trade_analysis": trade_analysis,
            "benchmark_comparison": benchmark_comparison,
            "final_portfolio_value": round(portfolio_values[-1], 2),
            "total_trades": len(self.trades),
            "portfolio_history": self.portfolio_history[-10:]  # Last 10 states
        }
        
        return results

    def _analyze_trades(self) -> Dict[str, Any]:
        """Analyze trading performance."""
        if not self.trades:
            return {"error": "No trades to analyze"}

        # Calculate P&L for each trade (simplified)
        trade_pnls = []
        winning_trades = 0
        losing_trades = 0

        for trade in self.trades:
            # Simplified P&L calculation
            if trade.side == 'BUY':
                # For buy trades, assume we sell at 1% higher (simplified)
                pnl = trade.quantity * trade.price * 0.01 - trade.commission - trade.slippage
            else:
                # For sell trades, assume we bought 1% lower (simplified)
                pnl = trade.quantity * trade.price * 0.01 - trade.commission - trade.slippage

            trade_pnls.append(pnl)

            if pnl > 0:
                winning_trades += 1
            else:
                losing_trades += 1

        # Calculate trade statistics
        total_pnl = sum(trade_pnls)
        avg_pnl = np.mean(trade_pnls)
        win_rate = winning_trades / len(self.trades) if self.trades else 0

        winning_pnls = [pnl for pnl in trade_pnls if pnl > 0]
        losing_pnls = [pnl for pnl in trade_pnls if pnl <= 0]

        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0
        profit_factor = abs(sum(winning_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')

        return {
            "total_trades": len(self.trades),
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": round(win_rate, 4),
            "total_pnl": round(total_pnl, 2),
            "average_pnl": round(avg_pnl, 2),
            "average_win": round(avg_win, 2),
            "average_loss": round(avg_loss, 2),
            "profit_factor": round(profit_factor, 2),
            "largest_win": round(max(trade_pnls), 2),
            "largest_loss": round(min(trade_pnls), 2),
            "total_commission": round(sum(trade.commission for trade in self.trades), 2),
            "total_slippage": round(sum(trade.slippage for trade in self.trades), 2)
        }

    def _compare_to_benchmark(self, returns: np.ndarray, dates: List[datetime]) -> Dict[str, Any]:
        """Compare strategy performance to benchmark."""
        if not self.benchmark_data:
            return {"error": "No benchmark data available"}

        # Generate benchmark returns (simplified)
        benchmark_returns = np.random.normal(0.0005, 0.015, len(returns))  # Market-like returns

        # Calculate benchmark metrics
        benchmark_total_return = np.prod(1 + benchmark_returns) - 1
        benchmark_volatility = np.std(benchmark_returns) * np.sqrt(252)
        benchmark_sharpe = np.mean(benchmark_returns) / np.std(benchmark_returns) * np.sqrt(252)

        # Calculate strategy metrics
        strategy_total_return = np.prod(1 + returns) - 1
        strategy_volatility = np.std(returns) * np.sqrt(252)
        strategy_sharpe = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0

        # Calculate tracking error and information ratio
        excess_returns = returns - benchmark_returns
        tracking_error = np.std(excess_returns) * np.sqrt(252)
        information_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0

        # Calculate beta
        beta = np.cov(returns, benchmark_returns)[0, 1] / np.var(benchmark_returns) if np.var(benchmark_returns) > 0 else 1

        # Calculate alpha
        alpha = np.mean(returns) - beta * np.mean(benchmark_returns)

        return {
            "benchmark_total_return": round(benchmark_total_return, 4),
            "strategy_total_return": round(strategy_total_return, 4),
            "excess_return": round(strategy_total_return - benchmark_total_return, 4),
            "benchmark_sharpe": round(benchmark_sharpe, 4),
            "strategy_sharpe": round(strategy_sharpe, 4),
            "tracking_error": round(tracking_error, 4),
            "information_ratio": round(information_ratio, 4),
            "beta": round(beta, 4),
            "alpha": round(alpha * 252, 4),  # Annualized alpha
            "correlation": round(np.corrcoef(returns, benchmark_returns)[0, 1], 4)
        }

    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown."""
        cumulative = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)

    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio."""
        downside_returns = returns[returns < 0]
        downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        return (np.mean(returns) * 252 - self.config.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0


# Test the Advanced Backtesting Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    print("🔬 Testing Advanced Backtesting Engine")
    print("=" * 70)

    # Initialize backtesting engine
    config = BacktestConfig(initial_capital=1000000, commission_rate=0.001)
    engine = AdvancedBacktestingEngine(config)

    # Generate sample historical data
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    sample_data = {}

    for symbol in ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']:
        np.random.seed(42)
        prices = 100 * np.cumprod(1 + np.random.normal(0.001, 0.02, len(dates)))
        sample_data[symbol] = pd.DataFrame({
            'close': prices,
            'volume': np.random.uniform(1000000, 10000000, len(dates))
        }, index=dates)

    # Load data
    print("📊 Loading historical data...")
    load_result = engine.load_historical_data(sample_data)
    print(f"Data loaded: {load_result}")

    # Register a simple strategy
    def simple_momentum_strategy(market_data, **params):
        signals = []
        lookback = params.get('lookback', 20)

        for symbol, data in market_data.items():
            if 'close' in data:
                # Simple momentum signal
                if np.random.random() > 0.7:  # 30% chance of signal
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY' if np.random.random() > 0.5 else 'SELL',
                        'quantity': 100,
                        'price': data['close'],
                        'confidence': np.random.uniform(0.6, 0.9),
                        'strategy': 'momentum'
                    })

        return signals

    engine.register_strategy('momentum', simple_momentum_strategy)

    # Run backtest
    print("\n🔬 Running backtest...")
    backtest_result = engine.run_backtest('momentum', lookback=20)
    if "error" not in backtest_result:
        print(f"✅ Total return: {backtest_result['performance_metrics']['total_return']:.2%}")
        print(f"✅ Sharpe ratio: {backtest_result['performance_metrics']['sharpe_ratio']:.2f}")
        print(f"✅ Max drawdown: {backtest_result['risk_metrics']['max_drawdown']:.2%}")

    # Run Monte Carlo simulation
    print("\n🎲 Running Monte Carlo simulation...")
    mc_result = engine.run_monte_carlo_simulation('momentum', n_simulations=100)
    if "error" not in mc_result:
        print(f"✅ Mean return: {mc_result['summary_statistics']['mean_return']:.2%}")
        print(f"✅ VaR 95%: {mc_result['risk_metrics']['var_95']:.2%}")

    print("\n✅ Advanced Backtesting Engine test completed!")
