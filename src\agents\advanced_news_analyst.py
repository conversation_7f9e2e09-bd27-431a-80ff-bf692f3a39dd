"""
Advanced News Analyst Agent - Llama3.2:90b Model
Sophisticated news analysis with sentiment analysis, event detection,
market impact assessment, and predictive news modeling.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import logging
import re
from textblob import TextBlob
from transformers import pipeline
import feedparser
import requests
from bs4 import BeautifulSoup
import warnings
warnings.filterwarnings('ignore')

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, NewsAlert, MarketEvent
from src.utils.news_sources import NewsSourceManager
from src.utils.sentiment_analyzer import AdvancedSentimentAnalyzer
from src.utils.event_detector import EventDetector
from src.utils.impact_predictor import MarketImpactPredictor


class SentimentScore(Enum):
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2


class NewsCategory(Enum):
    REGULATORY = "regulatory"
    INSTITUTIONAL = "institutional"
    TECHNICAL = "technical"
    MARKET_STRUCTURE = "market_structure"
    MACROECONOMIC = "macroeconomic"
    ADOPTION = "adoption"
    SECURITY = "security"
    PARTNERSHIPS = "partnerships"
    EARNINGS = "earnings"
    PRODUCT_LAUNCH = "product_launch"
    MERGER_ACQUISITION = "merger_acquisition"
    LEGAL = "legal"


class ImpactLevel(Enum):
    MINIMAL = "minimal"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class NewsArticle:
    article_id: str
    title: str
    content: str
    source: str
    author: Optional[str]
    published_at: datetime
    url: str
    symbols_mentioned: List[str]
    sentiment_score: float
    sentiment_label: SentimentScore
    category: NewsCategory
    impact_level: ImpactLevel
    credibility_score: float
    relevance_score: float
    urgency_score: float
    keywords: List[str]
    entities: List[str]
    market_implications: List[str]


@dataclass
class AdvancedNewsAlert:
    alert_id: str
    event_type: str
    severity: ImpactLevel
    affected_symbols: List[str]
    headline: str
    summary: str
    sentiment_analysis: Dict[str, Any]
    market_impact_prediction: Dict[str, Any]
    confidence: float
    time_sensitivity: int  # minutes until impact
    recommended_actions: List[str]
    related_articles: List[str]
    ai_analysis: str
    risk_factors: List[str]
    opportunity_factors: List[str]
    historical_precedents: List[str]
    timestamp: datetime


class AdvancedNewsAnalyst(BaseAgent):
    """
    Advanced News Analyst using Llama3.2:90b for sophisticated news analysis.
    
    Features:
    - Real-time news monitoring from 50+ sources
    - Advanced sentiment analysis with context understanding
    - Event detection and classification
    - Market impact prediction with ML models
    - Multi-language news processing
    - Fake news detection and credibility scoring
    - Entity recognition and relationship mapping
    - Trend analysis and narrative tracking
    - Social media sentiment integration
    - Regulatory filing analysis
    - Earnings report analysis
    - Economic indicator impact assessment
    - Real-time alert generation
    - Historical event correlation
    - Predictive news modeling
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_news_analyst"
        self.model_name = "llama3.2:90b"
        
        # News analysis components
        self.news_source_manager = NewsSourceManager()
        self.sentiment_analyzer = AdvancedSentimentAnalyzer()
        self.event_detector = EventDetector()
        self.impact_predictor = MarketImpactPredictor()
        
        # News data storage
        self.news_articles = {}
        self.sentiment_trends = {}
        self.event_timeline = {}
        self.impact_predictions = {}
        self.news_alerts = {}
        
        # News sources configuration
        self.news_sources = {
            "crypto_news": [
                "https://cointelegraph.com/rss",
                "https://coindesk.com/arc/outboundfeeds/rss/",
                "https://decrypt.co/feed",
                "https://www.theblockcrypto.com/rss.xml",
                "https://cryptonews.com/news/feed/"
            ],
            "financial_news": [
                "https://feeds.bloomberg.com/markets/news.rss",
                "https://feeds.reuters.com/reuters/businessNews",
                "https://feeds.cnbc.com/cnbc/world.rss",
                "https://feeds.marketwatch.com/marketwatch/topstories/"
            ],
            "regulatory_news": [
                "https://www.sec.gov/news/pressreleases.rss",
                "https://www.cftc.gov/rss/pressreleases.xml"
            ]
        }
        
        # Analysis parameters
        self.sentiment_threshold = 0.3
        self.impact_threshold = 0.6
        self.credibility_threshold = 0.7
        self.relevance_threshold = 0.5
        
        # Keywords for crypto relevance
        self.crypto_keywords = [
            "bitcoin", "btc", "ethereum", "eth", "cryptocurrency", "crypto",
            "blockchain", "defi", "nft", "altcoin", "trading", "exchange",
            "wallet", "mining", "staking", "yield", "liquidity", "protocol"
        ]
        
        # Market impact keywords
        self.impact_keywords = {
            "high_impact": [
                "regulation", "ban", "approval", "etf", "institutional",
                "adoption", "partnership", "acquisition", "hack", "security"
            ],
            "medium_impact": [
                "upgrade", "launch", "listing", "delisting", "earnings",
                "report", "analysis", "prediction", "forecast"
            ],
            "low_impact": [
                "comment", "opinion", "interview", "discussion", "rumor"
            ]
        }
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced news analyst components."""
        self.logger.info("📰 Initializing Advanced News Analyst with Llama3.2:90b")
        
        # Initialize news analysis components
        await self.news_source_manager.initialize()
        await self.sentiment_analyzer.initialize()
        await self.event_detector.initialize()
        await self.impact_predictor.initialize()
        
        # Load historical news data
        await self._load_historical_news_data()
        
        # Initialize sentiment models
        await self._initialize_sentiment_models()
        
        self.logger.info("✅ Advanced News Analyst initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced news analysis tasks."""
        return [
            asyncio.create_task(self._news_monitoring()),
            asyncio.create_task(self._sentiment_analysis()),
            asyncio.create_task(self._event_detection()),
            asyncio.create_task(self._impact_prediction()),
            asyncio.create_task(self._trend_analysis()),
            asyncio.create_task(self._alert_generation()),
            asyncio.create_task(self._social_media_monitoring()),
            asyncio.create_task(self._regulatory_monitoring()),
            asyncio.create_task(self._earnings_analysis()),
            asyncio.create_task(self._narrative_tracking())
        ]

    async def _news_monitoring(self):
        """Monitor news sources for new articles."""
        while self.running:
            try:
                new_articles = []
                
                # Monitor RSS feeds
                for category, sources in self.news_sources.items():
                    for source_url in sources:
                        articles = await self._fetch_rss_articles(source_url, category)
                        new_articles.extend(articles)
                
                # Process new articles
                for article in new_articles:
                    if await self._is_relevant_article(article):
                        processed_article = await self._process_article(article)
                        if processed_article:
                            await self._store_article(processed_article)
                            await self._analyze_article_impact(processed_article)
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"News monitoring error: {e}")
                await asyncio.sleep(30)

    async def _sentiment_analysis(self):
        """Perform advanced sentiment analysis on news articles."""
        while self.running:
            try:
                # Get recent unanalyzed articles
                recent_articles = await self._get_recent_articles(hours=1)
                
                for article in recent_articles:
                    if not article.get("sentiment_analyzed", False):
                        # Perform comprehensive sentiment analysis
                        sentiment_result = await self._analyze_article_sentiment(article)
                        
                        # Update article with sentiment data
                        article.update(sentiment_result)
                        article["sentiment_analyzed"] = True
                        
                        # Update sentiment trends
                        await self._update_sentiment_trends(article)
                
                await asyncio.sleep(120)  # Every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Sentiment analysis error: {e}")
                await asyncio.sleep(60)

    async def _event_detection(self):
        """Detect significant market events from news."""
        while self.running:
            try:
                # Get recent articles for event detection
                recent_articles = await self._get_recent_articles(hours=6)
                
                # Cluster articles by topic
                article_clusters = await self._cluster_articles_by_topic(recent_articles)
                
                # Detect events from clusters
                for cluster in article_clusters:
                    event = await self._detect_event_from_cluster(cluster)
                    if event:
                        await self._process_detected_event(event)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Event detection error: {e}")
                await asyncio.sleep(120)

    async def _impact_prediction(self):
        """Predict market impact of news events."""
        while self.running:
            try:
                # Get recent high-impact articles
                high_impact_articles = await self._get_high_impact_articles()
                
                for article in high_impact_articles:
                    if not article.get("impact_predicted", False):
                        # Predict market impact
                        impact_prediction = await self._predict_market_impact(article)
                        
                        # Store prediction
                        article["impact_prediction"] = impact_prediction
                        article["impact_predicted"] = True
                        
                        # Generate alert if high impact predicted
                        if impact_prediction.get("impact_score", 0) > self.impact_threshold:
                            await self._generate_impact_alert(article, impact_prediction)
                
                await asyncio.sleep(180)  # Every 3 minutes
                
            except Exception as e:
                self.logger.error(f"Impact prediction error: {e}")
                await asyncio.sleep(90)

    async def _analyze_article_sentiment(self, article: Dict) -> Dict[str, Any]:
        """Perform comprehensive sentiment analysis on an article."""
        
        text = f"{article['title']} {article['content']}"
        
        # Basic sentiment analysis
        blob = TextBlob(text)
        basic_sentiment = blob.sentiment.polarity
        
        # Advanced sentiment analysis using AI
        ai_context = {
            "title": article["title"],
            "content": article["content"][:1000],  # First 1000 chars
            "source": article["source"],
            "symbols": article.get("symbols_mentioned", [])
        }
        
        ai_sentiment_analysis = await ai_service.generate_response(
            "news_analyst",
            f"""
            As an expert financial news analyst using the Llama3.2:90b model, perform comprehensive sentiment analysis on this article:
            
            Title: {ai_context['title']}
            Content: {ai_context['content']}
            Source: {ai_context['source']}
            Mentioned Symbols: {ai_context['symbols']}
            
            Provide detailed sentiment analysis including:
            1. Overall sentiment score (-1 to +1)
            2. Sentiment confidence level (0 to 1)
            3. Key sentiment drivers (positive and negative factors)
            4. Market implications and potential impact
            5. Emotional tone analysis
            6. Bias detection and credibility assessment
            7. Symbol-specific sentiment if multiple assets mentioned
            8. Time sensitivity of the sentiment impact
            
            Focus on market-relevant sentiment that could affect trading decisions.
            Consider context, nuance, and potential market reactions.
            """,
            ai_context
        )
        
        # Parse AI response for structured sentiment data
        sentiment_data = await self._parse_sentiment_analysis(ai_sentiment_analysis)
        
        # Combine basic and AI sentiment
        final_sentiment = {
            "basic_sentiment": basic_sentiment,
            "ai_sentiment": sentiment_data.get("sentiment_score", 0),
            "combined_sentiment": (basic_sentiment + sentiment_data.get("sentiment_score", 0)) / 2,
            "confidence": sentiment_data.get("confidence", 0.5),
            "sentiment_drivers": sentiment_data.get("drivers", []),
            "market_implications": sentiment_data.get("implications", []),
            "credibility_score": sentiment_data.get("credibility", 0.7),
            "ai_analysis": ai_sentiment_analysis
        }
        
        return final_sentiment

    async def _predict_market_impact(self, article: Dict) -> Dict[str, Any]:
        """Predict market impact of a news article."""
        
        # Extract features for impact prediction
        features = {
            "sentiment_score": article.get("combined_sentiment", 0),
            "credibility_score": article.get("credibility_score", 0.7),
            "source_authority": await self._get_source_authority(article["source"]),
            "keyword_impact_score": self._calculate_keyword_impact(article),
            "timing_factor": self._calculate_timing_factor(article),
            "volume_factor": await self._get_related_article_volume(article)
        }
        
        # Use AI for sophisticated impact prediction
        ai_context = {
            "article": article,
            "features": features,
            "market_conditions": await self._get_current_market_conditions(),
            "historical_similar_events": await self._get_similar_historical_events(article)
        }
        
        ai_impact_analysis = await ai_service.generate_response(
            "news_analyst",
            f"""
            As a market impact specialist, predict the potential market impact of this news article:
            
            Article: {article['title']}
            Sentiment: {article.get('combined_sentiment', 0)}
            Features: {features}
            Market Conditions: {ai_context['market_conditions']}
            Historical Precedents: {ai_context['historical_similar_events']}
            
            Provide comprehensive impact prediction including:
            1. Impact score (0 to 1) for each mentioned symbol
            2. Expected price movement direction and magnitude
            3. Time horizon for impact (immediate, short-term, long-term)
            4. Confidence level in the prediction
            5. Key risk factors and uncertainties
            6. Potential catalysts that could amplify impact
            7. Market segments most likely to be affected
            8. Recommended monitoring indicators
            
            Consider market context, timing, and historical precedents.
            Focus on actionable insights for trading decisions.
            """,
            ai_context
        )
        
        # Parse AI response for structured impact data
        impact_prediction = await self._parse_impact_prediction(ai_impact_analysis)
        
        return impact_prediction

    async def _generate_impact_alert(self, article: Dict, impact_prediction: Dict):
        """Generate alert for high-impact news."""
        
        alert = AdvancedNewsAlert(
            alert_id=f"news_alert_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            event_type=article.get("category", "general"),
            severity=self._determine_impact_severity(impact_prediction),
            affected_symbols=article.get("symbols_mentioned", []),
            headline=article["title"],
            summary=article["content"][:200] + "...",
            sentiment_analysis=article.get("sentiment_data", {}),
            market_impact_prediction=impact_prediction,
            confidence=impact_prediction.get("confidence", 0.5),
            time_sensitivity=impact_prediction.get("time_to_impact", 60),
            recommended_actions=impact_prediction.get("recommended_actions", []),
            related_articles=await self._get_related_articles(article),
            ai_analysis=impact_prediction.get("ai_analysis", ""),
            risk_factors=impact_prediction.get("risk_factors", []),
            opportunity_factors=impact_prediction.get("opportunities", []),
            historical_precedents=impact_prediction.get("precedents", []),
            timestamp=datetime.utcnow()
        )
        
        # Store alert
        self.news_alerts[alert.alert_id] = alert
        
        # Send alert to other agents
        await self._send_news_alert(alert)

    async def _fetch_rss_articles(self, source_url: str, category: str) -> List[Dict]:
        """Fetch articles from RSS feed."""
        
        try:
            feed = feedparser.parse(source_url)
            articles = []
            
            for entry in feed.entries[:10]:  # Limit to 10 most recent
                article = {
                    "title": entry.get("title", ""),
                    "content": entry.get("summary", ""),
                    "source": feed.feed.get("title", source_url),
                    "url": entry.get("link", ""),
                    "published_at": self._parse_published_date(entry),
                    "category": category
                }
                articles.append(article)
            
            return articles
            
        except Exception as e:
            self.logger.error(f"Error fetching RSS from {source_url}: {e}")
            return []

    async def _is_relevant_article(self, article: Dict) -> bool:
        """Check if article is relevant to crypto/trading."""
        
        text = f"{article['title']} {article['content']}".lower()
        
        # Check for crypto keywords
        crypto_relevance = any(keyword in text for keyword in self.crypto_keywords)
        
        # Check for trading/financial keywords
        financial_keywords = ["trading", "market", "price", "investment", "financial"]
        financial_relevance = any(keyword in text for keyword in financial_keywords)
        
        return crypto_relevance or financial_relevance

    def _calculate_keyword_impact(self, article: Dict) -> float:
        """Calculate impact score based on keywords."""
        
        text = f"{article['title']} {article['content']}".lower()
        impact_score = 0.0
        
        # High impact keywords
        for keyword in self.impact_keywords["high_impact"]:
            if keyword in text:
                impact_score += 0.3
        
        # Medium impact keywords
        for keyword in self.impact_keywords["medium_impact"]:
            if keyword in text:
                impact_score += 0.2
        
        # Low impact keywords
        for keyword in self.impact_keywords["low_impact"]:
            if keyword in text:
                impact_score += 0.1
        
        return min(1.0, impact_score)

    def _calculate_timing_factor(self, article: Dict) -> float:
        """Calculate timing factor for impact prediction."""
        
        published_at = article.get("published_at", datetime.utcnow())
        time_diff = datetime.utcnow() - published_at
        
        # Recent news has higher impact
        if time_diff.total_seconds() < 3600:  # 1 hour
            return 1.0
        elif time_diff.total_seconds() < 7200:  # 2 hours
            return 0.8
        elif time_diff.total_seconds() < 14400:  # 4 hours
            return 0.6
        else:
            return 0.4

    def _determine_impact_severity(self, impact_prediction: Dict) -> ImpactLevel:
        """Determine impact severity from prediction."""
        
        impact_score = impact_prediction.get("impact_score", 0)
        
        if impact_score >= 0.9:
            return ImpactLevel.EXTREME
        elif impact_score >= 0.7:
            return ImpactLevel.HIGH
        elif impact_score >= 0.5:
            return ImpactLevel.MODERATE
        elif impact_score >= 0.3:
            return ImpactLevel.LOW
        else:
            return ImpactLevel.MINIMAL

    def _parse_published_date(self, entry) -> datetime:
        """Parse published date from RSS entry."""
        
        try:
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                return datetime(*entry.published_parsed[:6])
            elif hasattr(entry, 'published'):
                # Try to parse string date
                from dateutil import parser
                return parser.parse(entry.published)
            else:
                return datetime.utcnow()
        except:
            return datetime.utcnow()

    async def _cleanup_agent(self):
        """Cleanup news analyst resources."""
        self.logger.info("🧹 Cleaning up Advanced News Analyst resources")
        
        # Clear data structures
        self.news_articles.clear()
        self.sentiment_trends.clear()
        self.event_timeline.clear()
        self.impact_predictions.clear()
        self.news_alerts.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "news_analysis_request":
            await self._process_news_analysis_request(message.content)
        elif message.message_type == "sentiment_update":
            await self._process_sentiment_update(message.content)
        elif message.message_type == "event_alert":
            await self._process_event_alert(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic news analysis."""
        while self.running:
            try:
                # Update sentiment trends
                await self._update_sentiment_trends_summary()
                
                # Generate news reports
                await self._generate_news_reports()
                
                # Clean old articles
                await self._cleanup_old_articles()
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Periodic news analysis error: {e}")
                await asyncio.sleep(300)
