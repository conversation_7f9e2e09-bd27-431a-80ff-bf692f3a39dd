"""Pytest configuration shared fixtures & path setup.

Ensures project root is on sys.path so that `import src.*` works
regardless of where pytest collects tests from.
"""

from __future__ import annotations

import sys
import asyncio
import pytest
import logging
from pathlib import Path
from typing import AsyncGenerator, Dict, Any
from unittest.mock import AsyncMock, MagicMock

# Add project root to sys.path if not already present.
ROOT = Path(__file__).resolve().parent.parent
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

# Configure logging for tests
logging.basicConfig(level=logging.INFO)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def mock_redis():
    """Mock Redis client for testing."""
    mock_redis = AsyncMock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.setex.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    mock_redis.ping.return_value = True
    return mock_redis

@pytest.fixture
async def mock_postgres():
    """Mock PostgreSQL connection for testing."""
    mock_postgres = AsyncMock()
    mock_postgres.execute.return_value = None
    mock_postgres.fetch.return_value = []
    mock_postgres.fetchrow.return_value = None
    return mock_postgres

@pytest.fixture
async def mock_clickhouse():
    """Mock ClickHouse client for testing."""
    mock_clickhouse = MagicMock()
    mock_clickhouse.execute.return_value = []
    return mock_clickhouse

@pytest.fixture
async def mock_mongodb():
    """Mock MongoDB client for testing."""
    mock_mongodb = AsyncMock()
    mock_mongodb.find.return_value.to_list.return_value = []
    mock_mongodb.insert_one.return_value.inserted_id = "test_id"
    return mock_mongodb

@pytest.fixture
async def mock_ai_service():
    """Mock AI service for testing."""
    mock_ai = AsyncMock()
    mock_ai.analyze_market_data.return_value = "Mock AI analysis: Bullish trend detected"
    mock_ai.generate_trading_strategy.return_value = "Mock strategy: Buy signal"
    mock_ai.generate_response.return_value = "Mock AI response"
    return mock_ai

@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    return {
        "symbol": "BTCUSDT",
        "price": 45000.0,
        "volume": 1000000.0,
        "change_24h": 2.5,
        "timestamp": "2024-01-01T00:00:00Z",
        "bid": 44995.0,
        "ask": 45005.0
    }

@pytest.fixture
def sample_trading_signal():
    """Sample trading signal for testing."""
    return {
        "symbol": "BTCUSDT",
        "action": "BUY",
        "strength": 0.8,
        "confidence": 0.75,
        "price_target": 46000.0,
        "stop_loss": 44000.0,
        "strategy": "sma_crossover",
        "reasoning": "Strong bullish momentum detected"
    }

@pytest.fixture
def sample_agent_status():
    """Sample agent status for testing."""
    return {
        "market_watcher": {"running": True, "last_heartbeat": "2024-01-01T00:00:00Z"},
        "strategy_researcher": {"running": True, "last_heartbeat": "2024-01-01T00:00:00Z"},
        "risk_officer": {"running": True, "last_heartbeat": "2024-01-01T00:00:00Z"},
        "technical_analyst": {"running": True, "last_heartbeat": "2024-01-01T00:00:00Z"}
    }