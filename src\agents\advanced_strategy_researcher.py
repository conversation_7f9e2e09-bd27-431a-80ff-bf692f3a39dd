"""
Advanced Strategy Researcher Agent - Cogito:32b Model
Sophisticated trading strategy development, backtesting, optimization,
and adaptive strategy selection with machine learning capabilities.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import logging
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, TradingStrategy, StrategySignal
from src.utils.strategy_library import StrategyLibrary
from src.utils.backtesting_engine import BacktestingEngine
from src.utils.optimization_engine import OptimizationEngine
from src.utils.risk_calculator import RiskCalculator


class StrategyType(Enum):
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    ARBITRAGE = "arbitrage"
    STATISTICAL_ARBITRAGE = "statistical_arbitrage"
    MACHINE_LEARNING = "machine_learning"
    HYBRID = "hybrid"
    ADAPTIVE = "adaptive"


class MarketCondition(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"


@dataclass
class StrategyPerformance:
    strategy_id: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    calmar_ratio: float
    sortino_ratio: float
    trades_count: int
    avg_trade_duration: float
    market_correlation: float
    volatility: float
    beta: float
    alpha: float
    information_ratio: float


@dataclass
class AdvancedStrategySignal:
    strategy_id: str
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    strength: float
    entry_price: float
    stop_loss: float
    take_profit: List[float]
    position_size: float
    risk_reward_ratio: float
    expected_return: float
    holding_period: int
    market_conditions: List[str]
    strategy_reasoning: str
    ai_analysis: str
    confluence_factors: List[str]
    timestamp: datetime


class AdvancedStrategyResearcher(BaseAgent):
    """
    Advanced Strategy Researcher using Cogito:32b for sophisticated strategy development.
    
    Features:
    - 50+ pre-built trading strategies
    - Advanced backtesting with walk-forward analysis
    - Multi-objective optimization (return, risk, drawdown)
    - Machine learning strategy adaptation
    - Real-time strategy performance monitoring
    - Adaptive strategy selection based on market conditions
    - Portfolio-level strategy allocation
    - Risk-adjusted strategy scoring
    - Strategy ensemble methods
    - Advanced feature engineering
    - Regime-aware strategy switching
    - Monte Carlo simulation for strategy validation
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_strategy_researcher"
        self.model_name = "cogito:32b"
        
        # Strategy components
        self.strategy_library = StrategyLibrary()
        self.backtesting_engine = BacktestingEngine()
        self.optimization_engine = OptimizationEngine()
        self.risk_calculator = RiskCalculator()
        
        # Machine learning models
        self.ml_models = {}
        self.feature_scalers = {}
        
        # Strategy storage
        self.active_strategies = {}
        self.strategy_performance = {}
        self.strategy_signals = {}
        self.market_conditions = {}
        
        # Configuration
        self.lookback_period = 252  # 1 year
        self.min_trades_for_validation = 30
        self.max_strategies_per_symbol = 5
        self.rebalance_frequency = "daily"
        
        # Performance thresholds
        self.min_sharpe_ratio = 1.0
        self.max_drawdown_threshold = 0.15
        self.min_win_rate = 0.45
        self.min_profit_factor = 1.2
        
        # Strategy weights for ensemble
        self.strategy_weights = {}
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced strategy researcher components."""
        self.logger.info("🧠 Initializing Advanced Strategy Researcher with Cogito:32b")
        
        # Initialize strategy library
        await self.strategy_library.initialize()
        
        # Initialize backtesting engine
        await self.backtesting_engine.initialize()
        
        # Initialize optimization engine
        await self.optimization_engine.initialize()
        
        # Load pre-trained ML models
        await self._load_ml_models()
        
        # Initialize strategy database
        await self._initialize_strategy_database()
        
        self.logger.info("✅ Advanced Strategy Researcher initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced strategy research tasks."""
        return [
            asyncio.create_task(self._strategy_discovery()),
            asyncio.create_task(self._strategy_optimization()),
            asyncio.create_task(self._strategy_validation()),
            asyncio.create_task(self._adaptive_strategy_selection()),
            asyncio.create_task(self._performance_monitoring()),
            asyncio.create_task(self._market_condition_analysis()),
            asyncio.create_task(self._ml_model_training()),
            asyncio.create_task(self._strategy_ensemble_management()),
            asyncio.create_task(self._risk_adjusted_allocation())
        ]

    async def _strategy_discovery(self):
        """Discover and develop new trading strategies."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    # Get market data
                    data = await self._get_market_data(symbol)
                    if len(data) < self.lookback_period:
                        continue
                    
                    # Analyze market characteristics
                    market_profile = await self._analyze_market_profile(symbol, data)
                    
                    # Generate strategy ideas using AI
                    strategy_ideas = await self._generate_strategy_ideas(symbol, market_profile)
                    
                    # Develop and test new strategies
                    for idea in strategy_ideas:
                        strategy = await self._develop_strategy(symbol, idea, data)
                        if strategy:
                            await self._validate_new_strategy(symbol, strategy)
                
                await asyncio.sleep(3600)  # Every hour
                
            except Exception as e:
                self.logger.error(f"Strategy discovery error: {e}")
                await asyncio.sleep(300)

    async def _strategy_optimization(self):
        """Optimize existing strategies using advanced techniques."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    if symbol not in self.active_strategies:
                        continue
                    
                    strategies = self.active_strategies[symbol]
                    
                    for strategy_id, strategy in strategies.items():
                        # Get recent performance data
                        performance = await self._get_strategy_performance(strategy_id)
                        
                        # Check if optimization is needed
                        if self._needs_optimization(performance):
                            # Optimize strategy parameters
                            optimized_strategy = await self._optimize_strategy(strategy)
                            
                            # Validate optimized strategy
                            if await self._validate_optimization(strategy, optimized_strategy):
                                self.active_strategies[symbol][strategy_id] = optimized_strategy
                                self.logger.info(f"Optimized strategy {strategy_id} for {symbol}")
                
                await asyncio.sleep(1800)  # Every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Strategy optimization error: {e}")
                await asyncio.sleep(300)

    async def _adaptive_strategy_selection(self):
        """Adaptively select best strategies based on current market conditions."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    # Analyze current market conditions
                    current_conditions = await self._analyze_current_market_conditions(symbol)
                    
                    # Get available strategies
                    available_strategies = self.active_strategies.get(symbol, {})
                    
                    if not available_strategies:
                        continue
                    
                    # Score strategies for current conditions
                    strategy_scores = {}
                    for strategy_id, strategy in available_strategies.items():
                        score = await self._score_strategy_for_conditions(
                            strategy, current_conditions
                        )
                        strategy_scores[strategy_id] = score
                    
                    # Select best strategies
                    selected_strategies = await self._select_optimal_strategies(
                        strategy_scores, current_conditions
                    )
                    
                    # Update strategy weights
                    await self._update_strategy_weights(symbol, selected_strategies)
                    
                    # Generate ensemble signals
                    ensemble_signal = await self._generate_ensemble_signal(
                        symbol, selected_strategies
                    )
                    
                    if ensemble_signal:
                        await self._process_strategy_signal(ensemble_signal)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Adaptive strategy selection error: {e}")
                await asyncio.sleep(60)

    async def _ml_model_training(self):
        """Train and update machine learning models for strategy enhancement."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    # Get training data
                    data = await self._get_ml_training_data(symbol)
                    if len(data) < 1000:  # Need sufficient data
                        continue
                    
                    # Prepare features and targets
                    features, targets = await self._prepare_ml_data(data)
                    
                    # Train classification model (direction prediction)
                    direction_model = await self._train_direction_model(features, targets["direction"])
                    
                    # Train regression model (magnitude prediction)
                    magnitude_model = await self._train_magnitude_model(features, targets["magnitude"])
                    
                    # Validate models
                    if await self._validate_ml_models(direction_model, magnitude_model, features, targets):
                        self.ml_models[symbol] = {
                            "direction": direction_model,
                            "magnitude": magnitude_model,
                            "scaler": self.feature_scalers[symbol],
                            "last_updated": datetime.utcnow()
                        }
                        
                        self.logger.info(f"Updated ML models for {symbol}")
                
                await asyncio.sleep(7200)  # Every 2 hours
                
            except Exception as e:
                self.logger.error(f"ML model training error: {e}")
                await asyncio.sleep(600)

    async def _generate_strategy_ideas(self, symbol: str, market_profile: Dict) -> List[Dict]:
        """Generate strategy ideas using AI analysis."""
        
        ai_context = {
            "symbol": symbol,
            "market_profile": market_profile,
            "current_strategies": list(self.active_strategies.get(symbol, {}).keys()),
            "market_conditions": self.market_conditions.get(symbol, {})
        }
        
        ai_response = await ai_service.generate_response(
            "strategy_researcher",
            f"""
            As an advanced quantitative strategist using the Cogito:32b model, analyze the market profile for {symbol} and generate innovative trading strategy ideas.
            
            Market Profile: {market_profile}
            Current Active Strategies: {ai_context['current_strategies']}
            Market Conditions: {ai_context['market_conditions']}
            
            Generate 3-5 sophisticated strategy ideas that:
            1. Are well-suited to the current market characteristics
            2. Complement existing strategies (low correlation)
            3. Have clear entry/exit rules
            4. Include risk management parameters
            5. Are adaptable to changing conditions
            
            For each strategy, provide:
            - Strategy type and core logic
            - Entry and exit conditions
            - Risk management rules
            - Expected market conditions for optimal performance
            - Potential weaknesses and mitigation
            - Parameter ranges for optimization
            
            Focus on innovative approaches that combine multiple techniques.
            """,
            ai_context
        )
        
        # Parse AI response into strategy ideas
        strategy_ideas = await self._parse_strategy_ideas(ai_response)
        
        return strategy_ideas

    async def _develop_strategy(self, symbol: str, idea: Dict, data: pd.DataFrame) -> Optional[Dict]:
        """Develop a complete strategy from an idea."""
        
        try:
            # Create strategy framework
            strategy = {
                "id": f"{symbol}_{idea['type']}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                "symbol": symbol,
                "type": idea["type"],
                "name": idea["name"],
                "description": idea["description"],
                "parameters": idea["parameters"],
                "entry_conditions": idea["entry_conditions"],
                "exit_conditions": idea["exit_conditions"],
                "risk_management": idea["risk_management"],
                "created_at": datetime.utcnow()
            }
            
            # Implement strategy logic
            strategy_logic = await self._implement_strategy_logic(strategy)
            
            # Backtest strategy
            backtest_results = await self.backtesting_engine.backtest_strategy(
                strategy_logic, data
            )
            
            # Evaluate performance
            performance = await self._evaluate_strategy_performance(backtest_results)
            
            # Check if strategy meets minimum criteria
            if self._meets_minimum_criteria(performance):
                strategy["performance"] = performance
                strategy["backtest_results"] = backtest_results
                return strategy
            
            return None
            
        except Exception as e:
            self.logger.error(f"Strategy development error: {e}")
            return None

    async def _optimize_strategy(self, strategy: Dict) -> Dict:
        """Optimize strategy parameters using advanced techniques."""
        
        # Get optimization bounds
        param_bounds = self._get_parameter_bounds(strategy)
        
        # Define optimization objective
        def objective_function(params):
            # Create strategy with new parameters
            test_strategy = strategy.copy()
            test_strategy["parameters"] = params
            
            # Quick backtest
            performance = self._quick_backtest(test_strategy)
            
            # Multi-objective score (return, risk, drawdown)
            score = (
                performance["sharpe_ratio"] * 0.4 +
                (1 - performance["max_drawdown"]) * 0.3 +
                performance["calmar_ratio"] * 0.3
            )
            
            return -score  # Minimize negative score
        
        # Run optimization
        optimal_params = await self.optimization_engine.optimize(
            objective_function, param_bounds
        )
        
        # Create optimized strategy
        optimized_strategy = strategy.copy()
        optimized_strategy["parameters"] = optimal_params
        optimized_strategy["optimized_at"] = datetime.utcnow()
        
        return optimized_strategy

    async def _score_strategy_for_conditions(self, strategy: Dict, conditions: Dict) -> float:
        """Score strategy suitability for current market conditions."""
        
        score = 0.0
        
        # Base performance score
        performance = strategy.get("performance", {})
        score += performance.get("sharpe_ratio", 0) * 0.3
        score += (1 - performance.get("max_drawdown", 1)) * 0.2
        score += performance.get("win_rate", 0) * 0.1
        
        # Market condition alignment
        strategy_type = strategy.get("type", "")
        current_regime = conditions.get("regime", "")
        
        # Strategy-regime compatibility matrix
        compatibility = {
            "trend_following": {"trending": 0.9, "breakout": 0.8, "ranging": 0.2},
            "mean_reversion": {"ranging": 0.9, "low_volatility": 0.8, "trending": 0.3},
            "momentum": {"trending": 0.8, "breakout": 0.9, "volatile": 0.7},
            "statistical_arbitrage": {"ranging": 0.8, "low_volatility": 0.9}
        }
        
        regime_score = compatibility.get(strategy_type, {}).get(current_regime, 0.5)
        score += regime_score * 0.4
        
        return max(0.0, min(1.0, score))

    async def _generate_ensemble_signal(self, symbol: str, selected_strategies: Dict) -> Optional[AdvancedStrategySignal]:
        """Generate ensemble signal from multiple strategies."""
        
        if not selected_strategies:
            return None
        
        # Get individual strategy signals
        individual_signals = []
        for strategy_id, weight in selected_strategies.items():
            strategy = self.active_strategies[symbol][strategy_id]
            signal = await self._generate_individual_signal(strategy)
            if signal:
                signal["weight"] = weight
                individual_signals.append(signal)
        
        if not individual_signals:
            return None
        
        # Combine signals using weighted voting
        ensemble_signal = await self._combine_signals(individual_signals)
        
        # Add AI analysis for ensemble reasoning
        ai_context = {
            "symbol": symbol,
            "individual_signals": individual_signals,
            "ensemble_signal": ensemble_signal,
            "market_conditions": self.market_conditions.get(symbol, {})
        }
        
        ai_analysis = await ai_service.generate_response(
            "strategy_researcher",
            f"""
            As an expert quantitative analyst, analyze the ensemble trading signal for {symbol}.
            
            Individual Signals: {individual_signals}
            Ensemble Signal: {ensemble_signal}
            Market Conditions: {ai_context['market_conditions']}
            
            Provide comprehensive analysis including:
            1. Signal confluence and divergence analysis
            2. Risk assessment and position sizing recommendation
            3. Market timing and execution strategy
            4. Potential scenarios and contingency plans
            5. Confidence level and key risk factors
            
            Focus on actionable insights for trade execution.
            """,
            ai_context
        )
        
        # Create advanced strategy signal
        advanced_signal = AdvancedStrategySignal(
            strategy_id="ensemble",
            symbol=symbol,
            action=ensemble_signal["action"],
            confidence=ensemble_signal["confidence"],
            strength=ensemble_signal["strength"],
            entry_price=ensemble_signal["entry_price"],
            stop_loss=ensemble_signal["stop_loss"],
            take_profit=ensemble_signal["take_profit"],
            position_size=ensemble_signal["position_size"],
            risk_reward_ratio=ensemble_signal["risk_reward_ratio"],
            expected_return=ensemble_signal["expected_return"],
            holding_period=ensemble_signal["holding_period"],
            market_conditions=ensemble_signal["market_conditions"],
            strategy_reasoning=ensemble_signal["reasoning"],
            ai_analysis=ai_analysis,
            confluence_factors=ensemble_signal["confluence_factors"],
            timestamp=datetime.utcnow()
        )
        
        return advanced_signal

    async def _train_direction_model(self, features: np.ndarray, targets: np.ndarray) -> RandomForestClassifier:
        """Train model to predict price direction."""
        
        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42
        )
        
        # Time series cross-validation
        tscv = TimeSeriesSplit(n_splits=5)
        
        # Train model
        model.fit(features, targets)
        
        return model

    async def _train_magnitude_model(self, features: np.ndarray, targets: np.ndarray) -> GradientBoostingRegressor:
        """Train model to predict price movement magnitude."""
        
        model = GradientBoostingRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=6,
            min_samples_split=20,
            min_samples_leaf=10,
            random_state=42
        )
        
        # Train model
        model.fit(features, targets)
        
        return model

    def _meets_minimum_criteria(self, performance: Dict) -> bool:
        """Check if strategy meets minimum performance criteria."""
        
        return (
            performance.get("sharpe_ratio", 0) >= self.min_sharpe_ratio and
            performance.get("max_drawdown", 1) <= self.max_drawdown_threshold and
            performance.get("win_rate", 0) >= self.min_win_rate and
            performance.get("profit_factor", 0) >= self.min_profit_factor and
            performance.get("trades_count", 0) >= self.min_trades_for_validation
        )

    def _needs_optimization(self, performance: Dict) -> bool:
        """Determine if strategy needs optimization."""
        
        recent_performance = performance.get("recent_performance", {})
        
        return (
            recent_performance.get("sharpe_ratio", 0) < self.min_sharpe_ratio * 0.8 or
            recent_performance.get("max_drawdown", 0) > self.max_drawdown_threshold * 1.2 or
            recent_performance.get("win_rate", 0) < self.min_win_rate * 0.9
        )

    async def _cleanup_agent(self):
        """Cleanup strategy researcher resources."""
        self.logger.info("🧹 Cleaning up Advanced Strategy Researcher resources")
        
        # Save strategy performance data
        await self._save_strategy_data()
        
        # Clear memory structures
        self.active_strategies.clear()
        self.strategy_performance.clear()
        self.strategy_signals.clear()
        self.ml_models.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "strategy_request":
            await self._process_strategy_request(message.content)
        elif message.message_type == "performance_update":
            await self._process_performance_update(message.content)
        elif message.message_type == "optimization_request":
            await self._process_optimization_request(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic strategy analysis."""
        while self.running:
            try:
                # Update strategy performance metrics
                await self._update_performance_metrics()
                
                # Rebalance strategy allocations
                await self._rebalance_strategies()
                
                # Generate strategy reports
                await self._generate_strategy_reports()
                
                await asyncio.sleep(3600)  # Every hour
                
            except Exception as e:
                self.logger.error(f"Periodic analysis error: {e}")
                await asyncio.sleep(300)
