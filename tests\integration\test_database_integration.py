"""
Database Integration Tests for NORYON V2

Tests data persistence, retrieval, and consistency across all storage systems:
PostgreSQL (transactional), ClickHouse (analytics), MongoDB (logs), Redis (caching).
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List

from src.db.database_manager import DatabaseManager
from src.db.postgres import get_async_session
from src.db.clickhouse import get_client as get_clickhouse_client
from src.db.redis import get_client as get_redis_client


class TestDatabaseIntegration:
    """Test suite for database integration verification."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.test_data = {
            "symbol": "BTCUSDT",
            "price": 45000.0,
            "volume": 1000000.0,
            "timestamp": datetime.utcnow()
        }
        
    @pytest.mark.asyncio
    async def test_database_manager_initialization(self, mock_redis, mock_postgres, mock_clickhouse, mock_mongodb):
        """Test database manager can initialize all database connections."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse), \
             patch('src.db.database_manager.motor.motor_asyncio.AsyncIOMotorClient') as mock_mongo_client:
            
            # Setup engine mock
            mock_engine = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value.execute = AsyncMock()
            mock_pg_engine.return_value = mock_engine
            
            # Setup MongoDB mock
            mock_mongo_instance = AsyncMock()
            mock_mongo_instance.admin.command = AsyncMock()
            mock_mongo_client.return_value = mock_mongo_instance
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Verify all connections are established
            assert db_manager.connection_status["redis"] == True
            assert db_manager.connection_status["postgres"] == True
            assert db_manager.connection_status["clickhouse"] == True
            assert db_manager.connection_status["mongodb"] == True

    @pytest.mark.asyncio
    async def test_postgres_transactional_operations(self, mock_postgres):
        """Test PostgreSQL transactional data operations."""
        with patch('src.db.database_manager.create_async_engine') as mock_engine_factory:
            # Setup mock engine and session
            mock_engine = AsyncMock()
            mock_session = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value = mock_session
            mock_engine_factory.return_value = mock_engine
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test portfolio creation
            portfolio_data = {
                "user_id": 1,
                "name": "Test Portfolio",
                "initial_balance": 10000.0,
                "current_balance": 10000.0
            }
            
            portfolio_id = await db_manager.create_portfolio(portfolio_data)
            assert portfolio_id is not None
            
            # Test order creation
            order_data = {
                "portfolio_id": portfolio_id,
                "symbol": "BTCUSDT",
                "order_type": "market",
                "side": "buy",
                "quantity": 0.1,
                "price": 45000.0
            }
            
            order_id = await db_manager.create_order(order_data)
            assert order_id is not None
            
            # Test trading signal storage
            signal_data = {
                "agent_id": 1,
                "symbol": "BTCUSDT",
                "action": "buy",
                "strength": 0.8,
                "confidence": 0.75,
                "reasoning": "Strong bullish momentum"
            }
            
            signal_id = await db_manager.store_trading_signal(signal_data)
            assert signal_id is not None

    @pytest.mark.asyncio
    async def test_clickhouse_analytics_operations(self, mock_clickhouse):
        """Test ClickHouse analytics data operations."""
        with patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test market data insertion
            market_data = {
                "timestamp": datetime.utcnow(),
                "symbol": "BTCUSDT",
                "open": 44900.0,
                "high": 45100.0,
                "low": 44800.0,
                "close": 45000.0,
                "volume": 1000.0
            }
            
            await db_manager.insert_market_data(market_data)
            mock_clickhouse.execute.assert_called()
            
            # Test price tick insertion
            tick_data = {
                "timestamp": datetime.utcnow(),
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 100.0,
                "bid": 44995.0,
                "ask": 45005.0
            }
            
            await db_manager.insert_price_tick(tick_data)
            assert mock_clickhouse.execute.call_count >= 2
            
            # Test trade execution logging
            trade_data = {
                "timestamp": datetime.utcnow(),
                "symbol": "BTCUSDT",
                "side": "buy",
                "quantity": 0.1,
                "price": 45000.0,
                "value": 4500.0,
                "fees": 4.5,
                "strategy": "sma_crossover"
            }
            
            await db_manager.log_trade_execution(trade_data)
            assert mock_clickhouse.execute.call_count >= 3

    @pytest.mark.asyncio
    async def test_mongodb_document_operations(self, mock_mongodb):
        """Test MongoDB document storage operations."""
        with patch('src.db.database_manager.motor.motor_asyncio.AsyncIOMotorClient') as mock_mongo_client:
            # Setup MongoDB mock
            mock_mongo_instance = AsyncMock()
            mock_db = AsyncMock()
            mock_collection = AsyncMock()
            
            mock_mongo_instance.admin.command = AsyncMock()
            mock_mongo_instance.__getitem__.return_value = mock_db
            mock_db.__getitem__.return_value = mock_collection
            mock_collection.insert_one.return_value.inserted_id = "test_id_123"
            mock_collection.find.return_value.to_list.return_value = []
            
            mock_mongo_client.return_value = mock_mongo_instance
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test news article insertion
            news_data = {
                "title": "Bitcoin Reaches New High",
                "content": "Bitcoin has reached a new all-time high...",
                "source": "CryptoNews",
                "published_at": datetime.utcnow(),
                "symbols": ["BTCUSDT"],
                "sentiment": "positive"
            }
            
            article_id = await db_manager.insert_news_article(news_data)
            assert article_id == "test_id_123"
            
            # Test agent log insertion
            log_data = {
                "agent_name": "market_watcher",
                "level": "INFO",
                "message": "Market analysis completed",
                "timestamp": datetime.utcnow(),
                "metadata": {"symbol": "BTCUSDT", "analysis_time": 0.05}
            }
            
            log_id = await db_manager.insert_agent_log(log_data)
            assert log_id is not None
            
            # Test research report storage
            report_data = {
                "title": "Weekly Market Analysis",
                "content": "Comprehensive analysis of market trends...",
                "author": "chief_analyst",
                "created_at": datetime.utcnow(),
                "symbols_analyzed": ["BTCUSDT", "ETHUSDT"],
                "conclusions": ["Bullish trend", "High volatility expected"]
            }
            
            report_id = await db_manager.store_research_report(report_data)
            assert report_id is not None

    @pytest.mark.asyncio
    async def test_redis_caching_operations(self, mock_redis):
        """Test Redis caching operations."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis):
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test market data caching
            market_data = {
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 1000000.0,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await db_manager.cache_market_data("BTCUSDT", market_data, ttl=300)
            mock_redis.setex.assert_called_with(
                "market_data:BTCUSDT", 
                300, 
                json.dumps(market_data, default=str)
            )
            
            # Test portfolio state caching
            portfolio_state = {
                "portfolio_id": 1,
                "total_value": 15000.0,
                "positions": [
                    {"symbol": "BTCUSDT", "quantity": 0.2, "value": 9000.0},
                    {"symbol": "ETHUSDT", "quantity": 2.0, "value": 5600.0}
                ],
                "cash_balance": 400.0
            }
            
            await db_manager.cache_portfolio_state(1, portfolio_state, ttl=60)
            mock_redis.setex.assert_called_with(
                "portfolio:1",
                60,
                json.dumps(portfolio_state, default=str)
            )
            
            # Test trading signal caching
            signal_data = {
                "symbol": "BTCUSDT",
                "action": "BUY",
                "strength": 0.8,
                "confidence": 0.75,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await db_manager.cache_trading_signal("BTCUSDT", signal_data, ttl=120)
            mock_redis.setex.assert_called_with(
                "signal:BTCUSDT",
                120,
                json.dumps(signal_data, default=str)
            )

    @pytest.mark.asyncio
    async def test_cross_database_data_consistency(self, mock_redis, mock_postgres, mock_clickhouse, mock_mongodb):
        """Test data consistency across multiple databases."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse), \
             patch('src.db.database_manager.motor.motor_asyncio.AsyncIOMotorClient') as mock_mongo_client:
            
            # Setup mocks
            mock_engine = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value.execute = AsyncMock()
            mock_pg_engine.return_value = mock_engine
            
            mock_mongo_instance = AsyncMock()
            mock_mongo_instance.admin.command = AsyncMock()
            mock_mongo_client.return_value = mock_mongo_instance
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test consistent trade execution across databases
            trade_data = {
                "timestamp": datetime.utcnow(),
                "symbol": "BTCUSDT",
                "side": "buy",
                "quantity": 0.1,
                "price": 45000.0,
                "portfolio_id": 1,
                "strategy": "sma_crossover"
            }
            
            # Store in PostgreSQL (transactional)
            order_data = {
                "portfolio_id": trade_data["portfolio_id"],
                "symbol": trade_data["symbol"],
                "order_type": "market",
                "side": trade_data["side"],
                "quantity": trade_data["quantity"],
                "price": trade_data["price"],
                "status": "filled"
            }
            await db_manager.create_order(order_data)
            
            # Store in ClickHouse (analytics)
            await db_manager.log_trade_execution(trade_data)
            
            # Cache in Redis (real-time access)
            await db_manager.cache_latest_trade(trade_data["symbol"], trade_data)
            
            # Log in MongoDB (audit trail)
            log_data = {
                "event_type": "trade_executed",
                "trade_data": trade_data,
                "timestamp": trade_data["timestamp"]
            }
            await db_manager.insert_system_log(log_data)
            
            # Verify all databases were called
            mock_clickhouse.execute.assert_called()
            mock_redis.setex.assert_called()

    @pytest.mark.asyncio
    async def test_database_transaction_rollback(self, mock_postgres):
        """Test database transaction rollback on errors."""
        with patch('src.db.database_manager.create_async_engine') as mock_engine_factory:
            # Setup mock to simulate transaction failure
            mock_engine = AsyncMock()
            mock_session = AsyncMock()
            mock_session.execute.side_effect = Exception("Database constraint violation")
            mock_engine.begin.return_value.__aenter__.return_value = mock_session
            mock_engine_factory.return_value = mock_engine
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test transaction rollback
            invalid_order_data = {
                "portfolio_id": 999,  # Non-existent portfolio
                "symbol": "BTCUSDT",
                "order_type": "market",
                "side": "buy",
                "quantity": -1.0,  # Invalid quantity
                "price": 45000.0
            }
            
            with pytest.raises(Exception):
                await db_manager.create_order(invalid_order_data)
            
            # Verify rollback was attempted
            mock_session.execute.assert_called()

    @pytest.mark.asyncio
    async def test_database_connection_recovery(self, mock_redis, mock_clickhouse):
        """Test database connection recovery after failures."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Simulate Redis connection failure
            mock_redis.ping.side_effect = Exception("Connection lost")
            
            # Test connection health check
            health_status = await db_manager.check_connection_health()
            
            # Should detect Redis failure
            assert health_status["redis"] == False
            
            # Simulate recovery
            mock_redis.ping.side_effect = None
            mock_redis.ping.return_value = True
            
            # Test recovery
            health_status = await db_manager.check_connection_health()
            assert health_status["redis"] == True

    @pytest.mark.asyncio
    async def test_database_performance_monitoring(self, mock_redis, mock_clickhouse):
        """Test database performance monitoring."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test performance metrics collection
            performance_metrics = await db_manager.get_performance_metrics()
            
            # Should return metrics for all databases
            assert "redis" in performance_metrics
            assert "clickhouse" in performance_metrics
            assert "postgres" in performance_metrics
            assert "mongodb" in performance_metrics

    @pytest.mark.asyncio
    async def test_database_backup_operations(self, mock_postgres, mock_clickhouse):
        """Test database backup and restore operations."""
        with patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            mock_engine = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value.execute = AsyncMock()
            mock_pg_engine.return_value = mock_engine
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test backup creation
            backup_config = {
                "include_tables": ["orders", "portfolios", "trading_signals"],
                "backup_path": "/tmp/noryon_backup",
                "compression": True
            }
            
            backup_result = await db_manager.create_backup(backup_config)
            assert backup_result is not None

    @pytest.mark.asyncio
    async def test_database_schema_validation(self, mock_postgres, mock_clickhouse, mock_mongodb):
        """Test database schema validation."""
        with patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse), \
             patch('src.db.database_manager.motor.motor_asyncio.AsyncIOMotorClient') as mock_mongo_client:
            
            # Setup mocks
            mock_engine = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value.execute = AsyncMock()
            mock_pg_engine.return_value = mock_engine
            
            mock_mongo_instance = AsyncMock()
            mock_mongo_instance.admin.command = AsyncMock()
            mock_mongo_instance.list_collection_names.return_value = ["news_articles", "agent_logs"]
            mock_mongo_client.return_value = mock_mongo_instance
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test schema validation
            schema_status = await db_manager.validate_schemas()
            
            # Should validate all database schemas
            assert "postgres" in schema_status
            assert "clickhouse" in schema_status
            assert "mongodb" in schema_status

    @pytest.mark.asyncio
    async def test_database_data_migration(self, mock_postgres, mock_clickhouse):
        """Test data migration between databases."""
        with patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            mock_engine = AsyncMock()
            mock_session = AsyncMock()
            mock_session.execute.return_value.fetchall.return_value = [
                (1, "BTCUSDT", "buy", 0.1, 45000.0, datetime.utcnow())
            ]
            mock_engine.begin.return_value.__aenter__.return_value = mock_session
            mock_pg_engine.return_value = mock_engine
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test data migration from PostgreSQL to ClickHouse
            migration_config = {
                "source": "postgres",
                "target": "clickhouse",
                "table": "orders",
                "batch_size": 1000
            }
            
            migration_result = await db_manager.migrate_data(migration_config)
            assert migration_result is not None
