"""
API Endpoint Integration Tests for NORYON V2

Comprehensive testing of all REST API endpoints with real data scenarios,
including market data, AI analysis, agent status, and system health endpoints.
"""

import pytest
import json
import time
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any
from fastapi.testclient import TestClient
from fastapi import FastAP<PERSON>

from src.api.main import app
from src.api.routes.market import router as market_router


class TestAPIEndpoints:
    """Test suite for comprehensive API endpoint verification."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup test environment."""
        self.client = TestClient(app)
        self.test_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
    def test_health_endpoint(self):
        """Test system health endpoint."""
        response = self.client.get("/healthz")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"

    def test_metrics_endpoint(self):
        """Test Prometheus metrics endpoint."""
        response = self.client.get("/metrics")
        
        assert response.status_code == 200
        assert "text/plain" in response.headers["content-type"]
        
        # Should contain Prometheus metrics
        content = response.text
        assert "noryon_" in content or "http_" in content

    @patch('src.api.routes.market.redis_client')
    def test_market_latest_endpoint(self, mock_redis):
        """Test latest market data endpoint."""
        # Setup mock Redis data
        mock_tick_data = {
            "symbol": "BTCUSDT",
            "ts": datetime.utcnow().isoformat(),
            "bid": 44995.0,
            "ask": 45005.0,
            "last": 45000.0,
            "volume": 1000000.0
        }
        mock_redis.get.return_value = json.dumps(mock_tick_data)
        
        response = self.client.get("/market/latest?symbol=BTCUSDT")
        
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "BTCUSDT"
        assert "last" in data
        assert "volume" in data

    @patch('src.api.routes.market.get_client')
    def test_market_candles_endpoint(self, mock_clickhouse):
        """Test candlestick data endpoint."""
        # Setup mock ClickHouse data
        mock_candles = [
            (datetime.utcnow(), 45000.0, 45100.0, 44900.0, 45050.0, 1000.0),
            (datetime.utcnow() - timedelta(minutes=1), 44950.0, 45000.0, 44900.0, 45000.0, 950.0)
        ]
        mock_clickhouse.execute.return_value = mock_candles
        
        response = self.client.get("/market/candles?symbol=BTCUSDT&interval=1m&limit=2")
        
        assert response.status_code == 200
        data = response.json()
        assert "candles" in data
        assert len(data["candles"]) == 2
        
        # Verify candle structure
        candle = data["candles"][0]
        assert "ts" in candle
        assert "open" in candle
        assert "high" in candle
        assert "low" in candle
        assert "close" in candle
        assert "volume" in candle

    @patch('src.api.routes.market.redis_client')
    def test_market_signal_endpoint(self, mock_redis):
        """Test trading signal endpoint."""
        # Setup mock signal data
        mock_signal = {
            "symbol": "BTCUSDT",
            "strategy": "sma_crossover",
            "side": "BUY",
            "price": 45000.0,
            "confidence": 0.8,
            "ts": datetime.utcnow().isoformat()
        }
        mock_redis.get.return_value = json.dumps(mock_signal)
        
        response = self.client.get("/market/signal?symbol=BTCUSDT")
        
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "BTCUSDT"
        assert "signal" in data

    @patch('src.api.routes.market.get_client')
    def test_market_stats_endpoint(self, mock_clickhouse):
        """Test market statistics endpoint."""
        # Setup mock stats data
        mock_stats = [
            ("BTCUSDT", 1000, datetime.utcnow() - timedelta(hours=1), datetime.utcnow(), 45000.0),
            ("ETHUSDT", 800, datetime.utcnow() - timedelta(hours=1), datetime.utcnow(), 2800.0)
        ]
        mock_clickhouse.execute.return_value = mock_stats
        
        response = self.client.get("/market/stats")
        
        assert response.status_code == 200
        data = response.json()
        assert "symbols" in data
        assert "total_symbols" in data
        assert data["total_symbols"] == 2

    @patch('src.api.routes.market.ai_service')
    @patch('src.api.routes.market.redis_client')
    def test_ai_analysis_endpoint(self, mock_redis, mock_ai_service):
        """Test AI analysis endpoint."""
        # Setup mock data
        mock_tick_data = {
            "symbol": "BTCUSDT",
            "last": 45000.0,
            "volume": 1000000.0,
            "ts": datetime.utcnow().isoformat()
        }
        mock_redis.get.return_value = json.dumps(mock_tick_data)
        mock_ai_service.analyze_market_data.return_value = "Strong bullish momentum detected"
        
        response = self.client.get("/market/ai-analysis/BTCUSDT")
        
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "BTCUSDT"
        assert "ai_analysis" in data
        assert "market_data" in data
        assert data["status"] == "success"

    @patch('src.api.routes.market.binance_connector')
    def test_live_data_endpoint(self, mock_binance):
        """Test live market data endpoint."""
        # Setup mock Binance data
        mock_live_data = {
            "BTCUSDT": {"price": 45000.0, "volume": 1000000.0, "change_24h": 2.5},
            "ETHUSDT": {"price": 2800.0, "volume": 500000.0, "change_24h": 1.8}
        }
        mock_binance.get_all_latest.return_value = mock_live_data
        
        response = self.client.get("/market/live-data")
        
        assert response.status_code == 200
        data = response.json()
        assert "symbols" in data
        assert "timestamp" in data
        assert "source" in data

    @patch('src.api.routes.market.agent_manager')
    def test_ai_agents_status_endpoint(self, mock_agent_manager):
        """Test AI agents status endpoint."""
        # Setup mock agent status
        mock_status = {
            "market_watcher": {"running": True, "last_heartbeat": datetime.utcnow().isoformat()},
            "strategy_researcher": {"running": True, "last_heartbeat": datetime.utcnow().isoformat()},
            "risk_officer": {"running": True, "last_heartbeat": datetime.utcnow().isoformat()},
            "technical_analyst": {"running": False, "last_heartbeat": datetime.utcnow().isoformat()}
        }
        mock_agent_manager.get_agent_status.return_value = mock_status
        
        response = self.client.get("/market/ai-agents/status")
        
        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert "total_agents" in data
        assert "active_agents" in data
        assert data["total_agents"] == 4
        assert data["active_agents"] == 3  # 3 running, 1 stopped

    @patch('src.api.routes.market.data_ingestion')
    @patch('src.api.routes.market.binance_connector')
    def test_market_stats_comprehensive_endpoint(self, mock_binance, mock_data_ingestion):
        """Test comprehensive market statistics endpoint."""
        # Setup mock data
        mock_stats = {
            "total_ticks": 10000,
            "symbols_count": 5,
            "avg_processing_time": 0.05
        }
        mock_data_ingestion.get_market_stats.return_value = mock_stats
        
        mock_binance_data = {
            "BTCUSDT": {"price": 45000.0},
            "ETHUSDT": {"price": 2800.0}
        }
        mock_binance.get_all_latest.return_value = mock_binance_data
        
        response = self.client.get("/market/market-stats")
        
        assert response.status_code == 200
        data = response.json()
        assert "clickhouse_stats" in data
        assert "live_symbols" in data
        assert "live_data_available" in data
        assert "status" in data

    def test_api_response_times(self):
        """Test API response time performance."""
        endpoints = [
            "/healthz",
            "/metrics"
        ]
        
        response_times = []
        
        for endpoint in endpoints:
            start_time = time.perf_counter()
            response = self.client.get(endpoint)
            end_time = time.perf_counter()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            assert response.status_code == 200
            assert response_time < 0.1  # Should respond within 100ms

        # Average response time should be fast
        avg_response_time = sum(response_times) / len(response_times)
        assert avg_response_time < 0.05  # 50ms average

    @patch('src.api.routes.market.redis_client')
    def test_api_error_handling(self, mock_redis):
        """Test API error handling for various failure scenarios."""
        # Test Redis connection failure
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        response = self.client.get("/market/latest?symbol=BTCUSDT")
        
        # Should handle error gracefully
        assert response.status_code in [404, 500]  # Appropriate error response
        
        # Test invalid symbol
        mock_redis.get.side_effect = None
        mock_redis.get.return_value = None  # No data found
        
        response = self.client.get("/market/latest?symbol=INVALID")
        assert response.status_code == 404

    def test_api_input_validation(self):
        """Test API input validation."""
        # Test invalid interval for candles
        response = self.client.get("/market/candles?symbol=BTCUSDT&interval=invalid&limit=10")
        assert response.status_code == 400
        
        # Test invalid limit values
        response = self.client.get("/market/candles?symbol=BTCUSDT&interval=1m&limit=0")
        assert response.status_code == 422  # Validation error
        
        response = self.client.get("/market/candles?symbol=BTCUSDT&interval=1m&limit=2000")
        assert response.status_code == 422  # Exceeds maximum

    @patch('src.api.routes.market.redis_client')
    @patch('src.api.routes.market.ai_service')
    def test_api_concurrent_requests(self, mock_ai_service, mock_redis):
        """Test API handling of concurrent requests."""
        import concurrent.futures
        import threading
        
        # Setup mocks
        mock_tick_data = {
            "symbol": "BTCUSDT",
            "last": 45000.0,
            "volume": 1000000.0,
            "ts": datetime.utcnow().isoformat()
        }
        mock_redis.get.return_value = json.dumps(mock_tick_data)
        mock_ai_service.analyze_market_data.return_value = "AI analysis result"
        
        def make_request():
            response = self.client.get("/market/ai-analysis/BTCUSDT")
            return response.status_code, response.elapsed if hasattr(response, 'elapsed') else 0
        
        # Make concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        status_codes = [result[0] for result in results]
        assert all(code == 200 for code in status_codes)

    @patch('src.api.routes.market.redis_client')
    def test_api_data_consistency(self, mock_redis):
        """Test API data consistency across multiple calls."""
        # Setup consistent mock data
        mock_tick_data = {
            "symbol": "BTCUSDT",
            "last": 45000.0,
            "volume": 1000000.0,
            "ts": "2024-01-01T00:00:00Z"
        }
        mock_redis.get.return_value = json.dumps(mock_tick_data)
        
        # Make multiple requests
        responses = []
        for _ in range(5):
            response = self.client.get("/market/latest?symbol=BTCUSDT")
            assert response.status_code == 200
            responses.append(response.json())
        
        # Data should be consistent across calls
        first_response = responses[0]
        for response in responses[1:]:
            assert response["symbol"] == first_response["symbol"]
            assert response["last"] == first_response["last"]
            assert response["volume"] == first_response["volume"]

    def test_api_documentation_endpoints(self):
        """Test API documentation endpoints."""
        # Test OpenAPI schema
        response = self.client.get("/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        
        # Verify market endpoints are documented
        paths = schema["paths"]
        assert "/market/latest" in paths
        assert "/market/candles" in paths
        assert "/market/ai-analysis/{symbol}" in paths

    @patch('src.api.routes.market.redis_client')
    @patch('src.api.routes.market.ai_service')
    def test_api_caching_behavior(self, mock_ai_service, mock_redis):
        """Test API caching behavior."""
        # Setup mock data
        mock_tick_data = {
            "symbol": "BTCUSDT",
            "last": 45000.0,
            "volume": 1000000.0,
            "ts": datetime.utcnow().isoformat()
        }
        mock_redis.get.return_value = json.dumps(mock_tick_data)
        mock_ai_service.analyze_market_data.return_value = "Cached AI analysis"
        
        # Make multiple requests to same endpoint
        for _ in range(3):
            response = self.client.get("/market/ai-analysis/BTCUSDT")
            assert response.status_code == 200
        
        # Redis should be called for each request (no HTTP caching in this test)
        assert mock_redis.get.call_count >= 3

    def test_api_rate_limiting_headers(self):
        """Test API rate limiting headers (if implemented)."""
        response = self.client.get("/healthz")
        
        # Check for rate limiting headers (if implemented)
        headers = response.headers
        
        # These headers might be present if rate limiting is implemented
        rate_limit_headers = [
            "X-RateLimit-Limit",
            "X-RateLimit-Remaining", 
            "X-RateLimit-Reset"
        ]
        
        # Test passes regardless of rate limiting implementation
        assert response.status_code == 200

    @patch('src.api.routes.market.redis_client')
    def test_api_cross_origin_requests(self, mock_redis):
        """Test API CORS handling."""
        # Setup mock data
        mock_redis.get.return_value = json.dumps({"symbol": "BTCUSDT", "price": 45000.0})
        
        # Test preflight request
        response = self.client.options("/market/latest?symbol=BTCUSDT")
        
        # Should handle OPTIONS request
        assert response.status_code in [200, 405]  # Either allowed or method not allowed

    def test_api_security_headers(self):
        """Test API security headers."""
        response = self.client.get("/healthz")
        
        headers = response.headers
        
        # Check for basic security headers
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection"
        ]
        
        # Test passes regardless of security header implementation
        assert response.status_code == 200
        
        # Content-Type should be properly set
        assert "application/json" in headers.get("content-type", "")
