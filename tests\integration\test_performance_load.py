"""
Performance and Load Testing for NORYON V2

Tests that verify the system can handle production-level load and concurrent requests,
measuring throughput, latency, memory usage, and scalability.
"""

import pytest
import asyncio
import time
import psutil
import concurrent.futures
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List
from statistics import mean, median

from src.services.ai_service import AIService
from src.services.market_simulator import MarketBroadcaster
from src.services.data_ingestion import DataIngestionService
from src.agents.agent_manager import AgentManager
from src.db.database_manager import DatabaseManager
from fastapi.testclient import TestClient
from src.api.main import app


class TestPerformanceLoad:
    """Test suite for performance and load testing."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.performance_metrics = {
            "response_times": [],
            "throughput": [],
            "memory_usage": [],
            "cpu_usage": [],
            "error_rates": []
        }
        
    def measure_system_resources(self):
        """Measure current system resource usage."""
        process = psutil.Process()
        return {
            "memory_mb": process.memory_info().rss / 1024 / 1024,
            "cpu_percent": process.cpu_percent(),
            "threads": process.num_threads()
        }

    @pytest.mark.asyncio
    async def test_ai_service_throughput(self, mock_ai_service):
        """Test AI service throughput under load."""
        # Setup mock for fast responses
        mock_ai_service.analyze_market_data.return_value = "Fast AI analysis result"
        
        # Test concurrent AI requests
        concurrent_requests = 50
        request_data = {
            "symbol": "BTCUSDT",
            "price": 45000.0,
            "volume": 1000000.0
        }
        
        async def make_ai_request():
            start_time = time.perf_counter()
            result = await mock_ai_service.analyze_market_data("BTCUSDT", request_data)
            end_time = time.perf_counter()
            return end_time - start_time, result is not None
        
        # Execute concurrent requests
        start_time = time.perf_counter()
        tasks = [make_ai_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        end_time = time.perf_counter()
        
        # Calculate metrics
        response_times = [r[0] for r in results]
        success_rate = sum(1 for r in results if r[1]) / len(results)
        total_time = end_time - start_time
        throughput = concurrent_requests / total_time
        
        # Performance assertions
        assert success_rate >= 0.95  # 95% success rate
        assert mean(response_times) < 0.1  # Average < 100ms
        assert max(response_times) < 0.5   # Max < 500ms
        assert throughput > 50  # > 50 requests/second

    @pytest.mark.asyncio
    async def test_market_data_ingestion_throughput(self, mock_clickhouse, mock_redis):
        """Test market data ingestion throughput."""
        with patch('src.services.data_ingestion.get_client', return_value=mock_clickhouse), \
             patch('src.db.redis.get_client', return_value=mock_redis):
            
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            
            # Generate high-volume test data
            test_data_batch = []
            symbols = [f"TEST{i}USDT" for i in range(10)]
            
            for i in range(1000):  # 1000 ticks
                tick = {
                    "symbol": symbols[i % len(symbols)],
                    "price": 1000.0 + (i * 0.1),
                    "volume": 100.0 + (i * 0.01),
                    "timestamp": datetime.utcnow() + timedelta(microseconds=i*1000)
                }
                test_data_batch.append(tick)
            
            # Measure ingestion performance
            start_time = time.perf_counter()
            
            # Process in batches
            batch_size = 100
            for i in range(0, len(test_data_batch), batch_size):
                batch = test_data_batch[i:i+batch_size]
                await data_ingestion.ingest_batch(batch)
            
            end_time = time.perf_counter()
            
            # Calculate metrics
            total_time = end_time - start_time
            throughput = len(test_data_batch) / total_time
            
            # Performance assertions
            assert throughput > 500  # > 500 ticks/second
            assert total_time < 5.0  # Complete within 5 seconds

    @pytest.mark.asyncio
    async def test_api_endpoint_load(self):
        """Test API endpoint performance under load."""
        client = TestClient(app)
        
        # Test concurrent API requests
        def make_api_request():
            start_time = time.perf_counter()
            response = client.get("/healthz")
            end_time = time.perf_counter()
            return {
                "response_time": end_time - start_time,
                "status_code": response.status_code,
                "success": response.status_code == 200
            }
        
        # Execute concurrent requests
        concurrent_requests = 100
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(make_api_request) for _ in range(concurrent_requests)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Calculate metrics
        response_times = [r["response_time"] for r in results]
        success_rate = sum(1 for r in results if r["success"]) / len(results)
        
        # Performance assertions
        assert success_rate >= 0.99  # 99% success rate
        assert mean(response_times) < 0.05  # Average < 50ms
        assert max(response_times) < 0.2    # Max < 200ms
        assert len([t for t in response_times if t > 0.1]) < 5  # < 5% over 100ms

    @pytest.mark.asyncio
    async def test_database_connection_pool_performance(self, mock_redis, mock_clickhouse):
        """Test database connection pool performance."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse):
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Test concurrent database operations
            async def database_operation():
                start_time = time.perf_counter()
                
                # Simulate typical database operations
                await db_manager.cache_market_data("BTCUSDT", {"price": 45000.0})
                
                end_time = time.perf_counter()
                return end_time - start_time
            
            # Execute concurrent operations
            concurrent_ops = 200
            tasks = [database_operation() for _ in range(concurrent_ops)]
            operation_times = await asyncio.gather(*tasks)
            
            # Performance assertions
            assert mean(operation_times) < 0.01  # Average < 10ms
            assert max(operation_times) < 0.05   # Max < 50ms

    @pytest.mark.asyncio
    async def test_agent_coordination_scalability(self, mock_ai_service, mock_redis):
        """Test agent coordination performance with multiple symbols."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster, \
             patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            # Setup large-scale market data
            symbols = [f"SYMBOL{i}USDT" for i in range(50)]  # 50 symbols
            market_data = {}
            
            for symbol in symbols:
                market_data[symbol] = {
                    "price": 1000.0 + (hash(symbol) % 1000),
                    "change_24h": (hash(symbol) % 20) - 10,  # -10% to +10%
                    "volume": 1000000.0 + (hash(symbol) % 1000000)
                }
            
            mock_broadcaster.get_all_latest.return_value = market_data
            mock_ai_service.analyze_market_data.return_value = "Scalable analysis"
            
            agent_manager = AgentManager()
            
            # Measure agent startup time
            start_time = time.perf_counter()
            await agent_manager.start_all_agents()
            startup_time = time.perf_counter() - start_time
            
            # Let agents process large dataset
            processing_start = time.perf_counter()
            await asyncio.sleep(5)  # Allow processing time
            processing_time = time.perf_counter() - processing_start
            
            # Verify agents handled the load
            status = agent_manager.get_agent_status()
            assert len(status) == 4
            for agent_name, agent_status in status.items():
                assert agent_status.get("running", False) == True
            
            # Performance assertions
            assert startup_time < 10.0  # Startup within 10 seconds
            assert processing_time < 10.0  # Processing within time limit
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, mock_ai_service):
        """Test memory usage during high-load operations."""
        initial_memory = self.measure_system_resources()["memory_mb"]
        
        # Simulate memory-intensive operations
        large_datasets = []
        
        for i in range(10):
            # Create large market data sets
            dataset = {
                f"SYMBOL{j}USDT": {
                    "price": 1000.0 + j,
                    "volume": 1000000.0,
                    "history": [1000.0 + k for k in range(1000)]  # Large history
                }
                for j in range(100)  # 100 symbols per dataset
            }
            large_datasets.append(dataset)
            
            # Process with AI service
            for symbol, data in list(dataset.items())[:5]:  # Process first 5
                await mock_ai_service.analyze_market_data(symbol, data)
            
            # Measure memory usage
            current_memory = self.measure_system_resources()["memory_mb"]
            memory_increase = current_memory - initial_memory
            
            # Memory should not grow excessively
            assert memory_increase < 500  # < 500MB increase
        
        # Clean up
        large_datasets.clear()

    @pytest.mark.asyncio
    async def test_concurrent_user_simulation(self, mock_redis, mock_ai_service):
        """Test system performance with multiple concurrent users."""
        client = TestClient(app)
        
        with patch('src.api.routes.market.redis_client', mock_redis), \
             patch('src.api.routes.market.ai_service', mock_ai_service):
            
            # Setup mock data
            mock_tick_data = {
                "symbol": "BTCUSDT",
                "last": 45000.0,
                "volume": 1000000.0,
                "ts": datetime.utcnow().isoformat()
            }
            mock_redis.get.return_value = json.dumps(mock_tick_data)
            mock_ai_service.analyze_market_data.return_value = "Concurrent analysis"
            
            # Simulate multiple users with different usage patterns
            def simulate_user(user_id):
                user_metrics = {
                    "requests": 0,
                    "total_time": 0,
                    "errors": 0
                }
                
                # Each user makes multiple requests
                for _ in range(10):
                    start_time = time.perf_counter()
                    
                    try:
                        # Mix of different endpoints
                        if user_id % 3 == 0:
                            response = client.get("/healthz")
                        elif user_id % 3 == 1:
                            response = client.get("/market/ai-analysis/BTCUSDT")
                        else:
                            response = client.get("/metrics")
                        
                        if response.status_code != 200:
                            user_metrics["errors"] += 1
                            
                    except Exception:
                        user_metrics["errors"] += 1
                    
                    end_time = time.perf_counter()
                    user_metrics["total_time"] += (end_time - start_time)
                    user_metrics["requests"] += 1
                    
                    # Brief pause between requests
                    time.sleep(0.1)
                
                return user_metrics
            
            # Simulate 20 concurrent users
            concurrent_users = 20
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(simulate_user, i) for i in range(concurrent_users)]
                user_results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            # Aggregate metrics
            total_requests = sum(r["requests"] for r in user_results)
            total_errors = sum(r["errors"] for r in user_results)
            avg_response_time = sum(r["total_time"] for r in user_results) / total_requests
            error_rate = total_errors / total_requests if total_requests > 0 else 0
            
            # Performance assertions
            assert error_rate < 0.05  # < 5% error rate
            assert avg_response_time < 0.1  # < 100ms average response time

    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, mock_ai_service):
        """Test system performance under sustained load."""
        # Run sustained load for extended period
        duration_seconds = 30
        request_interval = 0.1  # 10 requests/second
        
        performance_samples = []
        start_time = time.perf_counter()
        
        while (time.perf_counter() - start_time) < duration_seconds:
            sample_start = time.perf_counter()
            
            # Make AI request
            result = await mock_ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            
            sample_end = time.perf_counter()
            
            # Record performance sample
            performance_samples.append({
                "response_time": sample_end - sample_start,
                "memory_mb": self.measure_system_resources()["memory_mb"],
                "success": result is not None
            })
            
            await asyncio.sleep(request_interval)
        
        # Analyze sustained performance
        response_times = [s["response_time"] for s in performance_samples]
        memory_usage = [s["memory_mb"] for s in performance_samples]
        success_rate = sum(1 for s in performance_samples if s["success"]) / len(performance_samples)
        
        # Performance assertions
        assert success_rate >= 0.95  # 95% success rate
        assert mean(response_times) < 0.1  # Stable response times
        assert max(memory_usage) - min(memory_usage) < 100  # Stable memory usage

    @pytest.mark.asyncio
    async def test_burst_traffic_handling(self, mock_ai_service):
        """Test system handling of burst traffic patterns."""
        # Simulate traffic bursts
        burst_sizes = [10, 50, 100, 200]
        burst_results = []
        
        for burst_size in burst_sizes:
            # Create burst of requests
            burst_start = time.perf_counter()
            
            tasks = []
            for _ in range(burst_size):
                task = mock_ai_service.analyze_market_data("BTCUSDT", {
                    "price": 45000.0,
                    "volume": 1000000.0
                })
                tasks.append(task)
            
            # Execute burst
            results = await asyncio.gather(*tasks, return_exceptions=True)
            burst_end = time.perf_counter()
            
            # Analyze burst performance
            burst_time = burst_end - burst_start
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            success_rate = success_count / len(results)
            throughput = burst_size / burst_time
            
            burst_results.append({
                "size": burst_size,
                "time": burst_time,
                "success_rate": success_rate,
                "throughput": throughput
            })
            
            # Brief pause between bursts
            await asyncio.sleep(1)
        
        # Verify burst handling
        for result in burst_results:
            assert result["success_rate"] >= 0.9  # 90% success rate
            assert result["throughput"] > 50  # Minimum throughput

    @pytest.mark.asyncio
    async def test_resource_cleanup_performance(self, mock_redis):
        """Test resource cleanup and garbage collection performance."""
        with patch('src.agents.base.aioredis.from_url', return_value=mock_redis):
            
            initial_resources = self.measure_system_resources()
            
            # Create and destroy multiple agent managers
            for i in range(5):
                agent_manager = AgentManager()
                await agent_manager.start_all_agents()
                
                # Let agents run briefly
                await asyncio.sleep(1)
                
                # Stop and cleanup
                await agent_manager.stop_all_agents()
                
                # Force garbage collection
                import gc
                gc.collect()
                
                # Measure resources after cleanup
                current_resources = self.measure_system_resources()
                
                # Resources should not accumulate significantly
                memory_increase = current_resources["memory_mb"] - initial_resources["memory_mb"]
                assert memory_increase < 50  # < 50MB increase per iteration

    @pytest.mark.asyncio
    async def test_latency_percentiles(self, mock_ai_service):
        """Test latency percentile distribution."""
        # Collect latency samples
        latency_samples = []
        
        for _ in range(1000):  # 1000 samples
            start_time = time.perf_counter()
            await mock_ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            end_time = time.perf_counter()
            
            latency_samples.append(end_time - start_time)
        
        # Calculate percentiles
        latency_samples.sort()
        p50 = latency_samples[int(0.50 * len(latency_samples))]
        p95 = latency_samples[int(0.95 * len(latency_samples))]
        p99 = latency_samples[int(0.99 * len(latency_samples))]
        
        # Latency assertions
        assert p50 < 0.05   # 50th percentile < 50ms
        assert p95 < 0.1    # 95th percentile < 100ms
        assert p99 < 0.2    # 99th percentile < 200ms
