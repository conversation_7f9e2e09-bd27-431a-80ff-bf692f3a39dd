"""
Component Integration Tests for NORYON V2 AI Trading System

Tests that verify all system components can communicate and exchange data properly.
This includes AI Service, Market Data, Agents, API Server, and Database components.
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from src.services.ai_service import AIService
from src.services.market_simulator import MarketBroadcaster
from src.services.data_ingestion import DataIngestionService
from src.services.binance_connector import BinanceConnector
from src.agents.agent_manager import AgentManager
from src.db.database_manager import DatabaseManager
from src.core.orchestrator import SystemOrchestrator
from src.core.config import get_settings


class TestComponentIntegration:
    """Test suite for component integration verification."""

    @pytest.fixture(autouse=True)
    async def setup(self):
        """Setup test environment."""
        self.settings = get_settings()
        self.test_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        
    @pytest.mark.asyncio
    async def test_ai_service_integration(self, mock_ai_service):
        """Test AI Service can process requests and return responses."""
        ai_service = AIService()
        
        # Mock Ollama response
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "response": "Market analysis: Strong bullish momentum detected for BTCUSDT"
            }
            mock_response.status = 200
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # Test market analysis
            result = await ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0,
                "change_24h": 2.5
            })
            
            assert result is not None
            assert "BTCUSDT" in result or "market" in result.lower()
            mock_post.assert_called_once()

    @pytest.mark.asyncio
    async def test_market_data_integration(self):
        """Test market data services can generate and distribute data."""
        # Test market simulator
        market_broadcaster = MarketBroadcaster()
        await market_broadcaster.start()
        
        # Wait for data generation
        await asyncio.sleep(2)
        
        latest_data = market_broadcaster.get_all_latest()
        assert len(latest_data) > 0
        
        # Verify data structure
        for symbol, data in latest_data.items():
            assert "price" in data
            assert "volume" in data
            assert "timestamp" in data
            assert data["price"] > 0
            assert data["volume"] > 0
            
        await market_broadcaster.stop()

    @pytest.mark.asyncio
    async def test_database_manager_integration(self, mock_redis, mock_postgres, mock_clickhouse, mock_mongodb):
        """Test database manager can connect to all database systems."""
        with patch('src.db.database_manager.aioredis.from_url', return_value=mock_redis), \
             patch('src.db.database_manager.create_async_engine') as mock_pg_engine, \
             patch('src.db.database_manager.aioclickhouse.create_client', return_value=mock_clickhouse), \
             patch('src.db.database_manager.motor.motor_asyncio.AsyncIOMotorClient') as mock_mongo_client:
            
            # Setup mocks
            mock_pg_engine.return_value.begin.return_value.__aenter__.return_value.execute = AsyncMock()
            mock_mongo_client.return_value.admin.command = AsyncMock()
            mock_clickhouse.execute = MagicMock()
            
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            # Verify all connections are established
            assert db_manager.connection_status["redis"] == True
            assert db_manager.connection_status["postgres"] == True
            assert db_manager.connection_status["clickhouse"] == True
            assert db_manager.connection_status["mongodb"] == True

    @pytest.mark.asyncio
    async def test_agent_manager_integration(self, mock_ai_service):
        """Test agent manager can start and coordinate AI agents."""
        with patch('src.agents.agent_manager.ai_service', mock_ai_service), \
             patch('src.agents.agent_manager.market_broadcaster') as mock_broadcaster:
            
            # Setup mock market data
            mock_broadcaster.get_all_latest.return_value = {
                "BTCUSDT": {"price": 45000.0, "change_24h": 2.5, "volume": 1000000.0}
            }
            
            agent_manager = AgentManager()
            await agent_manager.start_all_agents()
            
            # Verify agents are running
            assert agent_manager.running == True
            assert len(agent_manager.agents) == 4
            
            # Test agent status
            status = agent_manager.get_agent_status()
            assert "market_watcher" in status
            assert "strategy_researcher" in status
            assert "risk_officer" in status
            assert "technical_analyst" in status
            
            await agent_manager.stop_all_agents()

    @pytest.mark.asyncio
    async def test_data_ingestion_integration(self, mock_clickhouse):
        """Test data ingestion service can process and store market data."""
        with patch('src.services.data_ingestion.get_client', return_value=mock_clickhouse):
            data_ingestion = DataIngestionService()
            await data_ingestion.initialize()
            
            # Test tick data ingestion
            tick_data = {
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 1000.0,
                "bid": 44995.0,
                "ask": 45005.0,
                "timestamp": datetime.utcnow()
            }
            
            await data_ingestion.ingest_tick(tick_data)
            
            # Verify ClickHouse insert was called
            mock_clickhouse.execute.assert_called()
            
            # Test batch ingestion
            batch_data = [tick_data for _ in range(10)]
            await data_ingestion.ingest_batch(batch_data)
            
            # Verify batch insert
            assert mock_clickhouse.execute.call_count >= 2

    @pytest.mark.asyncio
    async def test_binance_connector_integration(self):
        """Test Binance connector can fetch real market data."""
        binance_connector = BinanceConnector()
        
        # Test with mock to avoid real API calls
        with patch('ccxt.binance') as mock_binance:
            mock_exchange = AsyncMock()
            mock_exchange.fetch_ticker.return_value = {
                "symbol": "BTC/USDT",
                "last": 45000.0,
                "bid": 44995.0,
                "ask": 45005.0,
                "baseVolume": 1000.0,
                "percentage": 2.5
            }
            mock_binance.return_value = mock_exchange
            
            await binance_connector.initialize()
            
            # Test single symbol fetch
            ticker = await binance_connector.get_ticker("BTCUSDT")
            assert ticker is not None
            assert "last" in ticker
            
            # Test multiple symbols
            tickers = await binance_connector.get_multiple_tickers(["BTCUSDT", "ETHUSDT"])
            assert len(tickers) > 0

    @pytest.mark.asyncio
    async def test_system_orchestrator_integration(self, mock_redis, mock_ai_service):
        """Test system orchestrator can coordinate all components."""
        with patch('src.core.orchestrator.aioredis.from_url', return_value=mock_redis), \
             patch('src.services.ai_service.ai_service', mock_ai_service):
            
            # Mock agents
            mock_agents = {
                "market_watcher": AsyncMock(),
                "strategy_researcher": AsyncMock(),
                "risk_officer": AsyncMock(),
                "technical_analyst": AsyncMock()
            }
            
            # Mock database and exchange managers
            mock_db_manager = AsyncMock()
            mock_exchange_manager = AsyncMock()
            mock_config = MagicMock()
            
            orchestrator = SystemOrchestrator(
                agents=mock_agents,
                db_manager=mock_db_manager,
                exchange_manager=mock_exchange_manager,
                config=mock_config
            )
            
            await orchestrator.initialize()
            await orchestrator.start()
            
            # Verify orchestrator is running
            assert orchestrator.running == True
            
            # Test message sending
            from src.core.models import AgentMessage
            test_message = AgentMessage(
                source="test",
                target="market_watcher",
                message_type="test_message",
                content={"test": "data"},
                timestamp=datetime.utcnow()
            )
            
            await orchestrator.send_message(test_message)
            
            # Verify message was queued
            assert orchestrator.message_queue.qsize() > 0
            
            await orchestrator.stop()

    @pytest.mark.asyncio
    async def test_cross_component_data_flow(self, mock_redis, mock_clickhouse, mock_ai_service):
        """Test data flow between multiple components."""
        with patch('src.db.redis.get_client', return_value=mock_redis), \
             patch('src.db.clickhouse.get_client', return_value=mock_clickhouse), \
             patch('src.services.ai_service.ai_service', mock_ai_service):
            
            # Simulate market data → AI analysis → storage flow
            
            # 1. Market data generation
            market_data = {
                "symbol": "BTCUSDT",
                "price": 45000.0,
                "volume": 1000000.0,
                "change_24h": 2.5,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 2. Store in Redis (cache)
            await mock_redis.setex(
                f"tick:latest:{market_data['symbol']}", 
                300, 
                json.dumps(market_data, default=str)
            )
            
            # 3. AI analysis
            analysis = await mock_ai_service.analyze_market_data(
                market_data["symbol"], 
                market_data
            )
            
            # 4. Store analysis in ClickHouse
            analysis_data = {
                "symbol": market_data["symbol"],
                "timestamp": market_data["timestamp"],
                "analysis": analysis,
                "confidence": 0.8
            }
            
            # Verify all components were called
            mock_redis.setex.assert_called()
            mock_ai_service.analyze_market_data.assert_called_once()
            
            # Verify data integrity
            assert analysis is not None
            assert len(analysis) > 0

    @pytest.mark.asyncio
    async def test_component_error_handling(self, mock_redis, mock_ai_service):
        """Test component behavior when other components fail."""
        # Test AI service fallback when Ollama is unavailable
        with patch('aiohttp.ClientSession.post', side_effect=Exception("Connection failed")):
            ai_service = AIService()
            
            result = await ai_service.analyze_market_data("BTCUSDT", {
                "price": 45000.0,
                "volume": 1000000.0
            })
            
            # Should return fallback message, not crash
            assert result is not None
            assert "error" in result.lower() or "timeout" in result.lower()
        
        # Test Redis fallback
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        # Component should handle Redis failure gracefully
        try:
            await mock_redis.get("test_key")
        except Exception:
            pass  # Expected to fail, but shouldn't crash the system

    @pytest.mark.asyncio
    async def test_component_health_monitoring(self):
        """Test component health monitoring and status reporting."""
        # Test individual component health
        components_health = {}
        
        # AI Service health
        try:
            ai_service = AIService()
            # Mock successful health check
            components_health["ai_service"] = True
        except Exception:
            components_health["ai_service"] = False
        
        # Market Data health
        try:
            market_broadcaster = MarketBroadcaster()
            components_health["market_data"] = True
        except Exception:
            components_health["market_data"] = False
        
        # Database health (mocked)
        components_health["database"] = True
        
        # Verify health status
        assert "ai_service" in components_health
        assert "market_data" in components_health
        assert "database" in components_health
        
        # At least some components should be healthy
        healthy_components = sum(1 for status in components_health.values() if status)
        assert healthy_components > 0
