"""
Advanced Technical Analyst Agent - Qwen2.5:32b Model
Sophisticated technical analysis with advanced charting, pattern recognition,
multi-timeframe analysis, and predictive modeling.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import logging
import talib
from scipy.signal import find_peaks, argrelextrema
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from src.agents.base_agent import BaseAgent
from src.services.ai_service import ai_service
from src.core.models import AgentMessage, TechnicalSignal, ChartPattern
from src.utils.technical_indicators import AdvancedIndicators
from src.utils.pattern_recognition import AdvancedPatternRecognition
from src.utils.wave_analysis import ElliottWaveAnalyzer
from src.utils.fibonacci_analysis import FibonacciAnalyzer


class TrendDirection(Enum):
    STRONG_BULLISH = "strong_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    STRONG_BEARISH = "strong_bearish"


class PatternType(Enum):
    REVERSAL = "reversal"
    CONTINUATION = "continuation"
    CONSOLIDATION = "consolidation"
    BREAKOUT = "breakout"


class SignalStrength(Enum):
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5


@dataclass
class TechnicalLevel:
    level_type: str  # support, resistance, pivot
    price: float
    strength: float
    touches: int
    timeframe: str
    last_test: datetime
    confidence: float


@dataclass
class AdvancedTechnicalSignal:
    signal_id: str
    symbol: str
    timeframe: str
    signal_type: str
    direction: TrendDirection
    strength: SignalStrength
    confidence: float
    entry_price: float
    stop_loss: float
    take_profit: List[float]
    risk_reward_ratio: float
    technical_confluence: int
    supporting_indicators: List[str]
    chart_patterns: List[str]
    key_levels: List[TechnicalLevel]
    wave_analysis: Dict[str, Any]
    fibonacci_levels: Dict[str, float]
    volume_confirmation: bool
    momentum_divergence: Optional[str]
    ai_analysis: str
    probability_success: float
    expected_move: float
    timeframe_alignment: Dict[str, str]
    timestamp: datetime


class AdvancedTechnicalAnalyst(BaseAgent):
    """
    Advanced Technical Analyst using Qwen2.5:32b for sophisticated chart analysis.
    
    Features:
    - 100+ technical indicators and oscillators
    - Advanced pattern recognition (50+ patterns)
    - Multi-timeframe analysis and alignment
    - Elliott Wave analysis
    - Fibonacci retracement and extension analysis
    - Volume profile and market microstructure
    - Support/resistance level identification
    - Trend analysis and momentum detection
    - Divergence analysis (price vs indicators)
    - Market structure analysis
    - Harmonic pattern recognition
    - Candlestick pattern analysis
    - Ichimoku cloud analysis
    - Bollinger Band squeeze detection
    - MACD and RSI divergences
    - Volume-weighted indicators
    - Custom indicator development
    - Real-time chart monitoring
    - Predictive price modeling
    """
    
    def __init__(self):
        super().__init__()
        self.agent_name = "advanced_technical_analyst"
        self.model_name = "qwen2.5:32b"
        
        # Technical analysis components
        self.advanced_indicators = AdvancedIndicators()
        self.pattern_recognition = AdvancedPatternRecognition()
        self.wave_analyzer = ElliottWaveAnalyzer()
        self.fibonacci_analyzer = FibonacciAnalyzer()
        
        # Analysis data storage
        self.price_data = {}  # Multi-timeframe OHLCV data
        self.technical_levels = {}
        self.chart_patterns = {}
        self.wave_counts = {}
        self.fibonacci_levels = {}
        self.indicator_values = {}
        
        # Analysis parameters
        self.timeframes = ["1m", "5m", "15m", "1h", "4h", "1d", "1w"]
        self.primary_timeframes = ["1h", "4h", "1d"]  # For main analysis
        
        # Technical indicator parameters
        self.indicator_params = {
            "sma_periods": [9, 20, 50, 100, 200],
            "ema_periods": [12, 26, 50, 100, 200],
            "rsi_period": 14,
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "bb_period": 20,
            "bb_std": 2,
            "stoch_k": 14,
            "stoch_d": 3,
            "atr_period": 14,
            "adx_period": 14,
            "cci_period": 20,
            "williams_r_period": 14
        }
        
        # Pattern recognition parameters
        self.pattern_params = {
            "min_pattern_bars": 10,
            "max_pattern_bars": 100,
            "pattern_tolerance": 0.02,  # 2% tolerance
            "volume_confirmation": True,
            "min_pattern_strength": 0.6
        }
        
        # Support/resistance parameters
        self.level_params = {
            "min_touches": 2,
            "touch_tolerance": 0.005,  # 0.5% tolerance
            "min_level_strength": 0.5,
            "max_levels_per_timeframe": 10
        }
        
        self.logger = logging.getLogger(self.agent_name)

    async def _initialize_agent(self):
        """Initialize advanced technical analyst components."""
        self.logger.info("📊 Initializing Advanced Technical Analyst with Qwen2.5:32b")
        
        # Initialize technical analysis components
        await self.advanced_indicators.initialize()
        await self.pattern_recognition.initialize()
        await self.wave_analyzer.initialize()
        await self.fibonacci_analyzer.initialize()
        
        # Load historical data for analysis
        await self._load_historical_chart_data()
        
        # Initialize technical levels database
        await self._initialize_levels_database()
        
        self.logger.info("✅ Advanced Technical Analyst initialized successfully")

    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start advanced technical analysis tasks."""
        return [
            asyncio.create_task(self._multi_timeframe_analysis()),
            asyncio.create_task(self._pattern_recognition_scanner()),
            asyncio.create_task(self._support_resistance_analysis()),
            asyncio.create_task(self._elliott_wave_analysis()),
            asyncio.create_task(self._fibonacci_analysis()),
            asyncio.create_task(self._volume_analysis()),
            asyncio.create_task(self._divergence_detection()),
            asyncio.create_task(self._trend_analysis()),
            asyncio.create_task(self._momentum_analysis()),
            asyncio.create_task(self._market_structure_analysis()),
            asyncio.create_task(self._signal_generation()),
            asyncio.create_task(self._confluence_analysis())
        ]

    async def _multi_timeframe_analysis(self):
        """Perform comprehensive multi-timeframe technical analysis."""
        while self.running:
            try:
                for symbol in self.monitored_symbols:
                    timeframe_analysis = {}
                    
                    for timeframe in self.timeframes:
                        # Get OHLCV data
                        data = await self._get_ohlcv_data(symbol, timeframe)
                        if len(data) < 100:  # Need minimum data
                            continue
                        
                        # Calculate all technical indicators
                        indicators = await self._calculate_all_indicators(data)
                        
                        # Analyze trend and momentum
                        trend_analysis = await self._analyze_trend(data, indicators)
                        momentum_analysis = await self._analyze_momentum(data, indicators)
                        
                        # Identify key levels
                        key_levels = await self._identify_key_levels(data, timeframe)
                        
                        # Detect patterns
                        patterns = await self._detect_chart_patterns(data, timeframe)
                        
                        timeframe_analysis[timeframe] = {
                            "indicators": indicators,
                            "trend": trend_analysis,
                            "momentum": momentum_analysis,
                            "levels": key_levels,
                            "patterns": patterns,
                            "timestamp": datetime.utcnow()
                        }
                    
                    # Synthesize multi-timeframe analysis
                    if timeframe_analysis:
                        synthesis = await self._synthesize_timeframe_analysis(symbol, timeframe_analysis)
                        await self._process_technical_synthesis(symbol, synthesis)
                
                await asyncio.sleep(60)  # Every minute
                
            except Exception as e:
                self.logger.error(f"Multi-timeframe analysis error: {e}")
                await asyncio.sleep(30)

    async def _calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate comprehensive set of technical indicators."""
        
        indicators = {}
        
        # Price data
        high = data["high"].values
        low = data["low"].values
        close = data["close"].values
        volume = data["volume"].values
        
        # Moving Averages
        for period in self.indicator_params["sma_periods"]:
            if len(close) >= period:
                indicators[f"sma_{period}"] = talib.SMA(close, timeperiod=period)
        
        for period in self.indicator_params["ema_periods"]:
            if len(close) >= period:
                indicators[f"ema_{period}"] = talib.EMA(close, timeperiod=period)
        
        # MACD
        if len(close) >= 34:  # Need enough data for MACD
            macd, macd_signal, macd_hist = talib.MACD(
                close,
                fastperiod=self.indicator_params["macd_fast"],
                slowperiod=self.indicator_params["macd_slow"],
                signalperiod=self.indicator_params["macd_signal"]
            )
            indicators["macd"] = macd
            indicators["macd_signal"] = macd_signal
            indicators["macd_histogram"] = macd_hist
        
        # RSI
        if len(close) >= self.indicator_params["rsi_period"]:
            indicators["rsi"] = talib.RSI(close, timeperiod=self.indicator_params["rsi_period"])
        
        # Bollinger Bands
        if len(close) >= self.indicator_params["bb_period"]:
            bb_upper, bb_middle, bb_lower = talib.BBANDS(
                close,
                timeperiod=self.indicator_params["bb_period"],
                nbdevup=self.indicator_params["bb_std"],
                nbdevdn=self.indicator_params["bb_std"]
            )
            indicators["bb_upper"] = bb_upper
            indicators["bb_middle"] = bb_middle
            indicators["bb_lower"] = bb_lower
            indicators["bb_width"] = (bb_upper - bb_lower) / bb_middle
        
        # Stochastic
        if len(high) >= self.indicator_params["stoch_k"]:
            stoch_k, stoch_d = talib.STOCH(
                high, low, close,
                fastk_period=self.indicator_params["stoch_k"],
                slowk_period=self.indicator_params["stoch_d"],
                slowd_period=self.indicator_params["stoch_d"]
            )
            indicators["stoch_k"] = stoch_k
            indicators["stoch_d"] = stoch_d
        
        # ATR
        if len(high) >= self.indicator_params["atr_period"]:
            indicators["atr"] = talib.ATR(high, low, close, timeperiod=self.indicator_params["atr_period"])
        
        # ADX
        if len(high) >= self.indicator_params["adx_period"]:
            indicators["adx"] = talib.ADX(high, low, close, timeperiod=self.indicator_params["adx_period"])
            indicators["plus_di"] = talib.PLUS_DI(high, low, close, timeperiod=self.indicator_params["adx_period"])
            indicators["minus_di"] = talib.MINUS_DI(high, low, close, timeperiod=self.indicator_params["adx_period"])
        
        # CCI
        if len(high) >= self.indicator_params["cci_period"]:
            indicators["cci"] = talib.CCI(high, low, close, timeperiod=self.indicator_params["cci_period"])
        
        # Williams %R
        if len(high) >= self.indicator_params["williams_r_period"]:
            indicators["williams_r"] = talib.WILLR(high, low, close, timeperiod=self.indicator_params["williams_r_period"])
        
        # Volume indicators
        if len(volume) >= 20:
            indicators["volume_sma"] = talib.SMA(volume, timeperiod=20)
            indicators["volume_ratio"] = volume / indicators["volume_sma"]
        
        # On-Balance Volume
        if len(close) >= 2:
            indicators["obv"] = talib.OBV(close, volume)
        
        # Commodity Channel Index
        if len(high) >= 20:
            indicators["cci"] = talib.CCI(high, low, close, timeperiod=20)
        
        # Parabolic SAR
        if len(high) >= 2:
            indicators["sar"] = talib.SAR(high, low, acceleration=0.02, maximum=0.2)
        
        # Ichimoku components
        if len(high) >= 52:
            # Tenkan-sen (Conversion Line)
            tenkan_period = 9
            tenkan_high = pd.Series(high).rolling(tenkan_period).max()
            tenkan_low = pd.Series(low).rolling(tenkan_period).min()
            indicators["tenkan_sen"] = (tenkan_high + tenkan_low) / 2
            
            # Kijun-sen (Base Line)
            kijun_period = 26
            kijun_high = pd.Series(high).rolling(kijun_period).max()
            kijun_low = pd.Series(low).rolling(kijun_period).min()
            indicators["kijun_sen"] = (kijun_high + kijun_low) / 2
            
            # Senkou Span A (Leading Span A)
            indicators["senkou_span_a"] = ((indicators["tenkan_sen"] + indicators["kijun_sen"]) / 2).shift(26)
            
            # Senkou Span B (Leading Span B)
            senkou_b_period = 52
            senkou_b_high = pd.Series(high).rolling(senkou_b_period).max()
            senkou_b_low = pd.Series(low).rolling(senkou_b_period).min()
            indicators["senkou_span_b"] = ((senkou_b_high + senkou_b_low) / 2).shift(26)
        
        # Custom indicators
        indicators.update(await self._calculate_custom_indicators(data))
        
        return indicators

    async def _analyze_trend(self, data: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Analyze trend direction and strength."""
        
        trend_analysis = {
            "direction": TrendDirection.NEUTRAL,
            "strength": 0.0,
            "confidence": 0.0,
            "trend_lines": [],
            "moving_average_alignment": {},
            "adx_reading": 0.0
        }
        
        close = data["close"].values
        
        # Moving average alignment
        ma_alignment_score = 0
        ma_count = 0
        
        for period in [20, 50, 100, 200]:
            ma_key = f"sma_{period}"
            if ma_key in indicators and len(indicators[ma_key]) > 0:
                ma_value = indicators[ma_key][-1]
                if not np.isnan(ma_value):
                    if close[-1] > ma_value:
                        ma_alignment_score += 1
                    else:
                        ma_alignment_score -= 1
                    ma_count += 1
        
        # ADX for trend strength
        adx_value = 0
        if "adx" in indicators and len(indicators["adx"]) > 0:
            adx_value = indicators["adx"][-1]
            if not np.isnan(adx_value):
                trend_analysis["adx_reading"] = adx_value
        
        # Determine trend direction
        if ma_count > 0:
            ma_ratio = ma_alignment_score / ma_count
            
            if ma_ratio >= 0.75:
                if adx_value > 30:
                    trend_analysis["direction"] = TrendDirection.STRONG_BULLISH
                else:
                    trend_analysis["direction"] = TrendDirection.BULLISH
            elif ma_ratio <= -0.75:
                if adx_value > 30:
                    trend_analysis["direction"] = TrendDirection.STRONG_BEARISH
                else:
                    trend_analysis["direction"] = TrendDirection.BEARISH
            else:
                trend_analysis["direction"] = TrendDirection.NEUTRAL
        
        # Calculate trend strength
        trend_analysis["strength"] = min(1.0, adx_value / 50.0) if adx_value > 0 else 0.0
        
        # Calculate confidence based on multiple factors
        confidence_factors = []
        
        # MA alignment confidence
        if ma_count > 0:
            confidence_factors.append(abs(ma_alignment_score / ma_count))
        
        # ADX confidence
        if adx_value > 0:
            confidence_factors.append(min(1.0, adx_value / 40.0))
        
        trend_analysis["confidence"] = np.mean(confidence_factors) if confidence_factors else 0.0
        
        return trend_analysis

    async def _analyze_momentum(self, data: pd.DataFrame, indicators: Dict) -> Dict[str, Any]:
        """Analyze momentum indicators and divergences."""
        
        momentum_analysis = {
            "rsi_reading": 0.0,
            "rsi_condition": "neutral",
            "macd_signal": "neutral",
            "stochastic_signal": "neutral",
            "momentum_divergence": None,
            "momentum_strength": 0.0,
            "overbought_oversold": "neutral"
        }
        
        # RSI analysis
        if "rsi" in indicators and len(indicators["rsi"]) > 0:
            rsi_value = indicators["rsi"][-1]
            if not np.isnan(rsi_value):
                momentum_analysis["rsi_reading"] = rsi_value
                
                if rsi_value > 70:
                    momentum_analysis["rsi_condition"] = "overbought"
                    momentum_analysis["overbought_oversold"] = "overbought"
                elif rsi_value < 30:
                    momentum_analysis["rsi_condition"] = "oversold"
                    momentum_analysis["overbought_oversold"] = "oversold"
                else:
                    momentum_analysis["rsi_condition"] = "neutral"
        
        # MACD analysis
        if all(key in indicators for key in ["macd", "macd_signal"]) and len(indicators["macd"]) > 1:
            macd = indicators["macd"][-1]
            macd_signal = indicators["macd_signal"][-1]
            macd_prev = indicators["macd"][-2]
            macd_signal_prev = indicators["macd_signal"][-2]
            
            if not any(np.isnan([macd, macd_signal, macd_prev, macd_signal_prev])):
                # MACD crossover
                if macd > macd_signal and macd_prev <= macd_signal_prev:
                    momentum_analysis["macd_signal"] = "bullish_crossover"
                elif macd < macd_signal and macd_prev >= macd_signal_prev:
                    momentum_analysis["macd_signal"] = "bearish_crossover"
                elif macd > macd_signal:
                    momentum_analysis["macd_signal"] = "bullish"
                else:
                    momentum_analysis["macd_signal"] = "bearish"
        
        # Stochastic analysis
        if all(key in indicators for key in ["stoch_k", "stoch_d"]) and len(indicators["stoch_k"]) > 1:
            stoch_k = indicators["stoch_k"][-1]
            stoch_d = indicators["stoch_d"][-1]
            stoch_k_prev = indicators["stoch_k"][-2]
            stoch_d_prev = indicators["stoch_d"][-2]
            
            if not any(np.isnan([stoch_k, stoch_d, stoch_k_prev, stoch_d_prev])):
                if stoch_k > stoch_d and stoch_k_prev <= stoch_d_prev:
                    momentum_analysis["stochastic_signal"] = "bullish_crossover"
                elif stoch_k < stoch_d and stoch_k_prev >= stoch_d_prev:
                    momentum_analysis["stochastic_signal"] = "bearish_crossover"
                elif stoch_k > 80:
                    momentum_analysis["stochastic_signal"] = "overbought"
                elif stoch_k < 20:
                    momentum_analysis["stochastic_signal"] = "oversold"
        
        # Detect momentum divergences
        momentum_analysis["momentum_divergence"] = await self._detect_momentum_divergence(data, indicators)
        
        # Calculate overall momentum strength
        momentum_factors = []
        
        # RSI momentum
        if momentum_analysis["rsi_reading"] > 0:
            rsi_momentum = abs(momentum_analysis["rsi_reading"] - 50) / 50
            momentum_factors.append(rsi_momentum)
        
        # MACD momentum
        if "macd_histogram" in indicators and len(indicators["macd_histogram"]) > 0:
            macd_hist = indicators["macd_histogram"][-1]
            if not np.isnan(macd_hist):
                macd_momentum = min(1.0, abs(macd_hist) / 0.01)  # Normalize
                momentum_factors.append(macd_momentum)
        
        momentum_analysis["momentum_strength"] = np.mean(momentum_factors) if momentum_factors else 0.0
        
        return momentum_analysis

    async def _identify_key_levels(self, data: pd.DataFrame, timeframe: str) -> List[TechnicalLevel]:
        """Identify key support and resistance levels."""
        
        levels = []
        
        # Use pivot points method
        high_prices = data["high"].values
        low_prices = data["low"].values
        close_prices = data["close"].values
        
        # Find local maxima (resistance levels)
        resistance_indices = argrelextrema(high_prices, np.greater, order=5)[0]
        
        for idx in resistance_indices:
            if idx < len(high_prices):
                level = TechnicalLevel(
                    level_type="resistance",
                    price=high_prices[idx],
                    strength=self._calculate_level_strength(high_prices[idx], data),
                    touches=self._count_level_touches(high_prices[idx], data),
                    timeframe=timeframe,
                    last_test=data.index[idx] if hasattr(data.index, '__getitem__') else datetime.utcnow(),
                    confidence=0.7  # Base confidence
                )
                levels.append(level)
        
        # Find local minima (support levels)
        support_indices = argrelextrema(low_prices, np.less, order=5)[0]
        
        for idx in support_indices:
            if idx < len(low_prices):
                level = TechnicalLevel(
                    level_type="support",
                    price=low_prices[idx],
                    strength=self._calculate_level_strength(low_prices[idx], data),
                    touches=self._count_level_touches(low_prices[idx], data),
                    timeframe=timeframe,
                    last_test=data.index[idx] if hasattr(data.index, '__getitem__') else datetime.utcnow(),
                    confidence=0.7  # Base confidence
                )
                levels.append(level)
        
        # Sort by strength and return top levels
        levels.sort(key=lambda x: x.strength, reverse=True)
        return levels[:self.level_params["max_levels_per_timeframe"]]

    def _calculate_level_strength(self, price: float, data: pd.DataFrame) -> float:
        """Calculate the strength of a support/resistance level."""
        
        touches = self._count_level_touches(price, data)
        volume_at_level = self._get_volume_at_level(price, data)
        
        # Base strength from number of touches
        strength = min(1.0, touches / 5.0)
        
        # Adjust for volume
        avg_volume = data["volume"].mean()
        if volume_at_level > avg_volume:
            strength *= 1.2
        
        return min(1.0, strength)

    def _count_level_touches(self, price: float, data: pd.DataFrame) -> int:
        """Count how many times price has touched a level."""
        
        tolerance = price * self.level_params["touch_tolerance"]
        
        touches = 0
        for _, row in data.iterrows():
            if abs(row["high"] - price) <= tolerance or abs(row["low"] - price) <= tolerance:
                touches += 1
        
        return touches

    def _get_volume_at_level(self, price: float, data: pd.DataFrame) -> float:
        """Get average volume when price was near a level."""
        
        tolerance = price * self.level_params["touch_tolerance"]
        volumes = []
        
        for _, row in data.iterrows():
            if (abs(row["high"] - price) <= tolerance or 
                abs(row["low"] - price) <= tolerance or
                (row["low"] <= price <= row["high"])):
                volumes.append(row["volume"])
        
        return np.mean(volumes) if volumes else 0.0

    async def _detect_momentum_divergence(self, data: pd.DataFrame, indicators: Dict) -> Optional[str]:
        """Detect bullish or bearish divergences between price and momentum indicators."""
        
        if len(data) < 20:
            return None
        
        close_prices = data["close"].values[-20:]  # Last 20 periods
        
        # Check RSI divergence
        if "rsi" in indicators and len(indicators["rsi"]) >= 20:
            rsi_values = indicators["rsi"][-20:]
            
            # Remove NaN values
            valid_indices = ~np.isnan(rsi_values)
            if np.sum(valid_indices) < 10:
                return None
            
            clean_prices = close_prices[valid_indices]
            clean_rsi = rsi_values[valid_indices]
            
            # Check for divergence patterns
            price_trend = np.polyfit(range(len(clean_prices)), clean_prices, 1)[0]
            rsi_trend = np.polyfit(range(len(clean_rsi)), clean_rsi, 1)[0]
            
            # Bullish divergence: price declining, RSI rising
            if price_trend < 0 and rsi_trend > 0 and abs(price_trend) > 0.001:
                return "bullish_divergence"
            
            # Bearish divergence: price rising, RSI declining
            if price_trend > 0 and rsi_trend < 0 and abs(price_trend) > 0.001:
                return "bearish_divergence"
        
        return None

    async def _cleanup_agent(self):
        """Cleanup technical analyst resources."""
        self.logger.info("🧹 Cleaning up Advanced Technical Analyst resources")
        
        # Clear data structures
        self.price_data.clear()
        self.technical_levels.clear()
        self.chart_patterns.clear()
        self.wave_counts.clear()
        self.fibonacci_levels.clear()
        self.indicator_values.clear()

    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages."""
        if message.message_type == "technical_analysis_request":
            await self._process_technical_analysis_request(message.content)
        elif message.message_type == "pattern_alert":
            await self._process_pattern_alert(message.content)
        elif message.message_type == "level_test":
            await self._process_level_test(message.content)

    async def _perform_periodic_analysis(self):
        """Perform periodic technical analysis."""
        while self.running:
            try:
                # Update technical levels
                await self._update_technical_levels()
                
                # Scan for new patterns
                await self._scan_for_patterns()
                
                # Generate technical reports
                await self._generate_technical_reports()
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Periodic technical analysis error: {e}")
                await asyncio.sleep(60)
