#!/usr/bin/env python3
"""
🔧 ADVANCED FEATURE ENGINEERING
Real feature engineering techniques that improve model performance
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.signal import find_peaks
from sklearn.preprocessing import PolynomialFeatures
from sklearn.decomposition import PCA, FastICA
from sklearn.cluster import KMeans
import talib
import logging
from typing import Dict, List, Tuple, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AdvancedFeatures")

class AdvancedFeatureEngineering:
    """Advanced feature engineering for trading systems"""
    
    def __init__(self):
        self.feature_names = []
        self.feature_importance = {}
        
    def technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Comprehensive technical indicators using TA-Lib"""
        
        features_df = df.copy()
        
        for symbol in features_df['symbol'].unique():
            mask = features_df['symbol'] == symbol
            symbol_data = features_df[mask].copy()
            
            if len(symbol_data) < 50:
                continue
            
            # Convert to numpy arrays for TA-Lib
            high = symbol_data['high_24h'].values if 'high_24h' in symbol_data.columns else symbol_data['price'].values
            low = symbol_data['low_24h'].values if 'low_24h' in symbol_data.columns else symbol_data['price'].values * 0.98
            close = symbol_data['price'].values
            volume = symbol_data['volume'].values
            
            try:
                # Trend Indicators
                symbol_data['sma_10'] = talib.SMA(close, timeperiod=10)
                symbol_data['sma_20'] = talib.SMA(close, timeperiod=20)
                symbol_data['sma_50'] = talib.SMA(close, timeperiod=50)
                symbol_data['ema_12'] = talib.EMA(close, timeperiod=12)
                symbol_data['ema_26'] = talib.EMA(close, timeperiod=26)
                
                # MACD
                macd, macd_signal, macd_hist = talib.MACD(close)
                symbol_data['macd'] = macd
                symbol_data['macd_signal'] = macd_signal
                symbol_data['macd_histogram'] = macd_hist
                
                # Bollinger Bands
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20)
                symbol_data['bb_upper'] = bb_upper
                symbol_data['bb_middle'] = bb_middle
                symbol_data['bb_lower'] = bb_lower
                symbol_data['bb_width'] = (bb_upper - bb_lower) / bb_middle
                symbol_data['bb_position'] = (close - bb_lower) / (bb_upper - bb_lower)
                
                # Momentum Indicators
                symbol_data['rsi'] = talib.RSI(close, timeperiod=14)
                symbol_data['stoch_k'], symbol_data['stoch_d'] = talib.STOCH(high, low, close)
                symbol_data['williams_r'] = talib.WILLR(high, low, close)
                symbol_data['cci'] = talib.CCI(high, low, close, timeperiod=14)
                symbol_data['roc'] = talib.ROC(close, timeperiod=10)
                symbol_data['momentum'] = talib.MOM(close, timeperiod=10)
                
                # Volume Indicators
                symbol_data['ad'] = talib.AD(high, low, close, volume)
                symbol_data['obv'] = talib.OBV(close, volume)
                symbol_data['ad_osc'] = talib.ADOSC(high, low, close, volume)
                
                # Volatility Indicators
                symbol_data['atr'] = talib.ATR(high, low, close, timeperiod=14)
                symbol_data['natr'] = talib.NATR(high, low, close, timeperiod=14)
                symbol_data['trange'] = talib.TRANGE(high, low, close)
                
                # Pattern Recognition (returns -100, 0, or 100)
                symbol_data['doji'] = talib.CDLDOJI(high, low, close, close)
                symbol_data['hammer'] = talib.CDLHAMMER(high, low, close, close)
                symbol_data['engulfing'] = talib.CDLENGULFING(high, low, close, close)
                symbol_data['harami'] = talib.CDLHARAMI(high, low, close, close)
                
            except Exception as e:
                logger.warning(f"TA-Lib error for {symbol}: {e}")
                # Fallback to manual calculations
                symbol_data = self._manual_technical_indicators(symbol_data)
            
            # Update main dataframe
            features_df.loc[mask] = symbol_data
        
        logger.info("✅ Technical indicators calculated")
        return features_df
    
    def _manual_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Manual calculation of technical indicators as fallback"""
        
        # Simple Moving Averages
        for period in [10, 20, 50]:
            df[f'sma_{period}'] = df['price'].rolling(period).mean()
        
        # Exponential Moving Averages
        df['ema_12'] = df['price'].ewm(span=12).mean()
        df['ema_26'] = df['price'].ewm(span=26).mean()
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['price'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['sma_20']
        bb_std = df['price'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['price'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        return df

    def statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Advanced statistical features"""
        
        features_df = df.copy()
        
        for symbol in features_df['symbol'].unique():
            mask = features_df['symbol'] == symbol
            symbol_data = features_df[mask].copy()
            
            if len(symbol_data) < 30:
                continue
            
            price = symbol_data['price']
            volume = symbol_data['volume']
            
            # Rolling statistical measures
            for window in [10, 20, 50]:
                # Price statistics
                symbol_data[f'price_mean_{window}'] = price.rolling(window).mean()
                symbol_data[f'price_std_{window}'] = price.rolling(window).std()
                symbol_data[f'price_skew_{window}'] = price.rolling(window).skew()
                symbol_data[f'price_kurt_{window}'] = price.rolling(window).kurt()
                symbol_data[f'price_min_{window}'] = price.rolling(window).min()
                symbol_data[f'price_max_{window}'] = price.rolling(window).max()
                symbol_data[f'price_range_{window}'] = symbol_data[f'price_max_{window}'] - symbol_data[f'price_min_{window}']
                
                # Z-scores
                symbol_data[f'price_zscore_{window}'] = (price - symbol_data[f'price_mean_{window}']) / symbol_data[f'price_std_{window}']
                
                # Percentiles
                symbol_data[f'price_pct25_{window}'] = price.rolling(window).quantile(0.25)
                symbol_data[f'price_pct75_{window}'] = price.rolling(window).quantile(0.75)
                
                # Volume statistics
                symbol_data[f'volume_mean_{window}'] = volume.rolling(window).mean()
                symbol_data[f'volume_std_{window}'] = volume.rolling(window).std()
                symbol_data[f'volume_zscore_{window}'] = (volume - symbol_data[f'volume_mean_{window}']) / symbol_data[f'volume_std_{window}']
            
            # Price change statistics
            returns = price.pct_change()
            for window in [5, 10, 20]:
                symbol_data[f'returns_mean_{window}'] = returns.rolling(window).mean()
                symbol_data[f'returns_std_{window}'] = returns.rolling(window).std()
                symbol_data[f'returns_skew_{window}'] = returns.rolling(window).skew()
                symbol_data[f'returns_kurt_{window}'] = returns.rolling(window).kurt()
            
            # Autocorrelation
            for lag in [1, 5, 10]:
                symbol_data[f'price_autocorr_{lag}'] = price.rolling(20).apply(lambda x: x.autocorr(lag=lag))
                symbol_data[f'returns_autocorr_{lag}'] = returns.rolling(20).apply(lambda x: x.autocorr(lag=lag))
            
            features_df.loc[mask] = symbol_data
        
        logger.info("✅ Statistical features calculated")
        return features_df

    def time_series_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Time series specific features"""
        
        features_df = df.copy()
        features_df['timestamp'] = pd.to_datetime(features_df['timestamp'])
        
        # Time-based features
        features_df['hour'] = features_df['timestamp'].dt.hour
        features_df['day_of_week'] = features_df['timestamp'].dt.dayofweek
        features_df['day_of_month'] = features_df['timestamp'].dt.day
        features_df['month'] = features_df['timestamp'].dt.month
        features_df['quarter'] = features_df['timestamp'].dt.quarter
        
        # Cyclical encoding
        features_df['hour_sin'] = np.sin(2 * np.pi * features_df['hour'] / 24)
        features_df['hour_cos'] = np.cos(2 * np.pi * features_df['hour'] / 24)
        features_df['day_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['day_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)
        features_df['month_sin'] = np.sin(2 * np.pi * features_df['month'] / 12)
        features_df['month_cos'] = np.cos(2 * np.pi * features_df['month'] / 12)
        
        for symbol in features_df['symbol'].unique():
            mask = features_df['symbol'] == symbol
            symbol_data = features_df[mask].copy()
            
            if len(symbol_data) < 20:
                continue
            
            # Lag features
            for lag in [1, 2, 3, 5, 10]:
                symbol_data[f'price_lag_{lag}'] = symbol_data['price'].shift(lag)
                symbol_data[f'volume_lag_{lag}'] = symbol_data['volume'].shift(lag)
                symbol_data[f'rsi_lag_{lag}'] = symbol_data['rsi'].shift(lag) if 'rsi' in symbol_data.columns else np.nan
            
            # Differencing
            symbol_data['price_diff_1'] = symbol_data['price'].diff(1)
            symbol_data['price_diff_2'] = symbol_data['price'].diff(2)
            symbol_data['volume_diff_1'] = symbol_data['volume'].diff(1)
            
            # Seasonal decomposition features
            if len(symbol_data) >= 50:
                # Trend (using moving average)
                symbol_data['trend'] = symbol_data['price'].rolling(20, center=True).mean()
                symbol_data['detrended'] = symbol_data['price'] - symbol_data['trend']
                
                # Seasonality (simplified)
                symbol_data['seasonal'] = symbol_data.groupby('hour')['price'].transform('mean')
                symbol_data['residual'] = symbol_data['price'] - symbol_data['trend'] - symbol_data['seasonal']
            
            features_df.loc[mask] = symbol_data
        
        logger.info("✅ Time series features calculated")
        return features_df

    def interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features between variables"""
        
        features_df = df.copy()
        
        # Define key features for interactions
        key_features = ['price', 'volume', 'rsi', 'macd', 'bb_position', 'atr']
        available_features = [f for f in key_features if f in features_df.columns]
        
        if len(available_features) < 2:
            logger.warning("Not enough features for interactions")
            return features_df
        
        # Pairwise interactions
        for i, feat1 in enumerate(available_features):
            for feat2 in available_features[i+1:]:
                # Multiplication
                features_df[f'{feat1}_x_{feat2}'] = features_df[feat1] * features_df[feat2]
                
                # Division (avoid division by zero)
                features_df[f'{feat1}_div_{feat2}'] = features_df[feat1] / (features_df[feat2] + 1e-8)
                
                # Difference
                features_df[f'{feat1}_minus_{feat2}'] = features_df[feat1] - features_df[feat2]
        
        # Polynomial features for key variables
        if 'price' in features_df.columns and 'volume' in features_df.columns:
            features_df['price_squared'] = features_df['price'] ** 2
            features_df['volume_squared'] = features_df['volume'] ** 2
            features_df['price_volume_product'] = features_df['price'] * features_df['volume']
        
        # Ratio features
        if 'rsi' in features_df.columns:
            features_df['rsi_normalized'] = (features_df['rsi'] - 50) / 50  # Center around 0
            features_df['rsi_extreme'] = np.where(
                (features_df['rsi'] < 30) | (features_df['rsi'] > 70), 1, 0
            )
        
        if 'bb_position' in features_df.columns:
            features_df['bb_extreme'] = np.where(
                (features_df['bb_position'] < 0.2) | (features_df['bb_position'] > 0.8), 1, 0
            )
        
        logger.info("✅ Interaction features calculated")
        return features_df

    def dimensionality_reduction_features(self, df: pd.DataFrame, n_components: int = 5) -> pd.DataFrame:
        """Create features using dimensionality reduction"""
        
        features_df = df.copy()
        
        # Select numeric columns for PCA
        numeric_cols = features_df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col not in ['timestamp']]
        
        if len(numeric_cols) < 5:
            logger.warning("Not enough numeric features for dimensionality reduction")
            return features_df
        
        # Prepare data
        X = features_df[numeric_cols].fillna(0)
        
        if X.shape[0] < 10:
            logger.warning("Not enough samples for dimensionality reduction")
            return features_df
        
        try:
            # PCA
            pca = PCA(n_components=min(n_components, len(numeric_cols)))
            pca_features = pca.fit_transform(X)
            
            for i in range(pca_features.shape[1]):
                features_df[f'pca_{i}'] = pca_features[:, i]
            
            # ICA
            ica = FastICA(n_components=min(n_components, len(numeric_cols)), random_state=42)
            ica_features = ica.fit_transform(X)
            
            for i in range(ica_features.shape[1]):
                features_df[f'ica_{i}'] = ica_features[:, i]
            
            logger.info(f"✅ Dimensionality reduction: {pca_features.shape[1]} PCA + {ica_features.shape[1]} ICA features")
            
        except Exception as e:
            logger.warning(f"Dimensionality reduction failed: {e}")
        
        return features_df

    def clustering_features(self, df: pd.DataFrame, n_clusters: int = 5) -> pd.DataFrame:
        """Create features using clustering"""
        
        features_df = df.copy()
        
        # Select features for clustering
        cluster_features = ['price', 'volume', 'rsi', 'macd', 'bb_position']
        available_cluster_features = [f for f in cluster_features if f in features_df.columns]
        
        if len(available_cluster_features) < 2:
            logger.warning("Not enough features for clustering")
            return features_df
        
        # Prepare data
        X = features_df[available_cluster_features].fillna(0)
        
        if X.shape[0] < n_clusters * 2:
            logger.warning("Not enough samples for clustering")
            return features_df
        
        try:
            # K-means clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(X)
            
            features_df['cluster'] = cluster_labels
            
            # Distance to cluster centers
            distances = kmeans.transform(X)
            for i in range(n_clusters):
                features_df[f'dist_to_cluster_{i}'] = distances[:, i]
            
            # Cluster-based features
            for cluster_id in range(n_clusters):
                mask = cluster_labels == cluster_id
                if mask.sum() > 0:
                    features_df[f'is_cluster_{cluster_id}'] = mask.astype(int)
            
            logger.info(f"✅ Clustering features: {n_clusters} clusters created")
            
        except Exception as e:
            logger.warning(f"Clustering failed: {e}")
        
        return features_df

    def create_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create all advanced features"""
        
        logger.info("🔧 Starting advanced feature engineering")
        
        # Start with technical indicators
        features_df = self.technical_indicators(df)
        
        # Add statistical features
        features_df = self.statistical_features(features_df)
        
        # Add time series features
        features_df = self.time_series_features(features_df)
        
        # Add interaction features
        features_df = self.interaction_features(features_df)
        
        # Add dimensionality reduction features
        features_df = self.dimensionality_reduction_features(features_df)
        
        # Add clustering features
        features_df = self.clustering_features(features_df)
        
        # Remove rows with too many NaN values
        initial_rows = len(features_df)
        features_df = features_df.dropna(thresh=len(features_df.columns) * 0.5)
        final_rows = len(features_df)
        
        logger.info(f"✅ Advanced feature engineering completed")
        logger.info(f"   Features created: {features_df.shape[1]}")
        logger.info(f"   Samples: {initial_rows} → {final_rows}")
        
        return features_df

def main():
    """Demonstration of advanced feature engineering"""
    print("🔧 ADVANCED FEATURE ENGINEERING DEMO")
    print("=" * 50)
    
    # Create sample data
    np.random.seed(42)
    n_samples = 200
    
    # Generate sample trading data
    timestamps = pd.date_range('2024-01-01', periods=n_samples, freq='1H')
    symbols = ['BTCUSD', 'ETHUSD']
    
    data = []
    for symbol in symbols:
        base_price = 45000 if symbol == 'BTCUSD' else 2500
        price = base_price
        
        for i, timestamp in enumerate(timestamps):
            # Random walk with trend
            change = np.random.normal(0.001, 0.02)
            price = price * (1 + change)
            
            data.append({
                'timestamp': timestamp,
                'symbol': symbol,
                'price': price,
                'volume': np.random.uniform(1000000, 10000000),
                'high_24h': price * 1.02,
                'low_24h': price * 0.98,
                'change_24h': change * 100
            })
    
    df = pd.DataFrame(data)
    print(f"📊 Sample data: {df.shape}")
    
    # Apply advanced feature engineering
    feature_engineer = AdvancedFeatureEngineering()
    enhanced_df = feature_engineer.create_all_features(df)
    
    print(f"\n📊 FEATURE ENGINEERING RESULTS:")
    print(f"Original features: {df.shape[1]}")
    print(f"Enhanced features: {enhanced_df.shape[1]}")
    print(f"Features added: {enhanced_df.shape[1] - df.shape[1]}")
    print(f"Final samples: {enhanced_df.shape[0]}")
    
    # Show some feature examples
    feature_types = {
        'Technical': [col for col in enhanced_df.columns if any(x in col for x in ['sma', 'ema', 'rsi', 'macd', 'bb'])],
        'Statistical': [col for col in enhanced_df.columns if any(x in col for x in ['mean', 'std', 'skew', 'kurt', 'zscore'])],
        'Time Series': [col for col in enhanced_df.columns if any(x in col for x in ['lag', 'diff', 'hour', 'day'])],
        'Interaction': [col for col in enhanced_df.columns if any(x in col for x in ['_x_', '_div_', '_minus_'])],
        'Dimensionality': [col for col in enhanced_df.columns if any(x in col for x in ['pca', 'ica'])],
        'Clustering': [col for col in enhanced_df.columns if any(x in col for x in ['cluster', 'dist_to'])]
    }
    
    print(f"\n🎯 FEATURE BREAKDOWN:")
    for feature_type, features in feature_types.items():
        print(f"  {feature_type:15}: {len(features):3d} features")
    
    print(f"\n✅ Advanced feature engineering demonstration completed!")

if __name__ == "__main__":
    main()
