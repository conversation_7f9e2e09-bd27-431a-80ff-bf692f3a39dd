#!/usr/bin/env python3
"""
Advanced Machine Learning Engine for AI Trading System
Internal ML models using only numpy/pandas - no external APIs
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Tuple
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger("AdvancedMLEngine")


class AdvancedMLEngine:
    """Advanced Machine Learning Engine with multiple models."""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.model_performance = {}
        self.prediction_history = []
        
        # Initialize models
        self._initialize_models()
        
    def _initialize_models(self):
        """Initialize all ML models."""
        self.models = {
            # Classification models for signal prediction
            "signal_classifier": RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            "trend_classifier": GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            ),
            "volatility_predictor": RandomForestClassifier(
                n_estimators=50,
                max_depth=8,
                random_state=42
            ),
            
            # Regression models for price prediction
            "price_predictor": GradientBoostingRegressor(
                n_estimators=150,
                learning_rate=0.05,
                max_depth=8,
                random_state=42
            ),
            "return_predictor": LinearRegression(),
            
            # Risk models
            "risk_classifier": LogisticRegression(
                random_state=42,
                max_iter=1000
            )
        }
        
        # Initialize scalers for each model
        for model_name in self.models.keys():
            self.scalers[model_name] = StandardScaler()
        
        logger.info(f"✅ Initialized {len(self.models)} ML models")
    
    def generate_features(self, market_data: Dict[str, Any]) -> np.ndarray:
        """Generate comprehensive feature set from market data."""
        features = []
        
        try:
            # Price-based features
            if 'price_history' in market_data:
                prices = np.array(market_data['price_history'][-50:])  # Last 50 prices
                if len(prices) >= 20:
                    # Technical indicators as features
                    features.extend([
                        np.mean(prices[-5:]) / np.mean(prices[-20:]) - 1,  # Short/Long MA ratio
                        np.std(prices[-10:]) / np.mean(prices[-10:]),      # Volatility ratio
                        (prices[-1] - np.min(prices[-14:])) / (np.max(prices[-14:]) - np.min(prices[-14:])),  # Stochastic
                        np.mean(prices[-3:]) / prices[-1] - 1,             # Recent momentum
                        (prices[-1] - prices[-5]) / prices[-5],           # 5-period return
                        (prices[-1] - prices[-10]) / prices[-10],         # 10-period return
                    ])
                else:
                    features.extend([0.0] * 6)  # Default values
            else:
                features.extend([0.0] * 6)
            
            # Volume-based features (simulated)
            volume_ratio = np.random.uniform(0.8, 1.2)  # Simulated volume ratio
            features.extend([
                volume_ratio,
                np.log(volume_ratio + 1),  # Log volume ratio
            ])
            
            # Market structure features
            if 'rsi' in market_data:
                rsi = market_data['rsi']
                features.extend([
                    rsi / 100.0,  # Normalized RSI
                    1.0 if rsi > 70 else (-1.0 if rsi < 30 else 0.0),  # RSI signal
                ])
            else:
                features.extend([0.5, 0.0])
            
            # Volatility features
            if 'volatility' in market_data:
                vol = market_data['volatility']
                features.extend([
                    vol,
                    np.log(vol + 1e-8),  # Log volatility
                    1.0 if vol > 0.02 else 0.0,  # High volatility flag
                ])
            else:
                features.extend([0.01, -4.6, 0.0])
            
            # Time-based features
            now = datetime.now(timezone.utc)
            features.extend([
                now.hour / 24.0,  # Hour of day
                now.weekday() / 6.0,  # Day of week
                np.sin(2 * np.pi * now.hour / 24),  # Cyclical hour
                np.cos(2 * np.pi * now.hour / 24),  # Cyclical hour
            ])
            
            # Cross-asset features (simulated correlations)
            features.extend([
                np.random.uniform(-0.5, 0.5),  # Simulated BTC correlation
                np.random.uniform(-0.3, 0.3),  # Simulated ETH correlation
                np.random.uniform(-0.2, 0.2),  # Simulated market correlation
            ])
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            logger.error(f"Feature generation error: {e}")
            # Return default feature vector
            return np.zeros((1, 20))
    
    def train_models(self, historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Train all ML models on historical data."""
        if len(historical_data) < 50:
            logger.warning("Insufficient data for training, using synthetic data")
            historical_data = self._generate_synthetic_training_data(1000)
        
        training_results = {}
        
        try:
            # Prepare training data
            X_features = []
            y_signals = []
            y_returns = []
            y_volatility = []
            y_risk = []
            
            for i, data_point in enumerate(historical_data[:-1]):
                features = self.generate_features(data_point).flatten()
                X_features.append(features)
                
                # Generate labels based on next period
                next_data = historical_data[i + 1]
                current_price = data_point.get('price', 100)
                next_price = next_data.get('price', 100)
                
                # Signal labels (1: buy, 0: hold, -1: sell)
                price_change = (next_price - current_price) / current_price
                if price_change > 0.02:
                    signal = 1  # Buy signal
                elif price_change < -0.02:
                    signal = 2  # Sell signal
                else:
                    signal = 0  # Hold signal
                
                y_signals.append(signal)
                y_returns.append(price_change)
                y_volatility.append(1 if abs(price_change) > 0.03 else 0)
                y_risk.append(1 if abs(price_change) > 0.05 else 0)
            
            X = np.array(X_features)
            
            # Train each model
            for model_name, model in self.models.items():
                try:
                    if model_name == "signal_classifier":
                        y = np.array(y_signals)
                    elif model_name == "volatility_predictor":
                        y = np.array(y_volatility)
                    elif model_name == "risk_classifier":
                        y = np.array(y_risk)
                    elif model_name in ["price_predictor", "trend_classifier"]:
                        y = np.array(y_returns)
                    else:
                        y = np.array(y_returns)
                    
                    # Scale features
                    X_scaled = self.scalers[model_name].fit_transform(X)
                    
                    # Split data
                    X_train, X_test, y_train, y_test = train_test_split(
                        X_scaled, y, test_size=0.2, random_state=42
                    )
                    
                    # Train model
                    model.fit(X_train, y_train)
                    
                    # Evaluate model
                    y_pred = model.predict(X_test)
                    
                    if hasattr(model, 'predict_proba'):  # Classification
                        accuracy = accuracy_score(y_test, y_pred)
                        training_results[model_name] = {
                            "accuracy": round(accuracy, 4),
                            "samples_trained": len(X_train),
                            "features": X.shape[1]
                        }
                    else:  # Regression
                        mse = np.mean((y_test - y_pred) ** 2)
                        r2 = 1 - (np.sum((y_test - y_pred) ** 2) / np.sum((y_test - np.mean(y_test)) ** 2))
                        training_results[model_name] = {
                            "mse": round(mse, 6),
                            "r2_score": round(r2, 4),
                            "samples_trained": len(X_train),
                            "features": X.shape[1]
                        }
                    
                    # Store feature importance if available
                    if hasattr(model, 'feature_importances_'):
                        self.feature_importance[model_name] = model.feature_importances_
                    
                    logger.info(f"✅ Trained {model_name}: {training_results[model_name]}")
                    
                except Exception as e:
                    logger.error(f"Training error for {model_name}: {e}")
                    training_results[model_name] = {"error": str(e)}
            
            self.model_performance = training_results
            return training_results
            
        except Exception as e:
            logger.error(f"Model training error: {e}")
            return {"error": str(e)}
    
    def _generate_synthetic_training_data(self, n_samples: int) -> List[Dict[str, Any]]:
        """Generate synthetic training data for model development."""
        synthetic_data = []
        
        # Generate realistic price series with trends and volatility
        np.random.seed(42)
        base_price = 100.0
        prices = [base_price]
        
        for i in range(n_samples):
            # Add trend and noise
            trend = 0.0001 * np.sin(i / 100)  # Cyclical trend
            noise = np.random.normal(0, 0.02)  # Random noise
            price_change = trend + noise
            
            new_price = prices[-1] * (1 + price_change)
            prices.append(new_price)
            
            # Calculate technical indicators
            if len(prices) >= 20:
                sma_10 = np.mean(prices[-10:])
                sma_20 = np.mean(prices[-20:])
                rsi = 50 + 30 * np.sin(i / 50)  # Oscillating RSI
                volatility = np.std(prices[-20:]) / np.mean(prices[-20:])
            else:
                sma_10 = new_price
                sma_20 = new_price
                rsi = 50
                volatility = 0.02
            
            data_point = {
                'price': new_price,
                'price_history': prices[-50:],  # Last 50 prices
                'sma_10': sma_10,
                'sma_20': sma_20,
                'rsi': max(0, min(100, rsi)),
                'volatility': volatility,
                'volume': np.random.uniform(1000000, 10000000),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            synthetic_data.append(data_point)
        
        logger.info(f"✅ Generated {len(synthetic_data)} synthetic training samples")
        return synthetic_data
    
    def predict_signals(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate ML-based trading signals."""
        try:
            features = self.generate_features(market_data)
            predictions = {}
            
            for model_name, model in self.models.items():
                try:
                    # Scale features
                    features_scaled = self.scalers[model_name].transform(features)
                    
                    # Make prediction
                    prediction = model.predict(features_scaled)[0]
                    
                    # Get confidence if available
                    confidence = 0.5
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features_scaled)[0]
                        confidence = np.max(proba)
                    elif hasattr(model, 'decision_function'):
                        decision = model.decision_function(features_scaled)[0]
                        confidence = 1 / (1 + np.exp(-abs(decision)))  # Sigmoid
                    
                    predictions[model_name] = {
                        "prediction": prediction,
                        "confidence": round(confidence, 4)
                    }
                    
                except Exception as e:
                    logger.error(f"Prediction error for {model_name}: {e}")
                    predictions[model_name] = {"error": str(e)}
            
            # Generate ensemble prediction
            ensemble_signal = self._generate_ensemble_signal(predictions)
            
            result = {
                "individual_predictions": predictions,
                "ensemble_signal": ensemble_signal,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.prediction_history.append(result)
            if len(self.prediction_history) > 1000:
                self.prediction_history = self.prediction_history[-1000:]
            
            return result
            
        except Exception as e:
            logger.error(f"Signal prediction error: {e}")
            return {"error": str(e)}
    
    def _generate_ensemble_signal(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Generate ensemble signal from individual model predictions."""
        try:
            # Extract valid predictions
            valid_predictions = {}
            for model_name, pred_data in predictions.items():
                if "error" not in pred_data:
                    valid_predictions[model_name] = pred_data
            
            if not valid_predictions:
                return {"signal": "HOLD", "confidence": 0.0, "reason": "No valid predictions"}
            
            # Weighted ensemble based on model performance
            signal_votes = {"BUY": 0, "SELL": 0, "HOLD": 0}
            total_confidence = 0
            
            for model_name, pred_data in valid_predictions.items():
                prediction = pred_data["prediction"]
                confidence = pred_data["confidence"]
                
                # Convert prediction to signal
                if model_name == "signal_classifier":
                    if prediction == 1:
                        signal_votes["BUY"] += confidence
                    elif prediction == 2:
                        signal_votes["SELL"] += confidence
                    else:
                        signal_votes["HOLD"] += confidence
                elif model_name in ["price_predictor", "trend_classifier", "return_predictor"]:
                    if prediction > 0.01:
                        signal_votes["BUY"] += confidence * 0.5
                    elif prediction < -0.01:
                        signal_votes["SELL"] += confidence * 0.5
                    else:
                        signal_votes["HOLD"] += confidence * 0.5
                
                total_confidence += confidence
            
            # Determine final signal
            if total_confidence > 0:
                final_signal = max(signal_votes, key=signal_votes.get)
                final_confidence = signal_votes[final_signal] / total_confidence
            else:
                final_signal = "HOLD"
                final_confidence = 0.0
            
            return {
                "signal": final_signal,
                "confidence": round(final_confidence, 4),
                "vote_distribution": {k: round(v, 4) for k, v in signal_votes.items()},
                "models_used": len(valid_predictions)
            }
            
        except Exception as e:
            logger.error(f"Ensemble signal generation error: {e}")
            return {"signal": "HOLD", "confidence": 0.0, "error": str(e)}
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get comprehensive model status."""
        return {
            "models_initialized": len(self.models),
            "models_trained": len([m for m in self.model_performance.values() if "error" not in m]),
            "model_performance": self.model_performance,
            "prediction_history_length": len(self.prediction_history),
            "feature_importance_available": len(self.feature_importance),
            "last_prediction": self.prediction_history[-1] if self.prediction_history else None
        }


# Test the ML Engine
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🧠 Testing Advanced ML Engine")
    print("=" * 50)
    
    # Initialize engine
    ml_engine = AdvancedMLEngine()
    
    # Train models
    print("📚 Training models...")
    training_results = ml_engine.train_models([])  # Will use synthetic data
    
    print("\n📊 Training Results:")
    for model_name, results in training_results.items():
        print(f"  {model_name}: {results}")
    
    # Test predictions
    print("\n🎯 Testing predictions...")
    test_market_data = {
        'price': 45000,
        'price_history': [44000 + i * 20 for i in range(50)],
        'rsi': 65,
        'volatility': 0.025,
        'sma_10': 44500,
        'sma_20': 44000
    }
    
    predictions = ml_engine.predict_signals(test_market_data)
    print(f"Predictions: {predictions}")
    
    # Get status
    print("\n📈 Model Status:")
    status = ml_engine.get_model_status()
    for key, value in status.items():
        if key != "last_prediction":
            print(f"  {key}: {value}")
    
    print("\n✅ ML Engine test completed!")
