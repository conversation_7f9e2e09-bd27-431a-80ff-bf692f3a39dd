#!/usr/bin/env python3
"""
Advanced Technical Analysis Suite
Professional-grade technical indicators and pattern recognition
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Tuple, Optional
from scipy import stats
from scipy.signal import find_peaks, argrelextrema
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger("AdvancedTechnicalAnalysis")


class AdvancedTechnicalAnalysis:
    """Advanced technical analysis with professional indicators."""
    
    def __init__(self):
        self.indicators = {}
        self.patterns = {}
        self.support_resistance_levels = {}
        
    def calculate_all_indicators(self, price_data: List[float], volume_data: Optional[List[float]] = None) -> Dict[str, Any]:
        """Calculate comprehensive set of technical indicators."""
        if len(price_data) < 20:
            logger.warning("Insufficient data for technical analysis")
            return self._get_default_indicators()
        
        prices = np.array(price_data)
        volumes = np.array(volume_data) if volume_data else np.random.uniform(1000000, 10000000, len(prices))
        
        indicators = {}
        
        try:
            # Moving Averages
            indicators.update(self._calculate_moving_averages(prices))
            
            # Momentum Oscillators
            indicators.update(self._calculate_momentum_oscillators(prices))
            
            # Volatility Indicators
            indicators.update(self._calculate_volatility_indicators(prices))
            
            # Volume Indicators
            indicators.update(self._calculate_volume_indicators(prices, volumes))
            
            # Trend Indicators
            indicators.update(self._calculate_trend_indicators(prices))
            
            # Support/Resistance
            indicators.update(self._calculate_support_resistance(prices))
            
            # Pattern Recognition
            indicators.update(self._detect_patterns(prices))
            
            # Market Structure
            indicators.update(self._analyze_market_structure(prices))
            
            logger.info(f"✅ Calculated {len(indicators)} technical indicators")
            return indicators
            
        except Exception as e:
            logger.error(f"Technical analysis error: {e}")
            return self._get_default_indicators()
    
    def _calculate_moving_averages(self, prices: np.ndarray) -> Dict[str, Any]:
        """Calculate various moving averages."""
        mas = {}
        
        try:
            # Simple Moving Averages
            for period in [5, 10, 20, 50, 100, 200]:
                if len(prices) >= period:
                    ma = np.mean(prices[-period:])
                    mas[f"sma_{period}"] = round(ma, 4)
                    
                    # MA slope (trend strength)
                    if len(prices) >= period + 5:
                        ma_prev = np.mean(prices[-(period+5):-5])
                        mas[f"sma_{period}_slope"] = round((ma - ma_prev) / ma_prev, 6)
            
            # Exponential Moving Averages
            for period in [12, 26, 50]:
                if len(prices) >= period:
                    ema = self._calculate_ema(prices, period)
                    mas[f"ema_{period}"] = round(ema, 4)
            
            # Weighted Moving Average
            if len(prices) >= 20:
                weights = np.arange(1, 21)
                wma = np.average(prices[-20:], weights=weights)
                mas["wma_20"] = round(wma, 4)
            
            # Hull Moving Average
            if len(prices) >= 20:
                hma = self._calculate_hull_ma(prices, 20)
                mas["hma_20"] = round(hma, 4)
            
            # Moving Average Convergence
            if len(prices) >= 50:
                ma_short = np.mean(prices[-10:])
                ma_long = np.mean(prices[-50:])
                mas["ma_convergence"] = round((ma_short - ma_long) / ma_long, 6)
            
        except Exception as e:
            logger.error(f"Moving averages calculation error: {e}")
        
        return mas
    
    def _calculate_momentum_oscillators(self, prices: np.ndarray) -> Dict[str, Any]:
        """Calculate momentum oscillators."""
        oscillators = {}
        
        try:
            # RSI (Relative Strength Index)
            if len(prices) >= 14:
                rsi = self._calculate_rsi(prices, 14)
                oscillators["rsi_14"] = round(rsi, 2)
                oscillators["rsi_signal"] = "overbought" if rsi > 70 else ("oversold" if rsi < 30 else "neutral")
            
            # Stochastic Oscillator
            if len(prices) >= 14:
                stoch_k, stoch_d = self._calculate_stochastic(prices, 14, 3)
                oscillators["stoch_k"] = round(stoch_k, 2)
                oscillators["stoch_d"] = round(stoch_d, 2)
                oscillators["stoch_signal"] = "overbought" if stoch_k > 80 else ("oversold" if stoch_k < 20 else "neutral")
            
            # Williams %R
            if len(prices) >= 14:
                williams_r = self._calculate_williams_r(prices, 14)
                oscillators["williams_r"] = round(williams_r, 2)
            
            # Rate of Change (ROC)
            for period in [10, 20]:
                if len(prices) >= period + 1:
                    roc = (prices[-1] - prices[-(period+1)]) / prices[-(period+1)] * 100
                    oscillators[f"roc_{period}"] = round(roc, 4)
            
            # Commodity Channel Index (CCI)
            if len(prices) >= 20:
                cci = self._calculate_cci(prices, 20)
                oscillators["cci_20"] = round(cci, 2)
            
            # Money Flow Index (simulated)
            if len(prices) >= 14:
                mfi = self._calculate_mfi_simulated(prices, 14)
                oscillators["mfi_14"] = round(mfi, 2)
            
        except Exception as e:
            logger.error(f"Momentum oscillators calculation error: {e}")
        
        return oscillators
    
    def _calculate_volatility_indicators(self, prices: np.ndarray) -> Dict[str, Any]:
        """Calculate volatility indicators."""
        volatility = {}
        
        try:
            # Bollinger Bands
            if len(prices) >= 20:
                bb_upper, bb_middle, bb_lower, bb_width = self._calculate_bollinger_bands(prices, 20, 2)
                volatility["bb_upper"] = round(bb_upper, 4)
                volatility["bb_middle"] = round(bb_middle, 4)
                volatility["bb_lower"] = round(bb_lower, 4)
                volatility["bb_width"] = round(bb_width, 6)
                volatility["bb_position"] = round((prices[-1] - bb_lower) / (bb_upper - bb_lower), 4)
            
            # Average True Range (ATR)
            if len(prices) >= 14:
                atr = self._calculate_atr(prices, 14)
                volatility["atr_14"] = round(atr, 4)
                volatility["atr_percent"] = round(atr / prices[-1] * 100, 2)
            
            # Keltner Channels
            if len(prices) >= 20:
                kc_upper, kc_middle, kc_lower = self._calculate_keltner_channels(prices, 20, 2)
                volatility["kc_upper"] = round(kc_upper, 4)
                volatility["kc_middle"] = round(kc_middle, 4)
                volatility["kc_lower"] = round(kc_lower, 4)
            
            # Volatility Ratio
            if len(prices) >= 30:
                vol_short = np.std(prices[-10:])
                vol_long = np.std(prices[-30:])
                volatility["volatility_ratio"] = round(vol_short / vol_long, 4)
            
            # Historical Volatility
            if len(prices) >= 20:
                returns = np.diff(np.log(prices[-20:]))
                hist_vol = np.std(returns) * np.sqrt(252)  # Annualized
                volatility["historical_volatility"] = round(hist_vol, 4)
            
        except Exception as e:
            logger.error(f"Volatility indicators calculation error: {e}")
        
        return volatility
    
    def _calculate_volume_indicators(self, prices: np.ndarray, volumes: np.ndarray) -> Dict[str, Any]:
        """Calculate volume-based indicators."""
        volume_indicators = {}
        
        try:
            # Volume Moving Average
            if len(volumes) >= 20:
                vol_ma = np.mean(volumes[-20:])
                volume_indicators["volume_ma_20"] = round(vol_ma, 0)
                volume_indicators["volume_ratio"] = round(volumes[-1] / vol_ma, 2)
            
            # On-Balance Volume (OBV)
            if len(prices) >= 20:
                obv = self._calculate_obv(prices, volumes)
                volume_indicators["obv"] = round(obv, 0)
            
            # Volume Price Trend (VPT)
            if len(prices) >= 10:
                vpt = self._calculate_vpt(prices, volumes)
                volume_indicators["vpt"] = round(vpt, 0)
            
            # Accumulation/Distribution Line
            if len(prices) >= 10:
                ad_line = self._calculate_ad_line(prices, volumes)
                volume_indicators["ad_line"] = round(ad_line, 0)
            
            # Volume Oscillator
            if len(volumes) >= 28:
                vol_osc = self._calculate_volume_oscillator(volumes, 14, 28)
                volume_indicators["volume_oscillator"] = round(vol_osc, 2)
            
        except Exception as e:
            logger.error(f"Volume indicators calculation error: {e}")
        
        return volume_indicators
    
    def _calculate_trend_indicators(self, prices: np.ndarray) -> Dict[str, Any]:
        """Calculate trend indicators."""
        trend = {}
        
        try:
            # MACD
            if len(prices) >= 26:
                macd_line, signal_line, histogram = self._calculate_macd(prices)
                trend["macd_line"] = round(macd_line, 4)
                trend["macd_signal"] = round(signal_line, 4)
                trend["macd_histogram"] = round(histogram, 4)
                trend["macd_signal_type"] = "bullish" if macd_line > signal_line else "bearish"
            
            # Parabolic SAR
            if len(prices) >= 20:
                sar = self._calculate_parabolic_sar(prices)
                trend["parabolic_sar"] = round(sar, 4)
                trend["sar_signal"] = "bullish" if prices[-1] > sar else "bearish"
            
            # ADX (Average Directional Index)
            if len(prices) >= 14:
                adx, di_plus, di_minus = self._calculate_adx(prices, 14)
                trend["adx"] = round(adx, 2)
                trend["di_plus"] = round(di_plus, 2)
                trend["di_minus"] = round(di_minus, 2)
                trend["trend_strength"] = "strong" if adx > 25 else ("weak" if adx < 20 else "moderate")
            
            # Aroon Indicator
            if len(prices) >= 25:
                aroon_up, aroon_down = self._calculate_aroon(prices, 25)
                trend["aroon_up"] = round(aroon_up, 2)
                trend["aroon_down"] = round(aroon_down, 2)
                trend["aroon_signal"] = "bullish" if aroon_up > aroon_down else "bearish"
            
        except Exception as e:
            logger.error(f"Trend indicators calculation error: {e}")
        
        return trend
    
    def _calculate_support_resistance(self, prices: np.ndarray) -> Dict[str, Any]:
        """Calculate support and resistance levels."""
        levels = {}
        
        try:
            if len(prices) >= 50:
                # Find local maxima and minima
                highs = argrelextrema(prices, np.greater, order=5)[0]
                lows = argrelextrema(prices, np.less, order=5)[0]
                
                if len(highs) > 0:
                    resistance_levels = prices[highs]
                    # Get recent resistance levels
                    recent_resistance = resistance_levels[highs > len(prices) - 30] if len(highs) > 0 else []
                    if len(recent_resistance) > 0:
                        levels["resistance_1"] = round(np.max(recent_resistance), 4)
                        levels["resistance_2"] = round(np.median(recent_resistance), 4) if len(recent_resistance) > 1 else levels["resistance_1"]
                
                if len(lows) > 0:
                    support_levels = prices[lows]
                    # Get recent support levels
                    recent_support = support_levels[lows > len(prices) - 30] if len(lows) > 0 else []
                    if len(recent_support) > 0:
                        levels["support_1"] = round(np.min(recent_support), 4)
                        levels["support_2"] = round(np.median(recent_support), 4) if len(recent_support) > 1 else levels["support_1"]
                
                # Pivot Points
                if len(prices) >= 3:
                    high = np.max(prices[-20:])
                    low = np.min(prices[-20:])
                    close = prices[-1]
                    
                    pivot = (high + low + close) / 3
                    levels["pivot_point"] = round(pivot, 4)
                    levels["r1"] = round(2 * pivot - low, 4)
                    levels["s1"] = round(2 * pivot - high, 4)
                    levels["r2"] = round(pivot + (high - low), 4)
                    levels["s2"] = round(pivot - (high - low), 4)
            
        except Exception as e:
            logger.error(f"Support/resistance calculation error: {e}")
        
        return levels
    
    def _detect_patterns(self, prices: np.ndarray) -> Dict[str, Any]:
        """Detect chart patterns."""
        patterns = {}
        
        try:
            if len(prices) >= 20:
                # Double Top/Bottom detection
                patterns.update(self._detect_double_patterns(prices))
                
                # Head and Shoulders detection
                patterns.update(self._detect_head_shoulders(prices))
                
                # Triangle patterns
                patterns.update(self._detect_triangles(prices))
                
                # Flag and Pennant patterns
                patterns.update(self._detect_flags_pennants(prices))
                
                # Candlestick patterns (simplified)
                patterns.update(self._detect_candlestick_patterns(prices))
            
        except Exception as e:
            logger.error(f"Pattern detection error: {e}")
        
        return patterns
    
    def _analyze_market_structure(self, prices: np.ndarray) -> Dict[str, Any]:
        """Analyze market structure."""
        structure = {}
        
        try:
            if len(prices) >= 50:
                # Trend analysis
                short_trend = self._calculate_trend_slope(prices[-10:])
                medium_trend = self._calculate_trend_slope(prices[-20:])
                long_trend = self._calculate_trend_slope(prices[-50:])
                
                structure["short_trend"] = round(short_trend, 6)
                structure["medium_trend"] = round(medium_trend, 6)
                structure["long_trend"] = round(long_trend, 6)
                
                # Market phase
                if short_trend > 0 and medium_trend > 0:
                    structure["market_phase"] = "uptrend"
                elif short_trend < 0 and medium_trend < 0:
                    structure["market_phase"] = "downtrend"
                else:
                    structure["market_phase"] = "sideways"
                
                # Volatility regime
                recent_vol = np.std(prices[-20:]) / np.mean(prices[-20:])
                historical_vol = np.std(prices[-50:]) / np.mean(prices[-50:])
                
                structure["volatility_regime"] = "high" if recent_vol > historical_vol * 1.5 else ("low" if recent_vol < historical_vol * 0.7 else "normal")
                
                # Price momentum
                momentum_5 = (prices[-1] - prices[-6]) / prices[-6] if len(prices) >= 6 else 0
                momentum_10 = (prices[-1] - prices[-11]) / prices[-11] if len(prices) >= 11 else 0
                
                structure["momentum_5d"] = round(momentum_5 * 100, 2)
                structure["momentum_10d"] = round(momentum_10 * 100, 2)
            
        except Exception as e:
            logger.error(f"Market structure analysis error: {e}")
        
        return structure
    
    # Helper methods for calculations
    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """Calculate Exponential Moving Average."""
        alpha = 2 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return ema
    
    def _calculate_rsi(self, prices: np.ndarray, period: int) -> float:
        """Calculate RSI."""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_stochastic(self, prices: np.ndarray, k_period: int, d_period: int) -> Tuple[float, float]:
        """Calculate Stochastic Oscillator."""
        recent_prices = prices[-k_period:]
        lowest_low = np.min(recent_prices)
        highest_high = np.max(recent_prices)
        
        if highest_high == lowest_low:
            k_percent = 50
        else:
            k_percent = ((prices[-1] - lowest_low) / (highest_high - lowest_low)) * 100
        
        # Simplified %D calculation
        d_percent = k_percent  # In practice, this would be a moving average of %K
        
        return k_percent, d_percent
    
    def _get_default_indicators(self) -> Dict[str, Any]:
        """Return default indicators when calculation fails."""
        return {
            "sma_20": 0.0,
            "rsi_14": 50.0,
            "bb_upper": 0.0,
            "bb_lower": 0.0,
            "macd_line": 0.0,
            "error": "Insufficient data for technical analysis"
        }
    
    # Additional helper methods would be implemented here...
    def _calculate_williams_r(self, prices: np.ndarray, period: int) -> float:
        """Calculate Williams %R."""
        recent_prices = prices[-period:]
        highest_high = np.max(recent_prices)
        lowest_low = np.min(recent_prices)
        
        if highest_high == lowest_low:
            return -50
        
        williams_r = ((highest_high - prices[-1]) / (highest_high - lowest_low)) * -100
        return williams_r
    
    def _calculate_cci(self, prices: np.ndarray, period: int) -> float:
        """Calculate Commodity Channel Index."""
        typical_prices = prices  # Simplified - normally (H+L+C)/3
        sma = np.mean(typical_prices[-period:])
        mean_deviation = np.mean(np.abs(typical_prices[-period:] - sma))
        
        if mean_deviation == 0:
            return 0
        
        cci = (typical_prices[-1] - sma) / (0.015 * mean_deviation)
        return cci
    
    def _calculate_mfi_simulated(self, prices: np.ndarray, period: int) -> float:
        """Calculate simulated Money Flow Index."""
        # Simplified MFI calculation
        price_changes = np.diff(prices[-period:])
        positive_flow = np.sum(price_changes[price_changes > 0])
        negative_flow = np.sum(np.abs(price_changes[price_changes < 0]))
        
        if negative_flow == 0:
            return 100
        
        money_ratio = positive_flow / negative_flow
        mfi = 100 - (100 / (1 + money_ratio))
        return mfi
    
    def _calculate_trend_slope(self, prices: np.ndarray) -> float:
        """Calculate trend slope using linear regression."""
        if len(prices) < 2:
            return 0
        
        x = np.arange(len(prices))
        slope, _, _, _, _ = stats.linregress(x, prices)
        return slope / np.mean(prices)  # Normalized slope


# Test the Technical Analysis Suite
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("📊 Testing Advanced Technical Analysis Suite")
    print("=" * 60)
    
    # Generate test data
    np.random.seed(42)
    base_price = 45000
    prices = [base_price]
    
    for i in range(100):
        change = np.random.normal(0, 0.02)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Initialize analyzer
    ta = AdvancedTechnicalAnalysis()
    
    # Calculate indicators
    print("🔍 Calculating technical indicators...")
    indicators = ta.calculate_all_indicators(prices)
    
    print(f"\n📈 Calculated {len(indicators)} indicators:")
    for category in ["Moving Averages", "Momentum", "Volatility", "Trend", "Support/Resistance"]:
        print(f"\n{category}:")
        count = 0
        for key, value in indicators.items():
            if count < 5:  # Show first 5 of each category
                print(f"  {key}: {value}")
                count += 1
    
    print("\n✅ Technical Analysis test completed!")
